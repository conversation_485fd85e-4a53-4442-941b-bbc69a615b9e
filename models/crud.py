"""
CRUD operations for gaming entities
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from datetime import datetime, timedelta

from .gaming import Article, Source, GamingProject, BlockchainData, NFTCollection
from .schemas import (
    ArticleCreate, ArticleUpdate,
    SourceCreate, SourceUpdate,
    GamingProjectCreate, GamingProjectUpdate,
    BlockchainDataCreate,
    NFTCollectionCreate, NFTCollectionUpdate
)


class CRUDBase:
    """Base CRUD class with common operations"""
    
    def __init__(self, model):
        self.model = model
    
    def get(self, db: Session, id: int):
        """Get a single record by ID"""
        return db.query(self.model).filter(self.model.id == id).first()
    
    def get_multi(
        self, 
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        order_by: str = "id",
        order_desc: bool = False
    ):
        """Get multiple records with pagination"""
        query = db.query(self.model)
        
        # Apply ordering
        if hasattr(self.model, order_by):
            order_column = getattr(self.model, order_by)
            if order_desc:
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))
        
        return query.offset(skip).limit(limit).all()
    
    def create(self, db: Session, obj_in):
        """Create a new record"""
        if hasattr(obj_in, 'dict'):
            obj_data = obj_in.dict()
        else:
            obj_data = obj_in
        
        db_obj = self.model(**obj_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update(self, db: Session, db_obj, obj_in):
        """Update an existing record"""
        if hasattr(obj_in, 'dict'):
            update_data = obj_in.dict(exclude_unset=True)
        else:
            update_data = obj_in
        
        for field, value in update_data.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def delete(self, db: Session, id: int):
        """Delete a record by ID"""
        obj = db.query(self.model).get(id)
        if obj:
            db.delete(obj)
            db.commit()
        return obj
    
    def count(self, db: Session):
        """Count total records"""
        return db.query(self.model).count()


class CRUDSource(CRUDBase):
    """CRUD operations for Source model"""
    
    def __init__(self):
        super().__init__(Source)
    
    def get_by_slug(self, db: Session, slug: str):
        """Get source by slug"""
        return db.query(Source).filter(Source.slug == slug).first()
    
    def get_by_url(self, db: Session, url: str):
        """Get source by URL"""
        return db.query(Source).filter(Source.url == url).first()
    
    def get_active(self, db: Session):
        """Get all active sources"""
        return db.query(Source).filter(Source.is_active == True).all()
    
    def get_by_category(self, db: Session, category: str):
        """Get sources by category"""
        return db.query(Source).filter(Source.category == category).all()
    
    def update_last_scraped(self, db: Session, source_id: int):
        """Update last scraped timestamp"""
        source = self.get(db, source_id)
        if source:
            source.last_scraped_at = datetime.utcnow()
            db.commit()
            db.refresh(source)
        return source


class CRUDArticle(CRUDBase):
    """CRUD operations for Article model"""
    
    def __init__(self):
        super().__init__(Article)
    
    def get_by_url(self, db: Session, url: str):
        """Get article by URL"""
        return db.query(Article).filter(Article.url == url).first()
    
    def get_by_source(self, db: Session, source_id: int, skip: int = 0, limit: int = 100):
        """Get articles by source"""
        return db.query(Article).filter(
            Article.source_id == source_id
        ).order_by(desc(Article.published_at)).offset(skip).limit(limit).all()
    
    def get_by_category(self, db: Session, category: str, skip: int = 0, limit: int = 100):
        """Get articles by gaming category"""
        return db.query(Article).filter(
            Article.gaming_category == category
        ).order_by(desc(Article.published_at)).offset(skip).limit(limit).all()
    
    def get_recent(self, db: Session, hours: int = 24, skip: int = 0, limit: int = 100):
        """Get recent articles"""
        since = datetime.utcnow() - timedelta(hours=hours)
        return db.query(Article).filter(
            Article.published_at >= since
        ).order_by(desc(Article.published_at)).offset(skip).limit(limit).all()
    
    def get_by_relevance(self, db: Session, min_score: float = 0.5, skip: int = 0, limit: int = 100):
        """Get articles by relevance score"""
        return db.query(Article).filter(
            Article.relevance_score >= min_score
        ).order_by(desc(Article.relevance_score)).offset(skip).limit(limit).all()
    
    def search(self, db: Session, query: str, skip: int = 0, limit: int = 100):
        """Search articles by title and content"""
        search_filter = or_(
            Article.title.contains(query),
            Article.content.contains(query),
            Article.summary.contains(query)
        )
        return db.query(Article).filter(search_filter).order_by(
            desc(Article.published_at)
        ).offset(skip).limit(limit).all()
    
    def get_unprocessed(self, db: Session, limit: int = 100):
        """Get unprocessed articles"""
        return db.query(Article).filter(
            Article.is_processed == False
        ).order_by(Article.created_at).limit(limit).all()
    
    def mark_processed(self, db: Session, article_id: int):
        """Mark article as processed"""
        article = self.get(db, article_id)
        if article:
            article.is_processed = True
            db.commit()
            db.refresh(article)
        return article
    
    def increment_views(self, db: Session, article_id: int):
        """Increment article view count"""
        article = self.get(db, article_id)
        if article:
            article.views += 1
            db.commit()
            db.refresh(article)
        return article


class CRUDGamingProject(CRUDBase):
    """CRUD operations for GamingProject model"""
    
    def __init__(self):
        super().__init__(GamingProject)
    
    def get_by_slug(self, db: Session, slug: str):
        """Get gaming project by slug"""
        return db.query(GamingProject).filter(GamingProject.slug == slug).first()
    
    def get_by_name(self, db: Session, name: str):
        """Get gaming project by name"""
        return db.query(GamingProject).filter(GamingProject.name == name).first()
    
    def get_by_category(self, db: Session, category: str):
        """Get gaming projects by category"""
        return db.query(GamingProject).filter(GamingProject.category == category).all()
    
    def get_by_blockchain(self, db: Session, blockchain: str):
        """Get gaming projects by blockchain"""
        return db.query(GamingProject).filter(GamingProject.blockchain == blockchain).all()
    
    def get_active(self, db: Session):
        """Get all active gaming projects"""
        return db.query(GamingProject).filter(GamingProject.is_active == True).all()
    
    def get_by_token_symbol(self, db: Session, symbol: str):
        """Get gaming project by token symbol"""
        return db.query(GamingProject).filter(GamingProject.token_symbol == symbol).first()
    
    def search(self, db: Session, query: str, skip: int = 0, limit: int = 100):
        """Search gaming projects"""
        search_filter = or_(
            GamingProject.name.contains(query),
            GamingProject.description.contains(query),
            GamingProject.token_symbol.contains(query)
        )
        return db.query(GamingProject).filter(search_filter).offset(skip).limit(limit).all()


class CRUDBlockchainData(CRUDBase):
    """CRUD operations for BlockchainData model"""
    
    def __init__(self):
        super().__init__(BlockchainData)
    
    def get_by_transaction(self, db: Session, tx_hash: str):
        """Get blockchain data by transaction hash"""
        return db.query(BlockchainData).filter(BlockchainData.transaction_hash == tx_hash).first()
    
    def get_by_contract(self, db: Session, contract_address: str, skip: int = 0, limit: int = 100):
        """Get blockchain data by contract address"""
        return db.query(BlockchainData).filter(
            BlockchainData.contract_address == contract_address
        ).order_by(desc(BlockchainData.block_timestamp)).offset(skip).limit(limit).all()
    
    def get_by_blockchain(self, db: Session, blockchain: str, skip: int = 0, limit: int = 100):
        """Get blockchain data by blockchain network"""
        return db.query(BlockchainData).filter(
            BlockchainData.blockchain == blockchain
        ).order_by(desc(BlockchainData.block_timestamp)).offset(skip).limit(limit).all()
    
    def get_by_event_type(self, db: Session, event_type: str, skip: int = 0, limit: int = 100):
        """Get blockchain data by event type"""
        return db.query(BlockchainData).filter(
            BlockchainData.event_type == event_type
        ).order_by(desc(BlockchainData.block_timestamp)).offset(skip).limit(limit).all()
    
    def get_recent(self, db: Session, hours: int = 24, skip: int = 0, limit: int = 100):
        """Get recent blockchain data"""
        since = datetime.utcnow() - timedelta(hours=hours)
        return db.query(BlockchainData).filter(
            BlockchainData.block_timestamp >= since
        ).order_by(desc(BlockchainData.block_timestamp)).offset(skip).limit(limit).all()


class CRUDNFTCollection(CRUDBase):
    """CRUD operations for NFTCollection model"""
    
    def __init__(self):
        super().__init__(NFTCollection)
    
    def get_by_slug(self, db: Session, slug: str):
        """Get NFT collection by slug"""
        return db.query(NFTCollection).filter(NFTCollection.slug == slug).first()
    
    def get_by_contract(self, db: Session, contract_address: str, blockchain: str):
        """Get NFT collection by contract address and blockchain"""
        return db.query(NFTCollection).filter(
            and_(
                NFTCollection.contract_address == contract_address,
                NFTCollection.blockchain == blockchain
            )
        ).first()
    
    def get_by_blockchain(self, db: Session, blockchain: str):
        """Get NFT collections by blockchain"""
        return db.query(NFTCollection).filter(NFTCollection.blockchain == blockchain).all()
    
    def get_by_gaming_category(self, db: Session, category: str):
        """Get NFT collections by gaming category"""
        return db.query(NFTCollection).filter(NFTCollection.gaming_category == category).all()
    
    def get_verified(self, db: Session):
        """Get verified NFT collections"""
        return db.query(NFTCollection).filter(NFTCollection.is_verified == True).all()
    
    def get_by_floor_price_range(self, db: Session, min_price: float, max_price: float):
        """Get NFT collections by floor price range"""
        return db.query(NFTCollection).filter(
            and_(
                NFTCollection.floor_price >= min_price,
                NFTCollection.floor_price <= max_price
            )
        ).order_by(asc(NFTCollection.floor_price)).all()


# Create CRUD instances
source = CRUDSource()
article = CRUDArticle()
gaming_project = CRUDGamingProject()
blockchain_data = CRUDBlockchainData()
nft_collection = CRUDNFTCollection()
