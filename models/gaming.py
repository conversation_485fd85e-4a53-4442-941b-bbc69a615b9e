"""
Gaming-specific database models
"""
from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, Integer, String, Text, Boolean, Float, JSON, ForeignKey, Index, DateTime
from sqlalchemy.orm import relationship
from models.base import BaseModel
import uuid


class Article(BaseModel):
    """News article model"""
    __tablename__ = "articles"
    
    # Basic article info
    title = Column(String(500), nullable=False, index=True)
    content = Column(Text)
    summary = Column(Text)
    url = Column(String(1000), unique=True, nullable=False, index=True)
    author = Column(String(200))
    published_at = Column(DateTime, index=True)
    
    # Source information
    source_id = Column(Integer, ForeignKey("sources.id"), nullable=False)
    source_url = Column(String(1000))
    
    # Gaming categorization
    gaming_category = Column(String(50), index=True)  # P2E, NFT, DeFi, Metaverse, etc.
    gaming_subcategory = Column(String(100))
    gaming_projects = Column(JSON)  # Related gaming projects (list of strings)
    gaming_tokens = Column(JSON)   # Related tokens (list of strings)

    # Content analysis
    sentiment_score = Column(Float)  # -1 to 1
    relevance_score = Column(Float)  # 0 to 1
    keywords = Column(JSON)  # List of keywords
    tags = Column(JSON)  # List of tags
    
    # Engagement metrics
    views = Column(Integer, default=0)
    likes = Column(Integer, default=0)
    shares = Column(Integer, default=0)
    comments = Column(Integer, default=0)
    
    # Processing status
    is_processed = Column(Boolean, default=False)
    is_duplicate = Column(Boolean, default=False)
    duplicate_of_id = Column(Integer, ForeignKey("articles.id"))
    
    # Additional metadata
    extra_metadata = Column(JSON)
    
    # Relationships
    source = relationship("Source", back_populates="articles")
    blockchain_data = relationship("BlockchainData", back_populates="article")
    
    # Indexes
    __table_args__ = (
        Index('idx_articles_gaming_category', 'gaming_category'),
        Index('idx_articles_published_at', 'published_at'),
        Index('idx_articles_relevance', 'relevance_score'),
        Index('idx_articles_source_published', 'source_id', 'published_at'),
    )


class Source(BaseModel):
    """News source model"""
    __tablename__ = "sources"
    
    name = Column(String(200), nullable=False, unique=True)
    url = Column(String(1000), nullable=False)
    source_type = Column(String(50), nullable=False)  # RSS, API, Scraper, Social
    category = Column(String(100))  # Gaming, General, Social, Blockchain
    
    # Source configuration
    is_active = Column(Boolean, default=True)
    scrape_frequency = Column(Integer, default=3600)  # seconds
    last_scraped_at = Column(DateTime)
    
    # Quality metrics
    reliability_score = Column(Float, default=0.5)  # 0 to 1
    article_count = Column(Integer, default=0)
    
    # Configuration
    config = Column(JSON)  # Source-specific configuration
    
    # Relationships
    articles = relationship("Article", back_populates="source")


class GamingProject(BaseModel):
    """Gaming project/protocol model"""
    __tablename__ = "gaming_projects"
    
    name = Column(String(200), nullable=False, unique=True)
    slug = Column(String(200), nullable=False, unique=True, index=True)
    description = Column(Text)
    website = Column(String(500))
    
    # Project categorization
    category = Column(String(50), nullable=False)  # P2E, NFT, DeFi, Metaverse
    subcategory = Column(String(100))
    blockchain = Column(String(50))  # Ethereum, Polygon, BSC, etc.
    
    # Contract information
    contract_addresses = Column(JSON)  # {chain: address}
    token_symbol = Column(String(20))
    token_address = Column(String(100))
    
    # Project metrics
    market_cap = Column(Float)
    token_price = Column(Float)
    daily_active_users = Column(Integer)
    total_value_locked = Column(Float)
    
    # Social metrics
    twitter_followers = Column(Integer)
    discord_members = Column(Integer)
    telegram_members = Column(Integer)
    
    # Status
    is_active = Column(Boolean, default=True)
    launch_date = Column(DateTime)
    
    # Additional metadata
    extra_metadata = Column(JSON)
    
    # Relationships
    blockchain_data = relationship("BlockchainData", back_populates="gaming_project")


class BlockchainData(BaseModel):
    """Blockchain-related data model"""
    __tablename__ = "blockchain_data"
    
    # Reference to article or project
    article_id = Column(Integer, ForeignKey("articles.id"))
    gaming_project_id = Column(Integer, ForeignKey("gaming_projects.id"))
    
    # Blockchain information
    blockchain = Column(String(50), nullable=False, index=True)
    block_number = Column(Integer)
    transaction_hash = Column(String(100), index=True)
    contract_address = Column(String(100), index=True)
    
    # Event data
    event_type = Column(String(100))  # Transfer, Sale, Mint, etc.
    event_data = Column(JSON)
    
    # Token information
    token_symbol = Column(String(20))
    token_address = Column(String(100))
    token_amount = Column(Float)
    token_price_usd = Column(Float)
    
    # NFT information
    nft_collection = Column(String(200))
    nft_token_id = Column(String(100))
    nft_metadata = Column(JSON)
    
    # Transaction details
    from_address = Column(String(100))
    to_address = Column(String(100))
    gas_used = Column(Integer)
    gas_price = Column(Float)
    
    # Timestamps
    block_timestamp = Column(DateTime, index=True)
    
    # Relationships
    article = relationship("Article", back_populates="blockchain_data")
    gaming_project = relationship("GamingProject", back_populates="blockchain_data")
    
    # Indexes
    __table_args__ = (
        Index('idx_blockchain_data_blockchain', 'blockchain'),
        Index('idx_blockchain_data_contract', 'contract_address'),
        Index('idx_blockchain_data_timestamp', 'block_timestamp'),
        Index('idx_blockchain_data_event_type', 'event_type'),
    )


class NFTCollection(BaseModel):
    """NFT collection model for gaming NFTs"""
    __tablename__ = "nft_collections"
    
    name = Column(String(200), nullable=False)
    slug = Column(String(200), nullable=False, unique=True, index=True)
    contract_address = Column(String(100), nullable=False, index=True)
    blockchain = Column(String(50), nullable=False)
    
    # Collection info
    description = Column(Text)
    image_url = Column(String(500))
    website = Column(String(500))
    
    # Gaming categorization
    gaming_category = Column(String(50))  # In-game assets, Characters, Land, etc.
    gaming_project_id = Column(Integer, ForeignKey("gaming_projects.id"))
    
    # Collection metrics
    total_supply = Column(Integer)
    floor_price = Column(Float)
    floor_price_usd = Column(Float)
    volume_24h = Column(Float)
    volume_total = Column(Float)
    owners_count = Column(Integer)
    
    # Social metrics
    discord_members = Column(Integer)
    twitter_followers = Column(Integer)
    
    # Status
    is_verified = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    
    # Additional metadata
    extra_metadata = Column(JSON)
    
    # Indexes
    __table_args__ = (
        Index('idx_nft_collections_blockchain', 'blockchain'),
        Index('idx_nft_collections_gaming_category', 'gaming_category'),
        Index('idx_nft_collections_floor_price', 'floor_price'),
    )
