"""
Base database models and configuration
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import Column, Integer, DateTime, String, Text, Boolean, Float, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from config.settings import get_settings

settings = get_settings()

# Create database engine
# Configure engine based on database type
if settings.database.url.startswith('sqlite'):
    # SQLite configuration
    engine = create_engine(
        settings.database.url,
        echo=settings.api.debug,
        connect_args={"check_same_thread": False}  # Allow SQLite to be used with multiple threads
    )
else:
    # PostgreSQL/other database configuration
    engine = create_engine(
        settings.database.url,
        echo=settings.api.debug,
        pool_size=10,
        max_overflow=20,
        pool_pre_ping=True,
        pool_recycle=3600
    )

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()


class TimestampMixin:
    """Mixin for timestamp fields"""
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class BaseModel(Base, TimestampMixin):
    """Base model with common fields"""
    __abstract__ = True

    id = Column(Integer, primary_key=True, index=True)


def get_db():
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """Create all tables"""
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """Drop all tables"""
    Base.metadata.drop_all(bind=engine)
