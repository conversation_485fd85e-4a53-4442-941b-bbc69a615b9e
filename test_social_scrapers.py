#!/usr/bin/env python3
"""
Test script for social media scrapers
Tests Twitter and Reddit scrapers with actual API calls
"""
import asyncio
import logging
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrapers.social.twitter_scraper import TwitterGamingScraper
from scrapers.social.reddit_scraper import RedditGamingScraper
from config.settings import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_twitter_scraper():
    """Test Twitter gaming scraper"""
    logger.info("🐦 Testing Twitter Gaming Scraper...")
    
    try:
        scraper = TwitterGamingScraper()
        
        # Check configuration
        status = await scraper.get_collection_status()
        logger.info(f"Twitter Scraper Status: {status}")
        
        if not status.get('bearer_token_configured'):
            logger.warning("⚠️ Twitter Bearer Token not configured - skipping actual collection")
            return
        
        # Test collection
        async with scraper:
            tweets = await scraper.collect_gaming_tweets()
            logger.info(f"✅ Collected {len(tweets)} gaming tweets")
            
            # Show sample tweets
            for i, tweet in enumerate(tweets[:3]):
                logger.info(f"Tweet {i+1}: {tweet.text[:100]}...")
                logger.info(f"  Author: @{tweet.author_username}")
                logger.info(f"  URL: {tweet.url}")
                logger.info(f"  Hashtags: {tweet.hashtags}")
                
    except Exception as e:
        logger.error(f"❌ Twitter scraper test failed: {e}")

async def test_reddit_scraper():
    """Test Reddit gaming scraper"""
    logger.info("🔴 Testing Reddit Gaming Scraper...")
    
    try:
        scraper = RedditGamingScraper()
        
        # Check configuration
        status = await scraper.get_collection_status()
        logger.info(f"Reddit Scraper Status: {status}")
        
        if not status.get('credentials_configured'):
            logger.warning("⚠️ Reddit API credentials not configured - skipping actual collection")
            return
        
        # Test collection
        async with scraper:
            posts = await scraper.collect_gaming_posts()
            logger.info(f"✅ Collected {len(posts)} gaming posts")
            
            # Show sample posts
            for i, post in enumerate(posts[:3]):
                logger.info(f"Post {i+1}: {post.title}")
                logger.info(f"  Subreddit: r/{post.subreddit}")
                logger.info(f"  Score: {post.score}")
                logger.info(f"  URL: {post.permalink}")
                
    except Exception as e:
        logger.error(f"❌ Reddit scraper test failed: {e}")

async def test_dune_analytics():
    """Test Dune Analytics integration"""
    logger.info("📊 Testing Dune Analytics Integration...")
    
    try:
        from blockchain.dune_analytics import DuneAnalytics
        
        dune = DuneAnalytics()
        
        # Check configuration
        if not dune.api_key:
            logger.warning("⚠️ Dune API key not configured - skipping actual queries")
            return
        
        # Test a simple query
        async with dune:
            query = dune.gaming_queries["gaming_nft_volume"]
            logger.info(f"Testing query: {query.name}")
            
            execution_id = await dune.execute_query(query)
            if execution_id:
                logger.info(f"✅ Query executed with ID: {execution_id}")
                
                # Wait for results (with timeout)
                result = await dune.execute_and_wait(query, max_wait_seconds=60)
                if result:
                    logger.info(f"✅ Query completed with {len(result.data)} rows")
                else:
                    logger.warning("⏰ Query timed out or failed")
            else:
                logger.error("❌ Failed to execute query")
                
    except Exception as e:
        logger.error(f"❌ Dune Analytics test failed: {e}")

async def main():
    """Run all tests"""
    logger.info("🚀 Starting Social Media Scrapers Test Suite")
    logger.info("=" * 60)
    
    # Load settings
    settings = get_settings()
    logger.info(f"Environment: {settings.environment}")
    
    # Test each component
    await test_twitter_scraper()
    logger.info("-" * 40)
    
    await test_reddit_scraper()
    logger.info("-" * 40)
    
    await test_dune_analytics()
    logger.info("-" * 40)
    
    logger.info("🏁 Test suite completed")

if __name__ == "__main__":
    asyncio.run(main())
