"""
Gaming-specific news scrapers for popular sources
"""
import logging
from datetime import datetime
from typing import List, Dict, Any
from urllib.parse import urljoin

from bs4 import BeautifulSoup

from .base import BaseScraper, NewsItem, RSSFeedScraper, WebScraper

logger = logging.getLogger(__name__)


class CoinDeskGamingScraper(RSSFeedScraper):
    """CoinDesk gaming news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://www.coindesk.com/arc/outboundfeeds/rss/',
            'fetch_full_content': True
        }
        super().__init__(source_id, config)
    
    def is_gaming_related(self, text: str) -> bool:
        """Enhanced gaming detection for CoinDesk"""
        gaming_keywords = [
            'gaming', 'game', 'nft', 'metaverse', 'play-to-earn', 'p2e',
            'blockchain game', 'crypto game', 'virtual world', 'axie',
            'sandbox', 'decentraland', 'enjin', 'immutable', 'polygon',
            'ethereum game', 'defi game', 'gamefi', 'web3 game',
            'opensea', 'collectible', 'avatar', 'virtual land'
        ]
        
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in gaming_keywords)


class DecryptGamingScraper(WebScraper):
    """Decrypt gaming news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://decrypt.co/news/gaming',
            'selectors': {
                'article_links': 'article h2 a',
                'title': 'h1.post-title',
                'content': '.post-content',
                'author': '.author-name'
            }
        }
        super().__init__(source_id, config)


class TheBlockGamingScraper(RSSFeedScraper):
    """The Block gaming news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://www.theblockcrypto.com/rss.xml',
            'fetch_full_content': False
        }
        super().__init__(source_id, config)


class CoinTelegraphGamingScraper(RSSFeedScraper):
    """CoinTelegraph gaming news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://cointelegraph.com/rss/tag/games',
            'fetch_full_content': True
        }
        super().__init__(source_id, config)


class GamesBeatWeb3Scraper(WebScraper):
    """GamesBeat Web3 gaming scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://venturebeat.com/games/',
            'selectors': {
                'article_links': '.ArticleListing h2 a',
                'title': 'h1.article-title',
                'content': '.article-content',
                'author': '.author-name'
            }
        }
        super().__init__(source_id, config)


class NFTGamerScraper(RSSFeedScraper):
    """NFT Gamer news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://nftgamer.io/feed/',
            'fetch_full_content': False
        }
        super().__init__(source_id, config)


class PlayToEarnScraper(WebScraper):
    """Play to Earn news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://playtoearn.net/news',
            'selectors': {
                'article_links': '.news-item h3 a',
                'title': 'h1.entry-title',
                'content': '.entry-content',
                'author': '.author-name'
            }
        }
        super().__init__(source_id, config)


class DappRadarScraper(RSSFeedScraper):
    """DappRadar gaming news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://dappradar.com/blog/feed',
            'fetch_full_content': False
        }
        super().__init__(source_id, config)
    
    def is_gaming_related(self, text: str) -> bool:
        """Enhanced gaming detection for DappRadar"""
        gaming_keywords = [
            'gaming', 'game', 'nft', 'metaverse', 'play-to-earn', 'p2e',
            'blockchain game', 'crypto game', 'virtual world', 'dapp',
            'gamefi', 'web3 game', 'gaming dapp', 'nft game'
        ]
        
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in gaming_keywords)


class BeInCryptoGamingScraper(RSSFeedScraper):
    """BeInCrypto gaming news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://beincrypto.com/feed/',
            'fetch_full_content': False
        }
        super().__init__(source_id, config)


# Scraper registry mapping source names to scraper classes
GAMING_SCRAPERS = {
    'coindesk-gaming': CoinDeskGamingScraper,
    'decrypt-gaming': DecryptGamingScraper,
    'theblock-gaming': TheBlockGamingScraper,
    'cointelegraph-gaming': CoinTelegraphGamingScraper,
    'gamesbeat-web3': GamesBeatWeb3Scraper,
    'nft-gamer': NFTGamerScraper,
    'play-to-earn': PlayToEarnScraper,
    'dappradar': DappRadarScraper,
    'beincrypto-gaming': BeInCryptoGamingScraper
}


def get_scraper_for_source(source_name: str, source_id: int) -> BaseScraper:
    """Get appropriate scraper for a source"""
    scraper_class = GAMING_SCRAPERS.get(source_name)
    if scraper_class:
        return scraper_class(source_id)
    else:
        # Default to RSS scraper
        logger.warning(f"No specific scraper for {source_name}, using default RSS scraper")
        return RSSFeedScraper(source_id, {})


async def scrape_all_sources() -> Dict[str, List[NewsItem]]:
    """Scrape all configured gaming news sources"""
    from models.base import SessionLocal
    from models.gaming import Source
    
    results = {}
    db = SessionLocal()
    
    try:
        # Get all active sources
        sources = db.query(Source).filter(Source.is_active == True).all()
        
        for source in sources:
            logger.info(f"Scraping source: {source.name}")
            
            try:
                # Get appropriate scraper
                scraper = get_scraper_for_source(source.slug, source.id)
                
                # Scrape articles
                async with scraper:
                    articles = await scraper.scrape()
                    results[source.slug] = articles
                    
                logger.info(f"Scraped {len(articles)} articles from {source.name}")
                
            except Exception as e:
                logger.error(f"Error scraping {source.name}: {e}")
                results[source.slug] = []
    
    finally:
        db.close()
    
    return results


async def save_scraped_articles(scraped_data: Dict[str, List[NewsItem]]) -> int:
    """Save scraped articles to database"""
    from models.base import SessionLocal
    from models.gaming import Article, Source
    
    db = SessionLocal()
    saved_count = 0
    
    try:
        for source_slug, articles in scraped_data.items():
            # Get source
            source = db.query(Source).filter(Source.slug == source_slug).first()
            if not source:
                logger.warning(f"Source {source_slug} not found")
                continue
            
            for news_item in articles:
                # Check if article already exists
                existing = db.query(Article).filter(
                    Article.url == news_item.url
                ).first()
                
                if existing:
                    logger.debug(f"Article already exists: {news_item.title}")
                    continue
                
                # Create new article
                article = Article(
                    title=news_item.title,
                    content=news_item.content,
                    summary=news_item.summary,
                    url=news_item.url,
                    author=news_item.author,
                    published_at=news_item.published_at or datetime.now(),
                    source_id=source.id,
                    keywords=news_item.keywords,
                    tags=news_item.raw_data.get('tags', []),
                    sentiment_score=news_item.raw_data.get('sentiment_score', 0.0),
                    extra_metadata=news_item.raw_data
                )
                
                db.add(article)
                saved_count += 1
        
        db.commit()
        logger.info(f"Saved {saved_count} new articles to database")
        
    except Exception as e:
        logger.error(f"Error saving articles: {e}")
        db.rollback()
    finally:
        db.close()
    
    return saved_count
