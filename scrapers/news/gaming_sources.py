"""
Gaming-specific news scrapers for popular sources with blockchain-aware classification
"""
import logging
import re
from datetime import datetime
from typing import List, Dict, Any, Set, Optional
from urllib.parse import urljoin
from enum import Enum

from bs4 import BeautifulSoup

from .base import BaseScraper, NewsItem, RSSFeedScraper, WebScraper

logger = logging.getLogger(__name__)


class BlockchainNetwork(Enum):
    """Supported blockchain networks for gaming"""
    ETHEREUM = "ethereum"
    POLYGON = "polygon"
    SOLANA = "solana"
    BSC = "bsc"
    ARBITRUM = "arbitrum"
    OPTIMISM = "optimism"
    BASE = "base"
    AVALANCHE = "avalanche"
    IMMUTABLE_X = "immutable_x"
    RONIN = "ronin"
    TON = "ton"
    UNKNOWN = "unknown"


class GamingCategory(Enum):
    """Gaming content categories"""
    P2E = "play_to_earn"
    NFT_GAMING = "nft_gaming"
    DEFI_GAMING = "defi_gaming"
    METAVERSE = "metaverse"
    GAMING_INFRASTRUCTURE = "gaming_infrastructure"
    ESPORTS = "esports"
    GAMING_TOKENS = "gaming_tokens"
    GAMING_NFTS = "gaming_nfts"
    GENERAL_GAMING = "general_gaming"


class BlockchainAwareClassifier:
    """Enhanced classifier for blockchain-aware gaming content"""

    def __init__(self):
        # Blockchain-specific keywords
        self.blockchain_keywords = {
            BlockchainNetwork.ETHEREUM: [
                'ethereum', 'eth', 'erc-20', 'erc-721', 'erc-1155', 'opensea',
                'ethereum mainnet', 'ethereum network', 'ethereum blockchain'
            ],
            BlockchainNetwork.POLYGON: [
                'polygon', 'matic', 'polygon network', 'polygon blockchain',
                'polygon gaming', 'matic network'
            ],
            BlockchainNetwork.SOLANA: [
                'solana', 'sol', 'solana network', 'solana blockchain', 'magic eden',
                'phantom wallet', 'solana gaming', 'solana nft', 'spl token',
                'solana ecosystem', 'solana labs', 'solana foundation'
            ],
            BlockchainNetwork.BSC: [
                'binance smart chain', 'bsc', 'bnb chain', 'pancakeswap',
                'binance chain', 'bep-20'
            ],
            BlockchainNetwork.ARBITRUM: [
                'arbitrum', 'arbitrum one', 'arbitrum network', 'arbitrum gaming'
            ],
            BlockchainNetwork.OPTIMISM: [
                'optimism', 'optimistic ethereum', 'op mainnet', 'optimism network'
            ],
            BlockchainNetwork.BASE: [
                'base', 'base network', 'coinbase base', 'base blockchain'
            ],
            BlockchainNetwork.AVALANCHE: [
                'avalanche', 'avax', 'avalanche network', 'avalanche blockchain'
            ],
            BlockchainNetwork.IMMUTABLE_X: [
                'immutable x', 'immutable', 'imx', 'immutable gaming'
            ],
            BlockchainNetwork.RONIN: [
                'ronin', 'ronin network', 'ronin blockchain', 'axie infinity',
                'sky mavis'
            ],
            BlockchainNetwork.TON: [
                'ton', 'the open network', 'ton blockchain', 'telegram blockchain'
            ]
        }

        # Gaming category keywords
        self.category_keywords = {
            GamingCategory.P2E: [
                'play-to-earn', 'p2e', 'play to earn', 'earn while playing',
                'gaming rewards', 'token rewards', 'gaming economy'
            ],
            GamingCategory.NFT_GAMING: [
                'nft game', 'nft gaming', 'gaming nft', 'collectible game',
                'digital collectibles', 'in-game nft', 'gaming assets'
            ],
            GamingCategory.DEFI_GAMING: [
                'defi gaming', 'gamefi', 'yield farming game', 'gaming defi',
                'staking game', 'liquidity gaming'
            ],
            GamingCategory.METAVERSE: [
                'metaverse', 'virtual world', 'virtual land', 'digital real estate',
                'avatar', 'virtual reality', 'vr gaming', 'ar gaming'
            ],
            GamingCategory.GAMING_INFRASTRUCTURE: [
                'gaming infrastructure', 'gaming platform', 'gaming protocol',
                'gaming sdk', 'gaming api', 'blockchain gaming platform'
            ],
            GamingCategory.ESPORTS: [
                'esports', 'e-sports', 'competitive gaming', 'gaming tournament',
                'gaming competition', 'professional gaming'
            ]
        }

        # Gaming project patterns (regex)
        self.project_patterns = {
            'axie_infinity': r'\b(axie\s+infinity|axs|slp)\b',
            'the_sandbox': r'\b(the\s+sandbox|sand\s+token|sandbox\s+game)\b',
            'decentraland': r'\b(decentraland|mana\s+token|dcl)\b',
            'enjin': r'\b(enjin|enj\s+token|enjin\s+coin)\b',
            'gala_games': r'\b(gala\s+games|gala\s+token|gala\s+network)\b',
            'immutable_x': r'\b(immutable\s+x|imx\s+token|immutable\s+gaming)\b',
            'polygon_gaming': r'\b(polygon\s+gaming|matic\s+gaming)\b',
            'solana_gaming': r'\b(solana\s+gaming|sol\s+gaming|magic\s+eden)\b'
        }

    def detect_blockchain_networks(self, text: str) -> Set[BlockchainNetwork]:
        """Detect mentioned blockchain networks in text"""
        text_lower = text.lower()
        detected_networks = set()

        for network, keywords in self.blockchain_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                detected_networks.add(network)

        return detected_networks if detected_networks else {BlockchainNetwork.UNKNOWN}

    def detect_gaming_categories(self, text: str) -> Set[GamingCategory]:
        """Detect gaming categories in text"""
        text_lower = text.lower()
        detected_categories = set()

        for category, keywords in self.category_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                detected_categories.add(category)

        # Default to general gaming if no specific category detected
        return detected_categories if detected_categories else {GamingCategory.GENERAL_GAMING}

    def detect_gaming_projects(self, text: str) -> Set[str]:
        """Detect mentioned gaming projects in text"""
        text_lower = text.lower()
        detected_projects = set()

        for project, pattern in self.project_patterns.items():
            if re.search(pattern, text_lower, re.IGNORECASE):
                detected_projects.add(project)

        return detected_projects

    def classify_content(self, title: str, content: str = "", summary: str = "") -> Dict[str, Any]:
        """Comprehensive content classification"""
        full_text = f"{title} {content} {summary}".strip()

        return {
            'blockchain_networks': [net.value for net in self.detect_blockchain_networks(full_text)],
            'gaming_categories': [cat.value for cat in self.detect_gaming_categories(full_text)],
            'gaming_projects': list(self.detect_gaming_projects(full_text)),
            'is_solana_focused': BlockchainNetwork.SOLANA in self.detect_blockchain_networks(full_text),
            'is_ethereum_focused': BlockchainNetwork.ETHEREUM in self.detect_blockchain_networks(full_text),
            'is_multi_chain': len(self.detect_blockchain_networks(full_text)) > 1
        }


# Global classifier instance
blockchain_classifier = BlockchainAwareClassifier()


class CoinDeskGamingScraper(RSSFeedScraper):
    """CoinDesk gaming news scraper with blockchain-aware classification"""

    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://www.coindesk.com/arc/outboundfeeds/rss/',
            'fetch_full_content': True
        }
        super().__init__(source_id, config)

    def is_gaming_related(self, text: str) -> bool:
        """Enhanced gaming detection using blockchain-aware classifier"""
        classification = blockchain_classifier.classify_content(text)

        # Check if it's gaming-related based on categories or projects
        has_gaming_categories = any(
            cat != GamingCategory.GENERAL_GAMING.value
            for cat in classification['gaming_categories']
        )
        has_gaming_projects = len(classification['gaming_projects']) > 0

        # Traditional keyword check as fallback
        gaming_keywords = [
            'gaming', 'game', 'nft', 'metaverse', 'play-to-earn', 'p2e',
            'blockchain game', 'crypto game', 'virtual world', 'gamefi', 'web3 game'
        ]
        has_gaming_keywords = any(keyword in text.lower() for keyword in gaming_keywords)

        return has_gaming_categories or has_gaming_projects or has_gaming_keywords

    async def process_item(self, item: NewsItem) -> NewsItem:
        """Add blockchain classification to news items"""
        classification = blockchain_classifier.classify_content(
            item.title, item.content or "", item.summary or ""
        )

        # Add classification to raw_data
        item.raw_data.update({
            'blockchain_classification': classification,
            'source_type': 'mainstream_crypto_news'
        })

        return item


class DecryptGamingScraper(WebScraper):
    """Decrypt gaming news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://decrypt.co/news/gaming',
            'selectors': {
                'article_links': 'article h2 a',
                'title': 'h1.post-title',
                'content': '.post-content',
                'author': '.author-name'
            }
        }
        super().__init__(source_id, config)


class TheBlockGamingScraper(RSSFeedScraper):
    """The Block gaming news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://www.theblockcrypto.com/rss.xml',
            'fetch_full_content': False
        }
        super().__init__(source_id, config)


class CoinTelegraphGamingScraper(RSSFeedScraper):
    """CoinTelegraph gaming news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://cointelegraph.com/rss/tag/games',
            'fetch_full_content': True
        }
        super().__init__(source_id, config)


class GamesBeatWeb3Scraper(WebScraper):
    """GamesBeat Web3 gaming scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://venturebeat.com/games/',
            'selectors': {
                'article_links': '.ArticleListing h2 a',
                'title': 'h1.article-title',
                'content': '.article-content',
                'author': '.author-name'
            }
        }
        super().__init__(source_id, config)


class NFTGamerScraper(RSSFeedScraper):
    """NFT Gamer news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://nftgamer.io/feed/',
            'fetch_full_content': False
        }
        super().__init__(source_id, config)


class PlayToEarnScraper(WebScraper):
    """Play to Earn news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://playtoearn.net/news',
            'selectors': {
                'article_links': '.news-item h3 a',
                'title': 'h1.entry-title',
                'content': '.entry-content',
                'author': '.author-name'
            }
        }
        super().__init__(source_id, config)


class DappRadarScraper(RSSFeedScraper):
    """DappRadar gaming news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://dappradar.com/blog/feed',
            'fetch_full_content': False
        }
        super().__init__(source_id, config)
    
    def is_gaming_related(self, text: str) -> bool:
        """Enhanced gaming detection for DappRadar"""
        gaming_keywords = [
            'gaming', 'game', 'nft', 'metaverse', 'play-to-earn', 'p2e',
            'blockchain game', 'crypto game', 'virtual world', 'dapp',
            'gamefi', 'web3 game', 'gaming dapp', 'nft game'
        ]
        
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in gaming_keywords)


class BeInCryptoGamingScraper(RSSFeedScraper):
    """BeInCrypto gaming news scraper"""
    
    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://beincrypto.com/feed/',
            'fetch_full_content': False
        }
        super().__init__(source_id, config)


class Gam3sScraper(WebScraper):
    """Gam3s.gg news scraper"""

    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://gam3s.gg/news',
            'selectors': {
                'article_links': 'a[href*="/news/"]',  # Based on actual structure
                'title': 'h1, h2, h3',
                'content': '.content, .article-content, p',
                'author': '.author, .by-author'
            }
        }
        super().__init__(source_id, config)


class ChainPlayScraper(WebScraper):
    """ChainPlay.gg news scraper"""

    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://chainplay.gg',
            'selectors': {
                'article_links': '.post-title a, .entry-title a',
                'title': 'h1.entry-title, h1.post-title',
                'content': '.entry-content, .post-content',
                'author': '.author-name, .entry-author'
            }
        }
        super().__init__(source_id, config)


class PlayToEarnComScraper(WebScraper):
    """PlayToEarn.com blockchain games scraper"""

    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://playtoearn.com/blockchaingames',
            'selectors': {
                'article_links': '.game-card a, .article-link',
                'title': 'h1, .game-title, .article-title',
                'content': '.game-description, .article-content',
                'author': '.author'
            }
        }
        super().__init__(source_id, config)


class GameFiToScraper(WebScraper):
    """GameFi.to news scraper"""

    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://gamefi.to',
            'selectors': {
                'article_links': '.news-item a, .post-link',
                'title': 'h1, .post-title',
                'content': '.post-content, .article-body',
                'author': '.author-info'
            }
        }
        super().__init__(source_id, config)


class GameFiOrgScraper(WebScraper):
    """GameFi.org news scraper"""

    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://gamefi.org',
            'selectors': {
                'article_links': '.article-card a, .news-link',
                'title': 'h1.article-title, h1',
                'content': '.article-content, .post-body',
                'author': '.author-name'
            }
        }
        super().__init__(source_id, config)


# Solana-specific gaming news sources
class SolanaNewsScraper(RSSFeedScraper):
    """Solana News gaming content scraper"""

    def __init__(self, source_id: int):
        config = {
            'rss_url': 'https://solana.com/news/rss.xml',
            'fetch_full_content': True
        }
        super().__init__(source_id, config)

    def is_gaming_related(self, text: str) -> bool:
        """Solana gaming detection"""
        solana_gaming_keywords = [
            'gaming', 'game', 'nft', 'metaverse', 'play-to-earn', 'p2e',
            'magic eden', 'solana gaming', 'sol gaming', 'gaming dapp',
            'nft game', 'blockchain game', 'web3 game', 'gamefi'
        ]

        text_lower = text.lower()
        return any(keyword in text_lower for keyword in solana_gaming_keywords)

    async def process_item(self, item: NewsItem) -> NewsItem:
        """Add Solana-specific classification"""
        classification = blockchain_classifier.classify_content(
            item.title, item.content or "", item.summary or ""
        )

        # Ensure Solana is marked as primary network
        if BlockchainNetwork.SOLANA.value not in classification['blockchain_networks']:
            classification['blockchain_networks'].append(BlockchainNetwork.SOLANA.value)

        item.raw_data.update({
            'blockchain_classification': classification,
            'source_type': 'solana_ecosystem',
            'primary_network': 'solana'
        })

        return item


class MagicEdenBlogScraper(WebScraper):
    """Magic Eden blog scraper for Solana NFT gaming news"""

    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://blog.magiceden.io',
            'selectors': {
                'article_links': '.post-card a, .article-link',
                'title': 'h1.post-title, h1',
                'content': '.post-content, .article-content',
                'author': '.author-name, .post-author'
            }
        }
        super().__init__(source_id, config)

    def is_gaming_related(self, text: str) -> bool:
        """Magic Eden gaming content detection"""
        gaming_keywords = [
            'gaming', 'game', 'nft game', 'play-to-earn', 'p2e',
            'gaming nft', 'solana gaming', 'gaming collection',
            'metaverse', 'virtual world', 'blockchain game'
        ]

        text_lower = text.lower()
        return any(keyword in text_lower for keyword in gaming_keywords)

    async def process_item(self, item: NewsItem) -> NewsItem:
        """Add Magic Eden specific classification"""
        classification = blockchain_classifier.classify_content(
            item.title, item.content or "", item.summary or ""
        )

        # Magic Eden is Solana-focused
        classification['blockchain_networks'] = [BlockchainNetwork.SOLANA.value]
        classification['is_solana_focused'] = True

        item.raw_data.update({
            'blockchain_classification': classification,
            'source_type': 'solana_nft_marketplace',
            'primary_network': 'solana',
            'marketplace': 'magic_eden'
        })

        return item


class SolanaLabsBlogScraper(WebScraper):
    """Solana Labs blog scraper for ecosystem updates"""

    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://solana.com/news',
            'selectors': {
                'article_links': '.news-card a, .post-link',
                'title': 'h1, .post-title',
                'content': '.post-content, .article-body',
                'author': '.author, .post-author'
            }
        }
        super().__init__(source_id, config)

    def is_gaming_related(self, text: str) -> bool:
        """Solana Labs gaming content detection"""
        gaming_keywords = [
            'gaming', 'game', 'nft', 'metaverse', 'play-to-earn', 'p2e',
            'gaming ecosystem', 'gaming developer', 'gaming dapp',
            'nft game', 'blockchain game', 'web3 game', 'gamefi'
        ]

        text_lower = text.lower()
        return any(keyword in text_lower for keyword in gaming_keywords)


class SolanaGamingDiscordScraper(BaseScraper):
    """Scraper for Solana gaming Discord announcements"""

    def __init__(self, source_id: int):
        self.source_id = source_id
        self.config = {
            'discord_channels': [
                'solana-gaming-general',
                'solana-nft-gaming',
                'magic-eden-announcements'
            ]
        }

    async def scrape(self) -> List[NewsItem]:
        """Scrape Solana gaming Discord content"""
        # This would integrate with Discord API or webhook system
        # For now, return empty list as placeholder
        logger.info("Solana gaming Discord scraping not yet implemented")
        return []


class StarAtlasScraper(WebScraper):
    """Star Atlas (major Solana game) news scraper"""

    def __init__(self, source_id: int):
        config = {
            'base_url': 'https://staratlas.com/news',
            'selectors': {
                'article_links': '.news-item a, .post-link',
                'title': 'h1.news-title, h1',
                'content': '.news-content, .post-body',
                'author': '.author-name'
            }
        }
        super().__init__(source_id, config)

    async def process_item(self, item: NewsItem) -> NewsItem:
        """Add Star Atlas specific classification"""
        classification = blockchain_classifier.classify_content(
            item.title, item.content or "", item.summary or ""
        )

        # Star Atlas is Solana-based
        classification['blockchain_networks'] = [BlockchainNetwork.SOLANA.value]
        classification['gaming_projects'].append('star_atlas')
        classification['is_solana_focused'] = True

        item.raw_data.update({
            'blockchain_classification': classification,
            'source_type': 'gaming_project_official',
            'primary_network': 'solana',
            'gaming_project': 'star_atlas'
        })

        return item


# Scraper registry mapping source names to scraper classes
GAMING_SCRAPERS = {
    # Mainstream crypto news with gaming focus
    'coindesk-gaming': CoinDeskGamingScraper,
    'decrypt-gaming': DecryptGamingScraper,
    'theblock-gaming': TheBlockGamingScraper,
    'cointelegraph-gaming': CoinTelegraphGamingScraper,
    'beincrypto-gaming': BeInCryptoGamingScraper,

    # Gaming-specific news sources
    'gamesbeat-web3': GamesBeatWeb3Scraper,
    'nft-gamer': NFTGamerScraper,
    'play-to-earn': PlayToEarnScraper,
    'dappradar': DappRadarScraper,
    'gam3s': Gam3sScraper,
    'chainplay': ChainPlayScraper,
    'playtoearn-com': PlayToEarnComScraper,
    'gamefi-to': GameFiToScraper,
    'gamefi-org': GameFiOrgScraper,

    # Solana ecosystem sources
    'solana-news': SolanaNewsScraper,
    'magic-eden-blog': MagicEdenBlogScraper,
    'solana-labs-blog': SolanaLabsBlogScraper,
    'solana-gaming-discord': SolanaGamingDiscordScraper,
    'star-atlas': StarAtlasScraper
}


def get_scraper_for_source(source_name: str, source_id: int) -> BaseScraper:
    """Get appropriate scraper for a source"""
    scraper_class = GAMING_SCRAPERS.get(source_name)
    if scraper_class:
        return scraper_class(source_id)
    else:
        # Default to RSS scraper
        logger.warning(f"No specific scraper for {source_name}, using default RSS scraper")
        return RSSFeedScraper(source_id, {})


async def scrape_all_sources() -> Dict[str, List[NewsItem]]:
    """Scrape all configured gaming news sources"""
    from models.base import SessionLocal
    from models.gaming import Source
    
    results = {}
    db = SessionLocal()
    
    try:
        # Get all active sources
        sources = db.query(Source).filter(Source.is_active == True).all()
        
        for source in sources:
            logger.info(f"Scraping source: {source.name}")
            
            try:
                # Get appropriate scraper
                scraper = get_scraper_for_source(source.slug, source.id)
                
                # Scrape articles
                async with scraper:
                    articles = await scraper.scrape()
                    results[source.slug] = articles
                    
                logger.info(f"Scraped {len(articles)} articles from {source.name}")
                
            except Exception as e:
                logger.error(f"Error scraping {source.name}: {e}")
                results[source.slug] = []
    
    finally:
        db.close()
    
    return results


async def save_scraped_articles(scraped_data: Dict[str, List[NewsItem]]) -> int:
    """Save scraped articles to database"""
    from models.base import SessionLocal
    from models.gaming import Article, Source
    
    db = SessionLocal()
    saved_count = 0
    
    try:
        for source_slug, articles in scraped_data.items():
            # Get source
            source = db.query(Source).filter(Source.slug == source_slug).first()
            if not source:
                logger.warning(f"Source {source_slug} not found")
                continue
            
            for news_item in articles:
                # Check if article already exists
                existing = db.query(Article).filter(
                    Article.url == news_item.url
                ).first()

                if existing:
                    logger.debug(f"Article already exists: {news_item.title}")
                    continue

                # Enhanced classification and entity recognition
                try:
                    from .entity_recognition import entity_engine

                    blockchain_classification = blockchain_classifier.classify_content(
                        news_item.title, news_item.content or "", news_item.summary or ""
                    )

                    entity_analysis = entity_engine.analyze_content(
                        news_item.title, news_item.content or "", news_item.summary or ""
                    )

                    # Enhanced raw_data with all analysis
                    enhanced_raw_data = news_item.raw_data.copy() if news_item.raw_data else {}
                    enhanced_raw_data.update({
                        'blockchain_classification': blockchain_classification,
                        'entity_analysis': entity_analysis,
                        'processing_timestamp': datetime.now().isoformat(),
                        'phase_4_enhanced': True
                    })

                except Exception as e:
                    logger.warning(f"Error in enhanced classification: {e}")
                    enhanced_raw_data = news_item.raw_data or {}

                # Create new article
                article = Article(
                    title=news_item.title,
                    content=news_item.content,
                    summary=news_item.summary,
                    url=news_item.url,
                    author=news_item.author,
                    published_at=news_item.published_at or datetime.now(),
                    source_id=source.id,
                    keywords=news_item.keywords,
                    tags=enhanced_raw_data.get('tags', []),
                    sentiment_score=enhanced_raw_data.get('sentiment_score', 0.0),
                    extra_metadata=enhanced_raw_data
                )

                db.add(article)
                saved_count += 1
        
        db.commit()
        logger.info(f"Saved {saved_count} new articles to database")
        
    except Exception as e:
        logger.error(f"Error saving articles: {e}")
        db.rollback()
    finally:
        db.close()
    
    return saved_count
