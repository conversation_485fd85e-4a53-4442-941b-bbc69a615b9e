"""
Twitter Gaming News Scraper
Collects gaming-related tweets using Twitter API v2
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from config.settings import get_settings

logger = logging.getLogger(__name__)


@dataclass
class TwitterPost:
    """Twitter post data structure"""
    id: str
    text: str
    author_username: str
    author_name: str
    created_at: datetime
    public_metrics: Dict[str, int]
    url: str
    hashtags: List[str]
    mentions: List[str]
    is_gaming_related: bool = False
    sentiment_score: float = 0.0


class TwitterGamingScraper:
    """Twitter scraper for gaming-related content"""
    
    def __init__(self):
        self.settings = get_settings()
        self.bearer_token = self.settings.social_media.twitter_bearer_token
        self.collection_interval = self.settings.social_media_interval  # 4 hours
        self.last_collection_time = None
        
        # Gaming-related keywords and hashtags
        self.gaming_keywords = [
            "web3 gaming", "blockchain gaming", "crypto gaming", "GameFi",
            "play to earn", "P2E", "NFT gaming", "metaverse gaming",
            "gaming tokens", "gaming NFTs", "blockchain games"
        ]
        
        self.gaming_hashtags = [
            "#web3gaming", "#blockchaingaming", "#cryptogaming", "#GameFi",
            "#PlayToEarn", "#P2E", "#NFTgaming", "#metaverse",
            "#gamingtokens", "#gamingNFTs", "#blockchaingames"
        ]
        
        # Gaming project handles to monitor
        self.gaming_accounts = [
            "AxieInfinity", "TheSandboxGame", "decentraland",
            "0xPolygonGaming", "Immutable", "gam3sdotgg",
            "chainplay_gg", "playtoearn_com"
        ]
    
    async def should_collect(self) -> bool:
        """Check if it's time to collect data based on 4-hour interval"""
        if not self.last_collection_time:
            return True
        
        time_since_last = datetime.now() - self.last_collection_time
        return time_since_last.total_seconds() >= self.collection_interval
    
    async def collect_gaming_tweets(self) -> List[TwitterPost]:
        """Collect gaming-related tweets"""
        if not await self.should_collect():
            logger.info(f"⏰ Skipping Twitter collection - next collection in {self._time_until_next_collection()}")
            return []
        
        if not self.bearer_token:
            logger.warning("⚠️ Twitter Bearer Token not configured - skipping Twitter collection")
            return []
        
        logger.info("🐦 Starting Twitter gaming content collection...")
        
        try:
            # Note: This is a placeholder implementation
            # In a real implementation, you would use tweepy or requests to call Twitter API v2
            
            # Placeholder for Twitter API calls
            tweets = await self._fetch_tweets_placeholder()
            
            # Filter and process tweets
            gaming_tweets = []
            for tweet_data in tweets:
                if self._is_gaming_related(tweet_data.get('text', '')):
                    tweet = self._process_tweet(tweet_data)
                    gaming_tweets.append(tweet)
            
            self.last_collection_time = datetime.now()
            logger.info(f"✅ Collected {len(gaming_tweets)} gaming-related tweets")
            
            return gaming_tweets
            
        except Exception as e:
            logger.error(f"❌ Error collecting Twitter data: {e}")
            return []
    
    async def _fetch_tweets_placeholder(self) -> List[Dict[str, Any]]:
        """Placeholder for actual Twitter API calls"""
        # This would be replaced with actual Twitter API v2 calls using tweepy
        # Example implementation would include:
        # 1. Search for gaming keywords
        # 2. Get tweets from gaming accounts
        # 3. Filter by date (last 4 hours)
        # 4. Handle rate limiting
        
        logger.info("📝 Placeholder: Would fetch tweets from Twitter API v2")
        return []
    
    def _is_gaming_related(self, text: str) -> bool:
        """Check if tweet text is gaming-related"""
        text_lower = text.lower()
        
        # Check for gaming keywords
        for keyword in self.gaming_keywords:
            if keyword.lower() in text_lower:
                return True
        
        # Check for gaming hashtags
        for hashtag in self.gaming_hashtags:
            if hashtag.lower() in text_lower:
                return True
        
        return False
    
    def _process_tweet(self, tweet_data: Dict[str, Any]) -> TwitterPost:
        """Process raw tweet data into TwitterPost object"""
        # This would process actual Twitter API response
        # Placeholder implementation
        return TwitterPost(
            id=tweet_data.get('id', ''),
            text=tweet_data.get('text', ''),
            author_username=tweet_data.get('author_username', ''),
            author_name=tweet_data.get('author_name', ''),
            created_at=datetime.now(),
            public_metrics=tweet_data.get('public_metrics', {}),
            url=f"https://twitter.com/{tweet_data.get('author_username', '')}/status/{tweet_data.get('id', '')}",
            hashtags=tweet_data.get('hashtags', []),
            mentions=tweet_data.get('mentions', []),
            is_gaming_related=True
        )
    
    def _time_until_next_collection(self) -> str:
        """Get human-readable time until next collection"""
        if not self.last_collection_time:
            return "now"
        
        next_collection = self.last_collection_time + timedelta(seconds=self.collection_interval)
        time_diff = next_collection - datetime.now()
        
        if time_diff.total_seconds() <= 0:
            return "now"
        
        hours = int(time_diff.total_seconds() // 3600)
        minutes = int((time_diff.total_seconds() % 3600) // 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m"
        else:
            return f"{minutes}m"
    
    async def get_collection_status(self) -> Dict[str, Any]:
        """Get current collection status"""
        return {
            "service": "Twitter Gaming Scraper",
            "collection_interval_hours": self.collection_interval / 3600,
            "last_collection": self.last_collection_time.isoformat() if self.last_collection_time else None,
            "next_collection": self._time_until_next_collection(),
            "bearer_token_configured": bool(self.bearer_token),
            "gaming_keywords_count": len(self.gaming_keywords),
            "gaming_accounts_count": len(self.gaming_accounts)
        }


# Global instance
twitter_scraper = TwitterGamingScraper()
