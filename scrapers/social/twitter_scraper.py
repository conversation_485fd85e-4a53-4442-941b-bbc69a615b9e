"""
Twitter Gaming News Scraper
Collects gaming-related tweets using Twitter API v2
"""
import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import json

from config.settings import get_settings

logger = logging.getLogger(__name__)


@dataclass
class TwitterPost:
    """Twitter post data structure"""
    id: str
    text: str
    author_username: str
    author_name: str
    created_at: datetime
    public_metrics: Dict[str, int]
    url: str
    hashtags: List[str]
    mentions: List[str]
    is_gaming_related: bool = False
    sentiment_score: float = 0.0


class TwitterGamingScraper:
    """Twitter scraper for gaming-related content"""
    
    def __init__(self):
        self.settings = get_settings()
        self.bearer_token = self.settings.social_media.twitter_bearer_token
        self.collection_interval = self.settings.social_media_interval  # 4 hours
        self.last_collection_time = None
        self.session: Optional[aiohttp.ClientSession] = None

        # Twitter API v2 base URL
        self.base_url = "https://api.twitter.com/2"

        # Enhanced gaming-related keywords and hashtags based on user specifications
        self.gaming_keywords = [
            "p2e", "playtoearn", "web3gaming", "cryptogaming",
            "blockchaingaming", "nftgaming", "gamefi", "metaverse",
            "solana gaming", "ethereum gaming", "ronin network",
            "gaming guild", "blockchain games", "crypto games",
            # Specific gaming projects from user requirements
            "race poker", "axie infinity", "gala games", "honeyland",
            "sunflowerland", "hamster kombat", "decentraland", "mayg", "star atlas"
        ]

        self.gaming_hashtags = [
            "#p2e", "#playtoearn", "#web3gaming", "#cryptogaming", "#GameFi",
            "#blockchaingaming", "#nftgaming", "#metaverse", "#solana",
            "#ethereum", "#ronin", "#guild", "#axieinfinity", "#sandbox",
            "#racepoker", "#galagames", "#honeyland", "#sunflowerland",
            "#hamsterkombat", "#decentraland", "#mayg", "#staratlas"
        ]

        # Gaming project handles and influencers to monitor (user-specified)
        self.gaming_accounts = [
            # Gaming Projects
            "AxieInfinity", "GalaGames", "decentraland", "HoneylandGame",
            "SunflowerLand", "hamster_kombat", "staratlas", "MAYGOfficial",
            # Gaming Influencers/KOLs
            "Filbertsteiner", "AmandaZhu_", "jihoz_axie", "sinjinMAYG",
            # Additional gaming ecosystem accounts
            "YieldGuildGames", "MeritCircle_IO", "PlayToEarnGames",
            "0xPolygonGaming", "Immutable", "gam3sdotgg", "chainplay_gg"
        ]

        # Content type filters for focused scraping
        self.content_types = [
            "news announcement", "game update", "game release",
            "partnership announcement", "collaboration", "integration"
        ]
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    async def _ensure_session(self):
        """Ensure aiohttp session is created"""
        if not self.session:
            headers = {
                "Authorization": f"Bearer {self.bearer_token}",
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(headers=headers, timeout=timeout)

    async def should_collect(self) -> bool:
        """Check if it's time to collect data based on 4-hour interval"""
        if not self.last_collection_time:
            return True

        time_since_last = datetime.now() - self.last_collection_time
        return time_since_last.total_seconds() >= self.collection_interval
    
    async def collect_gaming_posts(self, force: bool = False) -> List[TwitterPost]:
        """Collect gaming-related tweets using Twitter API v2"""
        if not force and not await self.should_collect():
            logger.info(f"⏰ Skipping Twitter collection - next collection in {self._time_until_next_collection()}")
            return []

        if not self.bearer_token:
            logger.warning("⚠️ Twitter Bearer Token not configured - skipping Twitter collection")
            return []

        logger.info("🐦 Starting Twitter gaming content collection...")

        try:
            await self._ensure_session()
            all_tweets = []

            # 1. Search for gaming keywords
            for keyword in self.gaming_keywords[:5]:  # Limit to avoid rate limits
                tweets = await self._search_tweets(keyword, max_results=20)
                all_tweets.extend(tweets)
                await asyncio.sleep(1)  # Rate limiting

            # 2. Get tweets from gaming accounts
            for account in self.gaming_accounts[:10]:  # Limit to avoid rate limits
                tweets = await self._get_user_tweets(account, max_results=10)
                all_tweets.extend(tweets)
                await asyncio.sleep(1)  # Rate limiting

            # Remove duplicates and filter
            unique_tweets = {}
            gaming_tweets = []

            for tweet_data in all_tweets:
                tweet_id = tweet_data.get('id')
                if tweet_id and tweet_id not in unique_tweets:
                    unique_tweets[tweet_id] = True
                    if self._is_gaming_related(tweet_data.get('text', '')):
                        tweet = self._process_tweet(tweet_data)
                        gaming_tweets.append(tweet)

            self.last_collection_time = datetime.now()
            logger.info(f"✅ Collected {len(gaming_tweets)} unique gaming-related tweets")

            return gaming_tweets

        except Exception as e:
            logger.error(f"❌ Error collecting Twitter data: {e}")
            return []
    
    async def _search_tweets(self, query: str, max_results: int = 50) -> List[Dict[str, Any]]:
        """Search for tweets using Twitter API v2"""
        try:
            # Calculate time range (last 4 hours)
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=4)

            params = {
                "query": f"{query} -is:retweet lang:en",  # Exclude retweets, English only
                "max_results": min(max_results, 100),  # API limit
                "tweet.fields": "created_at,author_id,public_metrics,context_annotations,lang",
                "user.fields": "username,name,verified,public_metrics",
                "expansions": "author_id",
                "start_time": start_time.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
                "end_time": end_time.strftime("%Y-%m-%dT%H:%M:%S.000Z")
            }

            url = f"{self.base_url}/tweets/search/recent"

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._process_api_response(data)
                elif response.status == 429:
                    logger.warning("Twitter API rate limit exceeded")
                    return []
                else:
                    logger.error(f"Twitter API error: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error searching tweets for '{query}': {e}")
            return []

    async def _get_user_tweets(self, username: str, max_results: int = 20) -> List[Dict[str, Any]]:
        """Get recent tweets from a specific user"""
        try:
            # First get user ID
            user_url = f"{self.base_url}/users/by/username/{username}"
            user_params = {"user.fields": "id,username,name,verified,public_metrics"}

            async with self.session.get(user_url, params=user_params) as response:
                if response.status != 200:
                    logger.error(f"Failed to get user ID for {username}: {response.status}")
                    return []

                user_data = await response.json()
                user_id = user_data.get("data", {}).get("id")

                if not user_id:
                    logger.error(f"No user ID found for {username}")
                    return []

            # Get user's recent tweets
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=4)

            tweets_params = {
                "max_results": min(max_results, 100),
                "tweet.fields": "created_at,author_id,public_metrics,context_annotations,lang",
                "user.fields": "username,name,verified,public_metrics",
                "expansions": "author_id",
                "exclude": "retweets,replies",  # Original tweets only
                "start_time": start_time.strftime("%Y-%m-%dT%H:%M:%S.000Z"),
                "end_time": end_time.strftime("%Y-%m-%dT%H:%M:%S.000Z")
            }

            tweets_url = f"{self.base_url}/users/{user_id}/tweets"

            async with self.session.get(tweets_url, params=tweets_params) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._process_api_response(data)
                elif response.status == 429:
                    logger.warning(f"Rate limit exceeded for user {username}")
                    return []
                else:
                    logger.error(f"Error getting tweets for {username}: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error getting user tweets for {username}: {e}")
            return []

    def _process_api_response(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Process Twitter API v2 response data"""
        tweets = data.get("data", [])
        users = {user["id"]: user for user in data.get("includes", {}).get("users", [])}

        processed_tweets = []
        for tweet in tweets:
            author_id = tweet.get("author_id")
            author = users.get(author_id, {})

            processed_tweet = {
                "id": tweet.get("id"),
                "text": tweet.get("text"),
                "author_id": author_id,
                "author_username": author.get("username", ""),
                "author_name": author.get("name", ""),
                "created_at": tweet.get("created_at"),
                "public_metrics": tweet.get("public_metrics", {}),
                "lang": tweet.get("lang", "en")
            }
            processed_tweets.append(processed_tweet)

        return processed_tweets
    
    def _is_gaming_related(self, text: str) -> bool:
        """Check if tweet text is gaming-related"""
        text_lower = text.lower()

        # Check for gaming keywords
        for keyword in self.gaming_keywords:
            if keyword.lower() in text_lower:
                return True

        # Check for gaming hashtags
        for hashtag in self.gaming_hashtags:
            if hashtag.lower() in text_lower:
                return True

        # Check for content type relevance (news, updates, partnerships)
        for content_type in self.content_types:
            if content_type.lower() in text_lower:
                return True

        return False

    def _calculate_relevance_score(self, text: str, metrics: Dict[str, Any]) -> float:
        """Calculate relevance score for gaming content prioritization"""
        score = 0.0
        text_lower = text.lower()

        # Base score for gaming keywords
        for keyword in self.gaming_keywords:
            if keyword.lower() in text_lower:
                score += 1.0

        # Bonus for specific gaming projects
        gaming_projects = ["race poker", "axie infinity", "gala games", "honeyland",
                          "sunflowerland", "hamster kombat", "decentraland", "mayg", "star atlas"]
        for project in gaming_projects:
            if project.lower() in text_lower:
                score += 2.0

        # Bonus for content types we're focusing on
        for content_type in self.content_types:
            if content_type.lower() in text_lower:
                score += 1.5

        # Engagement metrics boost
        if metrics:
            retweet_count = metrics.get('retweet_count', 0)
            like_count = metrics.get('like_count', 0)
            reply_count = metrics.get('reply_count', 0)

            # Normalize engagement (log scale to prevent outliers from dominating)
            import math
            engagement_score = math.log(1 + retweet_count + like_count + reply_count) / 10
            score += engagement_score

        return score
    
    def _process_tweet(self, tweet_data: Dict[str, Any]) -> TwitterPost:
        """Process raw tweet data into TwitterPost object"""
        text = tweet_data.get('text', '')
        created_at_str = tweet_data.get('created_at')

        # Parse created_at timestamp
        created_at = datetime.now()
        if created_at_str:
            try:
                created_at = datetime.fromisoformat(created_at_str.replace('Z', '+00:00'))
            except:
                pass

        # Extract hashtags and mentions from text
        hashtags = [word for word in text.split() if word.startswith('#')]
        mentions = [word for word in text.split() if word.startswith('@')]

        return TwitterPost(
            id=tweet_data.get('id', ''),
            text=text,
            author_username=tweet_data.get('author_username', ''),
            author_name=tweet_data.get('author_name', ''),
            created_at=created_at,
            public_metrics=tweet_data.get('public_metrics', {}),
            url=f"https://twitter.com/{tweet_data.get('author_username', '')}/status/{tweet_data.get('id', '')}",
            hashtags=hashtags,
            mentions=mentions,
            is_gaming_related=True
        )
    
    def _time_until_next_collection(self) -> str:
        """Get human-readable time until next collection"""
        if not self.last_collection_time:
            return "now"
        
        next_collection = self.last_collection_time + timedelta(seconds=self.collection_interval)
        time_diff = next_collection - datetime.now()
        
        if time_diff.total_seconds() <= 0:
            return "now"
        
        hours = int(time_diff.total_seconds() // 3600)
        minutes = int((time_diff.total_seconds() % 3600) // 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m"
        else:
            return f"{minutes}m"
    
    async def collect_gaming_tweets(self) -> List[TwitterPost]:
        """Backward compatibility alias for collect_gaming_posts"""
        return await self.collect_gaming_posts()

    async def get_collection_status(self) -> Dict[str, Any]:
        """Get current collection status"""
        return {
            "service": "Twitter Gaming Scraper",
            "collection_interval_hours": self.collection_interval / 3600,
            "last_collection": self.last_collection_time.isoformat() if self.last_collection_time else None,
            "next_collection": self._time_until_next_collection(),
            "bearer_token_configured": bool(self.bearer_token),
            "gaming_keywords": list(self.gaming_keywords),
            "gaming_accounts": list(self.gaming_accounts),
            "content_types": self.content_types,
            "gaming_projects": self.gaming_projects,
            "gaming_influencers": self.gaming_influencers
        }


# Global instance
twitter_scraper = TwitterGamingScraper()
