"""
Reddit Gaming News Scraper
Collects gaming-related posts from relevant subreddits using PRAW
"""
import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import json

from config.settings import get_settings

logger = logging.getLogger(__name__)


@dataclass
class RedditPost:
    """Reddit post data structure"""
    id: str
    title: str
    text: str
    author: str
    subreddit: str
    created_at: datetime
    score: int
    num_comments: int
    url: str
    permalink: str
    flair: Optional[str]
    is_gaming_related: bool = False
    sentiment_score: float = 0.0


class RedditGamingScraper:
    """Reddit scraper for gaming-related content"""
    
    def __init__(self):
        self.settings = get_settings()
        self.client_id = self.settings.social_media.reddit_client_id
        self.client_secret = self.settings.social_media.reddit_client_secret
        self.user_agent = "WebThreeGameScraper:v1.0 (by /u/kumabearPracticeDune)"
        self.collection_interval = self.settings.social_media_interval  # 4 hours
        self.last_collection_time = None
        self.session: Optional[aiohttp.ClientSession] = None
        self.access_token: Optional[str] = None

        # Reddit API base URL
        self.base_url = "https://oauth.reddit.com"
        self.auth_url = "https://www.reddit.com/api/v1/access_token"

        # Gaming-related subreddits to monitor (user-specified priorities)
        self.gaming_subreddits = [
            # Gaming-specific subreddits (high priority)
            "GameFi", "NFTGames", "PlayToEarn", "Web3Gaming", "BlockchainGaming", "CryptoGaming",
            # Project-specific subreddits (user-specified gaming projects)
            "AxieInfinity", "TheSandboxGame", "decentraland", "GalaGames",
            "HoneylandGame", "SunflowerLand", "HamsterKombat", "StarAtlas",
            # General crypto subreddits with gaming content
            "CryptoCurrency", "ethereum", "solana", "polygon", "BinanceSmartChain",
            "Sui", "TONBlockchain", "avalanche", "ImmutableX",
            # Gaming ecosystem subreddits
            "YieldGuildGames", "MeritCircle", "GuildFi", "MetaverseInvestor",
            "NFTMarketplace", "OpenSea", "DappRadar", "gam3s", "chainplay"
        ]

        # Gaming-related keywords for filtering posts (enhanced with user specifications)
        self.gaming_keywords = [
            "p2e", "play to earn", "playtoearn", "web3 gaming", "crypto gaming",
            "blockchain gaming", "nft gaming", "gamefi", "metaverse", "guild",
            # Specific gaming projects from user requirements
            "race poker", "axie infinity", "gala games", "honeyland",
            "sunflowerland", "hamster kombat", "decentraland", "mayg", "star atlas",
            # Gaming ecosystem terms
            "gaming nft", "gaming token", "gaming protocol", "gaming dao",
            "yield guild", "merit circle", "gaming investment", "gaming partnership",
            # Blockchain gaming terms
            "solana gaming", "ethereum gaming", "polygon gaming", "bsc gaming",
            "sui gaming", "ton gaming", "immutable gaming"
        ]

        # Content filtering settings (user-specified)
        self.min_upvote_threshold = 5  # Minimum upvotes for quality content
        self.min_comment_upvotes = 1000  # Minimum upvotes for comment scraping

        # Post types to scrape (user-specified)
        self.scrape_text_posts = True
        self.scrape_media_posts = True  # image/video posts
        self.scrape_high_upvote_comments = True  # comments with >1000 upvotes
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._ensure_session()
        await self._authenticate()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    async def _ensure_session(self):
        """Ensure aiohttp session is created"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)

    async def _authenticate(self):
        """Authenticate with Reddit API to get access token"""
        if not self.client_id or not self.client_secret:
            logger.error("Reddit API credentials not configured")
            return

        try:
            auth = aiohttp.BasicAuth(self.client_id, self.client_secret)
            headers = {"User-Agent": self.user_agent}
            data = {"grant_type": "client_credentials"}

            async with self.session.post(self.auth_url, auth=auth, headers=headers, data=data) as response:
                if response.status == 200:
                    auth_data = await response.json()
                    self.access_token = auth_data.get("access_token")
                    logger.info("Successfully authenticated with Reddit API")
                else:
                    logger.error(f"Failed to authenticate with Reddit API: {response.status}")
        except Exception as e:
            logger.error(f"Error authenticating with Reddit API: {e}")

    async def should_collect(self) -> bool:
        """Check if it's time to collect data based on 4-hour interval"""
        if not self.last_collection_time:
            return True

        time_since_last = datetime.now() - self.last_collection_time
        return time_since_last.total_seconds() >= self.collection_interval
    
    async def collect_gaming_posts(self) -> List[RedditPost]:
        """Collect gaming-related Reddit posts using Reddit API"""
        if not await self.should_collect():
            logger.info(f"⏰ Skipping Reddit collection - next collection in {self._time_until_next_collection()}")
            return []

        if not self.client_id or not self.client_secret:
            logger.warning("⚠️ Reddit API credentials not configured - skipping Reddit collection")
            return []

        logger.info("🔴 Starting Reddit gaming content collection...")

        try:
            await self._ensure_session()
            await self._authenticate()

            if not self.access_token:
                logger.error("Failed to authenticate with Reddit API")
                return []

            all_posts = []

            # Collect posts from gaming subreddits
            for subreddit in self.gaming_subreddits[:15]:  # Limit to avoid rate limits
                posts = await self._get_subreddit_posts(subreddit, limit=25)
                all_posts.extend(posts)
                await asyncio.sleep(1)  # Rate limiting

            # Remove duplicates and filter
            unique_posts = {}
            gaming_posts = []

            for post_data in all_posts:
                post_id = post_data.get('id')
                if post_id and post_id not in unique_posts:
                    unique_posts[post_id] = True
                    if self._is_gaming_related(post_data.get('title', ''), post_data.get('selftext', '')):
                        post = self._process_post(post_data)
                        gaming_posts.append(post)

            self.last_collection_time = datetime.now()
            logger.info(f"✅ Collected {len(gaming_posts)} unique gaming-related Reddit posts")

            return gaming_posts

        except Exception as e:
            logger.error(f"❌ Error collecting Reddit data: {e}")
            return []
    
    async def _get_subreddit_posts(self, subreddit: str, limit: int = 25) -> List[Dict[str, Any]]:
        """Get posts from a specific subreddit"""
        if not self.access_token:
            return []

        try:
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "User-Agent": self.user_agent
            }

            # Get hot posts from subreddit
            url = f"{self.base_url}/r/{subreddit}/hot"
            params = {"limit": limit, "raw_json": 1}

            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    posts = data.get("data", {}).get("children", [])

                    # Filter posts from last 4 hours
                    cutoff_time = datetime.now() - timedelta(hours=4)
                    recent_posts = []

                    for post in posts:
                        post_data = post.get("data", {})
                        created_utc = post_data.get("created_utc", 0)
                        post_time = datetime.fromtimestamp(created_utc)

                        # Apply user-specified filtering criteria
                        if (post_time >= cutoff_time and
                            self._meets_quality_threshold(post_data) and
                            self._is_gaming_related(
                                post_data.get('title', ''),
                                post_data.get('selftext', '')
                            )):
                            recent_posts.append(post_data)

                    logger.info(f"Retrieved {len(recent_posts)} recent posts from r/{subreddit}")
                    return recent_posts

                elif response.status == 429:
                    logger.warning(f"Rate limit exceeded for r/{subreddit}")
                    return []
                else:
                    logger.error(f"Error getting posts from r/{subreddit}: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Error fetching posts from r/{subreddit}: {e}")
            return []
    
    def _is_gaming_related(self, title: str, text: str) -> bool:
        """Check if Reddit post is gaming-related"""
        content = f"{title} {text}".lower()

        # Check for gaming keywords
        for keyword in self.gaming_keywords:
            if keyword.lower() in content:
                return True

        return False

    def _meets_quality_threshold(self, post_data: Dict[str, Any]) -> bool:
        """Check if post meets quality thresholds (user-specified criteria)"""
        score = post_data.get('score', 0)
        post_hint = post_data.get('post_hint', '')

        # Check minimum upvote threshold
        if score < self.min_upvote_threshold:
            return False

        # Check post type preferences
        is_text_post = post_hint == '' or post_hint == 'self'
        is_media_post = post_hint in ['image', 'video', 'link']

        if is_text_post and not self.scrape_text_posts:
            return False

        if is_media_post and not self.scrape_media_posts:
            return False

        return True

    def _calculate_relevance_score(self, title: str, text: str, score: int) -> float:
        """Calculate relevance score for gaming content prioritization"""
        relevance_score = 0.0
        content = f"{title} {text}".lower()

        # Base score for gaming keywords
        for keyword in self.gaming_keywords:
            if keyword.lower() in content:
                relevance_score += 1.0

        # Bonus for specific gaming projects
        gaming_projects = ["race poker", "axie infinity", "gala games", "honeyland",
                          "sunflowerland", "hamster kombat", "decentraland", "mayg", "star atlas"]
        for project in gaming_projects:
            if project.lower() in content:
                relevance_score += 2.0

        # Bonus for high-quality content (upvotes)
        import math
        upvote_score = math.log(1 + max(0, score)) / 10
        relevance_score += upvote_score

        return relevance_score
    
    def _process_post(self, post_data: Dict[str, Any]) -> RedditPost:
        """Process raw Reddit post data into RedditPost object with enhanced filtering"""
        created_utc = post_data.get('created_utc', 0)
        created_at = datetime.fromtimestamp(created_utc) if created_utc else datetime.now()

        title = post_data.get('title', '')
        text = post_data.get('selftext', '')
        score = post_data.get('score', 0)

        # Calculate relevance score using user-specified criteria
        relevance_score = self._calculate_relevance_score(title, text, score)

        return RedditPost(
            id=post_data.get('id', ''),
            title=title,
            text=text,
            author=post_data.get('author', ''),
            subreddit=post_data.get('subreddit', ''),
            created_at=created_at,
            score=score,
            num_comments=post_data.get('num_comments', 0),
            url=post_data.get('url', ''),
            permalink=f"https://reddit.com{post_data.get('permalink', '')}",
            flair=post_data.get('link_flair_text'),
            is_gaming_related=True,
            relevance_score=relevance_score
        )
    
    def _time_until_next_collection(self) -> str:
        """Get human-readable time until next collection"""
        if not self.last_collection_time:
            return "now"
        
        next_collection = self.last_collection_time + timedelta(seconds=self.collection_interval)
        time_diff = next_collection - datetime.now()
        
        if time_diff.total_seconds() <= 0:
            return "now"
        
        hours = int(time_diff.total_seconds() // 3600)
        minutes = int((time_diff.total_seconds() % 3600) // 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m"
        else:
            return f"{minutes}m"
    
    async def get_collection_status(self) -> Dict[str, Any]:
        """Get current collection status"""
        return {
            "service": "Reddit Gaming Scraper",
            "collection_interval_hours": self.collection_interval / 3600,
            "last_collection": self.last_collection_time.isoformat() if self.last_collection_time else None,
            "next_collection": self._time_until_next_collection(),
            "credentials_configured": bool(self.client_id and self.client_secret),
            "gaming_subreddits_count": len(self.gaming_subreddits),
            "gaming_keywords_count": len(self.gaming_keywords)
        }


# Global instance
reddit_scraper = RedditGamingScraper()
