"""
Reddit Gaming News Scraper
Collects gaming-related posts from relevant subreddits
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from config.settings import get_settings

logger = logging.getLogger(__name__)


@dataclass
class RedditPost:
    """Reddit post data structure"""
    id: str
    title: str
    text: str
    author: str
    subreddit: str
    created_at: datetime
    score: int
    num_comments: int
    url: str
    permalink: str
    flair: Optional[str]
    is_gaming_related: bool = False
    sentiment_score: float = 0.0


class RedditGamingScraper:
    """Reddit scraper for gaming-related content"""
    
    def __init__(self):
        self.settings = get_settings()
        self.client_id = self.settings.social_media.reddit_client_id
        self.client_secret = self.settings.social_media.reddit_client_secret
        self.user_agent = self.settings.social_media.reddit_user_agent
        self.collection_interval = self.settings.social_media_interval  # 4 hours
        self.last_collection_time = None
        
        # Gaming-related subreddits to monitor
        self.gaming_subreddits = [
            "CryptoCurrency",
            "ethereum",
            "NFTGames",
            "GameFi",
            "PlayToEarn",
            "Web3Gaming",
            "BlockchainGaming",
            "CryptoGaming",
            "AxieInfinity",
            "TheSandboxGame",
            "decentraland",
            "ImmutableX",
            "polygon",
            "solana",
            "gaming",
            "Games",
            "pcgaming",
            "truegaming"
        ]
        
        # Gaming-related keywords for filtering
        self.gaming_keywords = [
            "web3 gaming", "blockchain gaming", "crypto gaming", "GameFi",
            "play to earn", "P2E", "NFT gaming", "metaverse gaming",
            "gaming tokens", "gaming NFTs", "blockchain games",
            "axie infinity", "sandbox", "decentraland", "immutable",
            "polygon gaming", "solana gaming", "ethereum gaming"
        ]
    
    async def should_collect(self) -> bool:
        """Check if it's time to collect data based on 4-hour interval"""
        if not self.last_collection_time:
            return True
        
        time_since_last = datetime.now() - self.last_collection_time
        return time_since_last.total_seconds() >= self.collection_interval
    
    async def collect_gaming_posts(self) -> List[RedditPost]:
        """Collect gaming-related Reddit posts"""
        if not await self.should_collect():
            logger.info(f"⏰ Skipping Reddit collection - next collection in {self._time_until_next_collection()}")
            return []
        
        if not self.client_id or not self.client_secret:
            logger.warning("⚠️ Reddit API credentials not configured - skipping Reddit collection")
            return []
        
        logger.info("🔴 Starting Reddit gaming content collection...")
        
        try:
            # Note: This is a placeholder implementation
            # In a real implementation, you would use praw (Python Reddit API Wrapper)
            
            # Placeholder for Reddit API calls
            posts = await self._fetch_posts_placeholder()
            
            # Filter and process posts
            gaming_posts = []
            for post_data in posts:
                if self._is_gaming_related(post_data.get('title', ''), post_data.get('text', '')):
                    post = self._process_post(post_data)
                    gaming_posts.append(post)
            
            self.last_collection_time = datetime.now()
            logger.info(f"✅ Collected {len(gaming_posts)} gaming-related Reddit posts")
            
            return gaming_posts
            
        except Exception as e:
            logger.error(f"❌ Error collecting Reddit data: {e}")
            return []
    
    async def _fetch_posts_placeholder(self) -> List[Dict[str, Any]]:
        """Placeholder for actual Reddit API calls"""
        # This would be replaced with actual Reddit API calls using praw
        # Example implementation would include:
        # 1. Iterate through gaming subreddits
        # 2. Get hot/new posts from each subreddit
        # 3. Filter by date (last 4 hours)
        # 4. Handle rate limiting
        # 5. Extract post data including comments
        
        logger.info("📝 Placeholder: Would fetch posts from Reddit API")
        return []
    
    def _is_gaming_related(self, title: str, text: str) -> bool:
        """Check if Reddit post is gaming-related"""
        content = f"{title} {text}".lower()
        
        # Check for gaming keywords
        for keyword in self.gaming_keywords:
            if keyword.lower() in content:
                return True
        
        return False
    
    def _process_post(self, post_data: Dict[str, Any]) -> RedditPost:
        """Process raw Reddit post data into RedditPost object"""
        # This would process actual Reddit API response
        # Placeholder implementation
        return RedditPost(
            id=post_data.get('id', ''),
            title=post_data.get('title', ''),
            text=post_data.get('selftext', ''),
            author=post_data.get('author', ''),
            subreddit=post_data.get('subreddit', ''),
            created_at=datetime.now(),
            score=post_data.get('score', 0),
            num_comments=post_data.get('num_comments', 0),
            url=post_data.get('url', ''),
            permalink=f"https://reddit.com{post_data.get('permalink', '')}",
            flair=post_data.get('link_flair_text'),
            is_gaming_related=True
        )
    
    def _time_until_next_collection(self) -> str:
        """Get human-readable time until next collection"""
        if not self.last_collection_time:
            return "now"
        
        next_collection = self.last_collection_time + timedelta(seconds=self.collection_interval)
        time_diff = next_collection - datetime.now()
        
        if time_diff.total_seconds() <= 0:
            return "now"
        
        hours = int(time_diff.total_seconds() // 3600)
        minutes = int((time_diff.total_seconds() % 3600) // 60)
        
        if hours > 0:
            return f"{hours}h {minutes}m"
        else:
            return f"{minutes}m"
    
    async def get_collection_status(self) -> Dict[str, Any]:
        """Get current collection status"""
        return {
            "service": "Reddit Gaming Scraper",
            "collection_interval_hours": self.collection_interval / 3600,
            "last_collection": self.last_collection_time.isoformat() if self.last_collection_time else None,
            "next_collection": self._time_until_next_collection(),
            "credentials_configured": bool(self.client_id and self.client_secret),
            "gaming_subreddits_count": len(self.gaming_subreddits),
            "gaming_keywords_count": len(self.gaming_keywords)
        }


# Global instance
reddit_scraper = RedditGamingScraper()
