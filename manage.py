#!/usr/bin/env python3
"""
Management CLI for Web3 Gaming News Tracker
"""
import click
import asyncio
import uvicorn
from sqlalchemy import text
from models.base import engine, create_tables, drop_tables, SessionLocal
from config.settings import get_settings
# Import all models to register them with SQLAlchemy
import models.gaming

settings = get_settings()


@click.group()
def cli():
    """Web3 Gaming News Tracker Management CLI"""
    pass


@cli.group()
def db():
    """Database management commands"""
    pass


@db.command()
def init():
    """Initialize the database"""
    click.echo("Initializing database...")
    try:
        create_tables()
        click.echo("✅ Database initialized successfully!")
    except Exception as e:
        click.echo(f"❌ Error initializing database: {e}")
        raise


@db.command()
def reset():
    """Reset the database (WARNING: This will delete all data)"""
    if click.confirm("This will delete all data. Are you sure?"):
        click.echo("Resetting database...")
        try:
            drop_tables()
            create_tables()
            click.echo("✅ Database reset successfully!")
        except Exception as e:
            click.echo(f"❌ Error resetting database: {e}")
            raise


@db.command()
def test():
    """Test database connection"""
    click.echo("Testing database connection...")
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone()[0] == 1:
                click.echo("✅ Database connection successful!")
            else:
                click.echo("❌ Database connection failed!")
    except Exception as e:
        click.echo(f"❌ Database connection error: {e}")
        raise


@cli.group()
def blockchain():
    """Blockchain management commands"""
    pass


@blockchain.command()
def test_rpc():
    """Test RPC connections"""
    from blockchain.rpc import test_all_connections
    
    click.echo("Testing RPC connections...")
    try:
        results = asyncio.run(test_all_connections())
        for chain, status in results.items():
            if status:
                click.echo(f"✅ {chain}: Connected")
            else:
                click.echo(f"❌ {chain}: Failed")
    except Exception as e:
        click.echo(f"❌ Error testing RPC connections: {e}")
        raise


@blockchain.command()
def sync_chains():
    """Sync blockchain data"""
    from scrapers.blockchain.sync import sync_all_chains
    
    click.echo("Starting blockchain sync...")
    try:
        asyncio.run(sync_all_chains())
        click.echo("✅ Blockchain sync completed!")
    except Exception as e:
        click.echo(f"❌ Error syncing blockchain data: {e}")
        raise


@cli.group()
def scraper():
    """Scraper management commands"""
    pass


@scraper.command()
def start():
    """Start all scrapers"""
    from scrapers.manager import start_all_scrapers
    
    click.echo("Starting all scrapers...")
    try:
        start_all_scrapers()
        click.echo("✅ All scrapers started!")
    except Exception as e:
        click.echo(f"❌ Error starting scrapers: {e}")
        raise


@scraper.command()
def stop():
    """Stop all scrapers"""
    from scrapers.manager import stop_all_scrapers
    
    click.echo("Stopping all scrapers...")
    try:
        stop_all_scrapers()
        click.echo("✅ All scrapers stopped!")
    except Exception as e:
        click.echo(f"❌ Error stopping scrapers: {e}")
        raise


@scraper.command()
@click.argument('source_name')
def test_source(source_name):
    """Test a specific scraper source"""
    from scrapers.manager import test_scraper_source
    
    click.echo(f"Testing scraper source: {source_name}")
    try:
        result = test_scraper_source(source_name)
        if result:
            click.echo(f"✅ {source_name}: Working")
        else:
            click.echo(f"❌ {source_name}: Failed")
    except Exception as e:
        click.echo(f"❌ Error testing {source_name}: {e}")
        raise


@cli.command()
def run_api():
    """Run the API server"""
    click.echo(f"Starting API server on {settings.api.host}:{settings.api.port}")
    uvicorn.run(
        "api.main:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.debug,
        workers=1 if settings.api.debug else settings.api.workers
    )


@cli.command()
def run_worker():
    """Run Celery worker"""
    import subprocess
    
    click.echo("Starting Celery worker...")
    try:
        subprocess.run([
            "celery", "-A", "scrapers.celery_app", "worker",
            "--loglevel=info"
        ])
    except KeyboardInterrupt:
        click.echo("Worker stopped.")


@cli.command()
def run_beat():
    """Run Celery beat scheduler"""
    import subprocess
    
    click.echo("Starting Celery beat scheduler...")
    try:
        subprocess.run([
            "celery", "-A", "scrapers.celery_app", "beat",
            "--loglevel=info"
        ])
    except KeyboardInterrupt:
        click.echo("Beat scheduler stopped.")


@cli.group()
def gaming():
    """Gaming-specific management commands"""
    pass


@gaming.command()
def sync_projects():
    """Sync gaming projects data"""
    from scrapers.gaming.projects import sync_gaming_projects
    
    click.echo("Syncing gaming projects...")
    try:
        asyncio.run(sync_gaming_projects())
        click.echo("✅ Gaming projects synced!")
    except Exception as e:
        click.echo(f"❌ Error syncing gaming projects: {e}")
        raise


@gaming.command()
def sync_nfts():
    """Sync NFT collections data"""
    from scrapers.gaming.nfts import sync_nft_collections
    
    click.echo("Syncing NFT collections...")
    try:
        asyncio.run(sync_nft_collections())
        click.echo("✅ NFT collections synced!")
    except Exception as e:
        click.echo(f"❌ Error syncing NFT collections: {e}")
        raise


@cli.command()
def status():
    """Show system status"""
    click.echo("Web3 Gaming News Tracker Status")
    click.echo("=" * 40)
    
    # Database status
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        click.echo("✅ Database: Connected")
    except:
        click.echo("❌ Database: Disconnected")
    
    # Redis status
    try:
        import redis
        r = redis.from_url(settings.redis.url)
        r.ping()
        click.echo("✅ Redis: Connected")
    except:
        click.echo("❌ Redis: Disconnected")
    
    # API status
    click.echo(f"🔧 API Config: {settings.api.host}:{settings.api.port}")
    click.echo(f"🔧 Environment: {settings.environment}")


if __name__ == "__main__":
    cli()
