import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens if needed
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

// Dashboard API endpoints
export const dashboardAPI = {
  // Get dashboard overview metrics
  getOverview: (hours = 24) => 
    api.get(`/dashboard/overview?hours=${hours}`),

  // Get news analytics data
  getNewsAnalytics: (hours = 24, network = null) => {
    const params = new URLSearchParams({ hours: hours.toString() });
    if (network) params.append('network', network);
    return api.get(`/dashboard/news-analytics?${params}`);
  },

  // Get cross-chain comparison data
  getCrossChainData: (days = 7) => 
    api.get(`/dashboard/cross-chain?days=${days}`),

  // Get real-time data
  getRealTimeData: (limit = 20) => 
    api.get(`/dashboard/real-time?limit=${limit}`),

  // Get time-series metrics
  getTimeSeriesMetrics: (metric, hours = 24, interval = '1h') => 
    api.get(`/dashboard/metrics?metric=${metric}&hours=${hours}&interval=${interval}`),

  // Get gaming token data
  getGamingTokens: (network = null) => {
    const params = network ? `?network=${network}` : '';
    return api.get(`/dashboard/gaming-tokens${params}`);
  },

  // Get Solana-specific analytics
  getSolanaAnalytics: (hours = 24) => 
    api.get(`/dashboard/solana-analytics?hours=${hours}`),

  // Get dashboard alerts
  getAlerts: (severity = null, limit = 50) => {
    const params = new URLSearchParams({ limit: limit.toString() });
    if (severity) params.append('severity', severity);
    return api.get(`/dashboard/alerts?${params}`);
  },

  // Get dashboard health status
  getHealth: () => 
    api.get('/dashboard/health'),
};

// News API endpoints
export const newsAPI = {
  // Get articles with filtering
  getArticles: (params = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    return api.get(`/articles?${searchParams}`);
  },

  // Get article by ID
  getArticle: (id) => 
    api.get(`/articles/${id}`),

  // Search articles
  searchArticles: (query, filters = {}) => {
    const params = new URLSearchParams({ q: query });
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        params.append(key, value.toString());
      }
    });
    return api.get(`/search?${params}`);
  },
};

// Gaming API endpoints
export const gamingAPI = {
  // Get gaming projects
  getProjects: (params = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    return api.get(`/gaming/projects?${searchParams}`);
  },

  // Get gaming project by ID
  getProject: (id) => 
    api.get(`/gaming/projects/${id}`),

  // Get NFT collections
  getNFTCollections: (params = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    return api.get(`/gaming/nft-collections?${searchParams}`);
  },

  // Get gaming analytics
  getAnalytics: (timeframe = '24h') => 
    api.get(`/gaming/analytics?timeframe=${timeframe}`),

  // Get cross-chain gaming data
  getCrossChainGaming: () => 
    api.get('/gaming/cross-chain'),
};

// Blockchain API endpoints
export const blockchainAPI = {
  // Get blockchain data
  getBlockchainData: (network, params = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    return api.get(`/blockchain/${network}?${searchParams}`);
  },

  // Get network status
  getNetworkStatus: () => 
    api.get('/blockchain/status'),

  // Get token prices
  getTokenPrices: (tokens = []) => {
    const params = tokens.length > 0 ? `?tokens=${tokens.join(',')}` : '';
    return api.get(`/blockchain/token-prices${params}`);
  },
};

// WebSocket connection for real-time updates
export class WebSocketService {
  constructor() {
    this.ws = null;
    this.listeners = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
  }

  connect() {
    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8000/ws';
    
    try {
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
      };
      
      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.notifyListeners(data.type, data.payload);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      
      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.attemptReconnect();
      };
      
      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  subscribe(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType).add(callback);
  }

  unsubscribe(eventType, callback) {
    if (this.listeners.has(eventType)) {
      this.listeners.get(eventType).delete(callback);
    }
  }

  notifyListeners(eventType, data) {
    if (this.listeners.has(eventType)) {
      this.listeners.get(eventType).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in WebSocket listener:', error);
        }
      });
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    }
  }
}

// Create singleton WebSocket service instance
export const wsService = new WebSocketService();

export default api;
