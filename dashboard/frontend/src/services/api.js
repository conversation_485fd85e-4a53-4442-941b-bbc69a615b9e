import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens if needed
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

// Dashboard API endpoints
export const dashboardAPI = {
  // Get dashboard overview metrics
  getOverview: (hours = 24) => 
    api.get(`/dashboard/overview?hours=${hours}`),

  // Get news analytics data
  getNewsAnalytics: (hours = 24, network = null) => {
    const params = new URLSearchParams({ hours: hours.toString() });
    if (network) params.append('network', network);
    return api.get(`/dashboard/news-analytics?${params}`);
  },

  // Get cross-chain comparison data
  getCrossChainData: (days = 7) => 
    api.get(`/dashboard/cross-chain?days=${days}`),

  // Get real-time data
  getRealTimeData: (limit = 20) => 
    api.get(`/dashboard/real-time?limit=${limit}`),

  // Get time-series metrics
  getTimeSeriesMetrics: (metric, hours = 24, interval = '1h') => 
    api.get(`/dashboard/metrics?metric=${metric}&hours=${hours}&interval=${interval}`),

  // Get gaming token data
  getGamingTokens: (network = null) => {
    const params = network ? `?network=${network}` : '';
    return api.get(`/dashboard/gaming-tokens${params}`);
  },

  // Get Solana-specific analytics
  getSolanaAnalytics: (hours = 24) => 
    api.get(`/dashboard/solana-analytics?hours=${hours}`),

  // Get dashboard alerts
  getAlerts: (severity = null, limit = 50) => {
    const params = new URLSearchParams({ limit: limit.toString() });
    if (severity) params.append('severity', severity);
    return api.get(`/dashboard/alerts?${params}`);
  },

  // Get dashboard health status
  getHealth: () => 
    api.get('/dashboard/health'),
};

// News API endpoints
export const newsAPI = {
  // Get articles with filtering
  getArticles: (params = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    return api.get(`/articles?${searchParams}`);
  },

  // Get article by ID
  getArticle: (id) => 
    api.get(`/articles/${id}`),

  // Search articles
  searchArticles: (query, filters = {}) => {
    const params = new URLSearchParams({ q: query });
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        params.append(key, value.toString());
      }
    });
    return api.get(`/search?${params}`);
  },
};

// Gaming API endpoints
export const gamingAPI = {
  // Get gaming projects
  getProjects: (params = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    return api.get(`/gaming/projects?${searchParams}`);
  },

  // Get gaming project by ID
  getProject: (id) =>
    api.get(`/gaming/projects/${id}`),

  // Get NFT collections
  getNFTCollections: (params = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    return api.get(`/gaming/nft-collections?${searchParams}`);
  },

  // Get gaming analytics
  getAnalytics: (timeframe = '24h') =>
    api.get(`/gaming/analytics?timeframe=${timeframe}`),

  // Get cross-chain gaming data
  getCrossChainGaming: () =>
    api.get('/gaming/cross-chain'),
};

// Gaming Analytics API endpoints
export const gamingAnalyticsAPI = {
  // Get supported gaming protocols
  getProtocols: () =>
    api.get('/gaming-analytics/protocols'),

  // Get protocol metrics
  getProtocolMetrics: (protocolName) =>
    api.get(`/gaming-analytics/protocols/${protocolName}`),

  // Get gaming analytics summary
  getSummary: () =>
    api.get('/gaming-analytics/summary'),

  // Get user activity metrics
  getUserActivity: (protocolName = null) => {
    const params = protocolName ? `?protocol=${protocolName}` : '';
    return api.get(`/gaming-analytics/user-activity${params}`);
  },

  // Get token metrics
  getTokenMetrics: (protocolName = null) => {
    const params = protocolName ? `?protocol=${protocolName}` : '';
    return api.get(`/gaming-analytics/token-metrics${params}`);
  },

  // Get NFT metrics
  getNFTMetrics: (protocolName = null) => {
    const params = protocolName ? `?protocol=${protocolName}` : '';
    return api.get(`/gaming-analytics/nft-metrics${params}`);
  },

  // Get dashboard data
  getDashboardData: () =>
    api.get('/gaming-analytics/dashboard-data'),

  // Refresh protocol metrics
  refreshProtocol: (protocolName) =>
    api.post(`/gaming-analytics/refresh/${protocolName}`),

  // Refresh all protocols
  refreshAll: () =>
    api.post('/gaming-analytics/refresh-all'),

  // Get health status
  getHealth: () =>
    api.get('/gaming-analytics/health'),
};

// Blockchain API endpoints
export const blockchainAPI = {
  // Get blockchain data
  getBlockchainData: (network, params = {}) => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        searchParams.append(key, value.toString());
      }
    });
    return api.get(`/blockchain/${network}?${searchParams}`);
  },

  // Get network status
  getNetworkStatus: () => 
    api.get('/blockchain/status'),

  // Get token prices
  getTokenPrices: (tokens = []) => {
    const params = tokens.length > 0 ? `?tokens=${tokens.join(',')}` : '';
    return api.get(`/blockchain/token-prices${params}`);
  },
};

// WebSocket connection for real-time gaming analytics updates
export class GamingWebSocketService {
  constructor() {
    this.ws = null;
    this.listeners = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.connectionId = null;
    this.subscribedTopics = new Set();
    this.isConnected = false;
  }

  connect() {
    // Use the correct FastAPI WebSocket endpoint
    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8001/ws';

    try {
      console.log('🔌 Connecting to WebSocket:', wsUrl);
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('✅ WebSocket connected successfully');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.notifyListeners('connection_status', { connected: true });
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('📨 WebSocket message received:', data);

          // Handle connection establishment message
          if (data.type === 'connection_established') {
            this.connectionId = data.connection_id;
            console.log('🆔 Connection ID:', this.connectionId);
            console.log('📋 Available topics:', data.available_topics);

            // Auto-subscribe to gaming analytics topics
            this.subscribeToTopic('gaming_analytics');
            this.subscribeToTopic('market_summary');

            this.notifyListeners('connection_established', data);
          }
          // Handle subscription confirmation
          else if (data.type === 'subscription_confirmed') {
            console.log('✅ Subscription confirmed for topic:', data.topic);
            this.subscribedTopics.add(data.topic);
            this.notifyListeners('subscription_confirmed', data);
          }
          // Handle real-time data updates
          else if (data.type === 'data_update') {
            console.log('📊 Real-time data update:', data.topic, data.data);
            this.notifyListeners(data.topic, data.data);
            this.notifyListeners('data_update', data);
          }
          // Handle errors
          else if (data.type === 'error') {
            console.error('❌ WebSocket error:', data.message);
            this.notifyListeners('error', data);
          }
          // Handle other message types
          else {
            this.notifyListeners(data.type, data);
          }
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error, event.data);
        }
      };

      this.ws.onclose = (event) => {
        console.log('🔌 WebSocket disconnected:', event.code, event.reason);
        this.isConnected = false;
        this.connectionId = null;
        this.subscribedTopics.clear();
        this.notifyListeners('connection_status', { connected: false });
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        this.notifyListeners('connection_error', error);
      };
    } catch (error) {
      console.error('❌ Error creating WebSocket connection:', error);
    }
  }

  disconnect() {
    if (this.ws) {
      console.log('🔌 Manually disconnecting WebSocket');
      this.ws.close();
      this.ws = null;
      this.isConnected = false;
      this.connectionId = null;
      this.subscribedTopics.clear();
    }
  }

  subscribeToTopic(topic) {
    if (this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        type: 'subscribe',
        topic: topic
      };
      console.log('📡 Subscribing to topic:', topic);
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('⚠️ Cannot subscribe - WebSocket not connected');
    }
  }

  unsubscribeFromTopic(topic) {
    if (this.isConnected && this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        type: 'unsubscribe',
        topic: topic
      };
      console.log('📡 Unsubscribing from topic:', topic);
      this.ws.send(JSON.stringify(message));
      this.subscribedTopics.delete(topic);
    }
  }

  subscribe(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType).add(callback);
  }

  unsubscribe(eventType, callback) {
    if (this.listeners.has(eventType)) {
      this.listeners.get(eventType).delete(callback);
    }
  }

  notifyListeners(eventType, data) {
    if (this.listeners.has(eventType)) {
      this.listeners.get(eventType).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('❌ Error in WebSocket listener:', error);
        }
      });
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * this.reconnectAttempts;
      console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
      setTimeout(() => {
        this.connect();
      }, delay);
    } else {
      console.error('❌ Max reconnection attempts reached');
      this.notifyListeners('max_reconnect_attempts', { attempts: this.reconnectAttempts });
    }
  }

  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    } else {
      console.warn('⚠️ Cannot send message - WebSocket not connected');
    }
  }

  getConnectionStatus() {
    return {
      connected: this.isConnected,
      connectionId: this.connectionId,
      subscribedTopics: Array.from(this.subscribedTopics),
      reconnectAttempts: this.reconnectAttempts
    };
  }
}

// Create singleton gaming WebSocket service instance
export const gamingWsService = new GamingWebSocketService();

// Legacy WebSocket service for backward compatibility
export class WebSocketService {
  constructor() {
    this.ws = null;
    this.listeners = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
  }

  connect() {
    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8000/ws';

    try {
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.notifyListeners(data.type, data.payload);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  subscribe(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType).add(callback);
  }

  unsubscribe(eventType, callback) {
    if (this.listeners.has(eventType)) {
      this.listeners.get(eventType).delete(callback);
    }
  }

  notifyListeners(eventType, data) {
    if (this.listeners.has(eventType)) {
      this.listeners.get(eventType).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in WebSocket listener:', error);
        }
      });
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    }
  }
}

export const wsService = new WebSocketService();

export default api;
