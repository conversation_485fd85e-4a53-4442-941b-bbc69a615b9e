import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Article as ArticleIcon,
  Source as SourceIcon,
  SportsEsports as GamingIcon,
  AccountBalanceWallet as WalletIcon,
  Hub as NetworkIcon,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

import { dashboardAPI, gamingAnalyticsAPI } from '../services/api';

// Sample data for development
const sampleOverviewData = {
  total_articles: 1247,
  total_sources: 15,
  total_gaming_projects: 89,
  total_nft_collections: 156,
  articles_last_24h: 47,
  active_blockchain_networks: 6,
  top_gaming_categories: [
    { category: 'DeFi Gaming', count: 45 },
    { category: 'NFT Games', count: 38 },
    { category: 'Play-to-Earn', count: 32 },
    { category: 'Metaver<PERSON>', count: 28 },
    { category: 'GameFi', count: 25 }
  ],
  recent_activity_score: 78.5
};

const sampleChartData = [
  { hour: '00:00', articles: 5 },
  { hour: '04:00', articles: 3 },
  { hour: '08:00', articles: 8 },
  { hour: '12:00', articles: 12 },
  { hour: '16:00', articles: 15 },
  { hour: '20:00', articles: 9 },
];

const COLORS = ['#00d4ff', '#ff6b35', '#4caf50', '#ff9800', '#9c27b0', '#f44336'];

function MetricCard({ title, value, icon, color = 'primary', subtitle }) {
  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box
            sx={{
              p: 1,
              borderRadius: 1,
              backgroundColor: `${color}.main`,
              color: 'white',
              mr: 2,
            }}
          >
            {icon}
          </Box>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {title}
          </Typography>
        </Box>
        <Typography variant="h4" component="div" sx={{ mb: 1 }}>
          {typeof value === 'number' ? value.toLocaleString() : value}
        </Typography>
        {subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
}

function Dashboard() {
  // In a real app, this would fetch from the API
  const { data: overviewData, isLoading } = useQuery(
    'dashboard-overview',
    () => dashboardAPI.getOverview(),
    {
      // Use sample data for development
      initialData: sampleOverviewData,
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  // Fetch gaming analytics health for dashboard
  const { data: gamingHealth } = useQuery(
    'gaming-health-dashboard',
    gamingAnalyticsAPI.getHealth,
    {
      refetchInterval: 30000,
      retry: 1,
    }
  );

  // Fetch gaming analytics summary for dashboard
  const { data: gamingSummary } = useQuery(
    'gaming-summary-dashboard',
    gamingAnalyticsAPI.getSummary,
    {
      refetchInterval: 60000,
      retry: 1,
    }
  );

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Dashboard Overview
      </Typography>
      
      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Total Articles"
            value={overviewData?.total_articles || 0}
            icon={<ArticleIcon />}
            color="primary"
            subtitle={`${overviewData?.articles_last_24h || 0} in last 24h`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Active Sources"
            value={overviewData?.total_sources || 0}
            icon={<SourceIcon />}
            color="secondary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Gaming Protocols"
            value={gamingHealth?.supported_protocols || overviewData?.total_gaming_projects || 0}
            icon={<GamingIcon />}
            color="success"
            subtitle={gamingHealth?.status === 'healthy' ? 'All systems operational' : 'Some issues detected'}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="NFT Collections"
            value={overviewData?.total_nft_collections || 0}
            icon={<WalletIcon />}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Blockchain Networks"
            value={overviewData?.active_blockchain_networks || 0}
            icon={<NetworkIcon />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Activity Score"
            value={`${overviewData?.recent_activity_score || 0}%`}
            icon={<TrendingUpIcon />}
            color="primary"
            subtitle="Last 24 hours"
          />
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        {/* Article Activity Chart */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Article Activity (Last 24 Hours)
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={sampleChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="articles" 
                      stroke="#00d4ff" 
                      strokeWidth={2}
                      dot={{ fill: '#00d4ff', strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Gaming Categories Distribution */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Gaming Categories
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={overviewData?.top_gaming_categories || []}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      label={({ category, count }) => `${category}: ${count}`}
                    >
                      {(overviewData?.top_gaming_categories || []).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Gaming Analytics Summary */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Gaming Analytics Summary
              </Typography>
              {gamingSummary ? (
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Total Users
                    </Typography>
                    <Typography variant="h6">
                      {gamingSummary.total_users?.toLocaleString() || 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Market Cap
                    </Typography>
                    <Typography variant="h6">
                      ${gamingSummary.total_market_cap ? (gamingSummary.total_market_cap / 1000000).toFixed(1) + 'M' : 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Total TVL
                    </Typography>
                    <Typography variant="h6">
                      ${gamingSummary.total_tvl ? (gamingSummary.total_tvl / 1000000).toFixed(1) + 'M' : 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Active Protocols
                    </Typography>
                    <Typography variant="h6">
                      {gamingSummary.active_protocols || 'N/A'}
                    </Typography>
                  </Grid>
                </Grid>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Gaming analytics data loading...
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Top Gaming Categories List */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Gaming Categories
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {(overviewData?.top_gaming_categories || []).map((category, index) => (
                  <Chip
                    key={category.category}
                    label={`${category.category} (${category.count})`}
                    sx={{
                      backgroundColor: COLORS[index % COLORS.length],
                      color: 'white',
                      fontWeight: 'bold',
                    }}
                  />
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default Dashboard;
