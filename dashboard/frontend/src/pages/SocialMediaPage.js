import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON>,
  Fab,
  Tooltip
} from '@mui/material';
import {
  FilterList as FilterListIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import SocialMediaContent from '../components/SocialMediaContent';
import SocialMediaFilterSidebar from '../components/SocialMediaFilterSidebar';
import { socialMediaAPI } from '../services/api';

const SocialMediaPage = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState([]);
  const [summary, setSummary] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  useEffect(() => {
    loadSummaryData();
    loadActiveFilters();
  }, []);

  const loadSummaryData = async () => {
    try {
      const response = await socialMediaAPI.getSummary(24);
      setSummary(response.data);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error loading summary:', err);
      setError('Failed to load social media summary');
    }
  };

  const loadActiveFilters = async () => {
    try {
      const response = await socialMediaAPI.getFilters();
      const active = response.data.filters.filter(f => f.is_active);
      setActiveFilters(active);
    } catch (err) {
      console.error('Error loading filters:', err);
    }
  };

  const handleFiltersChange = () => {
    loadActiveFilters();
    loadSummaryData();
  };

  const handleTriggerCollection = async (platform) => {
    try {
      setLoading(true);
      await socialMediaAPI.triggerCollection(platform);
      await loadSummaryData();
      setError(null);
    } catch (err) {
      console.error('Error triggering collection:', err);
      setError(`Failed to trigger ${platform} collection`);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    await loadSummaryData();
  };

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Social Media Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Gaming-focused Twitter and Reddit content analysis
          </Typography>
          {lastUpdated && (
            <Typography variant="caption" color="text.secondary">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </Typography>
          )}
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={() => setSidebarOpen(true)}
          >
            Filters ({activeFilters.length})
          </Button>
          <Button
            variant="contained"
            startIcon={<SettingsIcon />}
            onClick={() => setSidebarOpen(true)}
          >
            Manage Filters
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Summary Cards */}
      {summary && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {summary.total_twitter_posts}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Twitter Posts
                </Typography>
                <Typography variant="caption" color="success.main">
                  +{summary.recent_twitter_posts} recent
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {summary.total_reddit_posts}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Reddit Posts
                </Typography>
                <Typography variant="caption" color="success.main">
                  +{summary.recent_reddit_posts} recent
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {summary.top_gaming_projects.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Trending Projects
                </Typography>
                {summary.top_gaming_projects.slice(0, 2).map((project, index) => (
                  <Typography key={index} variant="caption" display="block">
                    {project}
                  </Typography>
                ))}
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Typography variant="h6" color="primary">
                  {summary.top_gaming_influencers.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active Influencers
                </Typography>
                {summary.top_gaming_influencers.slice(0, 2).map((influencer, index) => (
                  <Typography key={index} variant="caption" display="block">
                    {influencer}
                  </Typography>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Active Filters Display */}
      {activeFilters.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Active Filters ({activeFilters.length})
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={1}>
              {activeFilters.map((filter) => (
                <Box
                  key={filter.id}
                  sx={{
                    border: 1,
                    borderColor: 'primary.main',
                    borderRadius: 1,
                    p: 1,
                    bgcolor: 'primary.light',
                    color: 'primary.contrastText'
                  }}
                >
                  <Typography variant="caption" fontWeight="bold">
                    {filter.filter_name}
                  </Typography>
                  <Typography variant="caption" display="block">
                    {filter.filter_type} • {filter.keywords?.length || 0} keywords
                  </Typography>
                </Box>
              ))}
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Collection Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Data Collection
          </Typography>
          <Box display="flex" gap={2}>
            <Button
              variant="outlined"
              onClick={() => handleTriggerCollection('twitter')}
              disabled={loading}
            >
              Collect Twitter Data
            </Button>
            <Button
              variant="outlined"
              onClick={() => handleTriggerCollection('reddit')}
              disabled={loading}
            >
              Collect Reddit Data
            </Button>
            <Button
              variant="contained"
              onClick={() => handleTriggerCollection('both')}
              disabled={loading}
            >
              Collect All Data
            </Button>
          </Box>
          <Typography variant="caption" color="text.secondary" display="block" mt={1}>
            Manual data collection respects rate limits and API quotas
          </Typography>
        </CardContent>
      </Card>

      {/* Main Content */}
      <SocialMediaContent filters={activeFilters} />

      {/* Filter Sidebar */}
      <SocialMediaFilterSidebar
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        onFiltersChange={handleFiltersChange}
      />

      {/* Floating Action Button for Filters */}
      {!sidebarOpen && (
        <Tooltip title="Open Filters">
          <Fab
            color="primary"
            aria-label="filters"
            sx={{
              position: 'fixed',
              bottom: 16,
              right: 16,
              zIndex: 1000
            }}
            onClick={() => setSidebarOpen(true)}
          >
            <FilterListIcon />
          </Fab>
        </Tooltip>
      )}
    </Container>
  );
};

export default SocialMediaPage;
