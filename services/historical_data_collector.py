"""
Historical Data Collection Service
Collects and stores gaming protocol metrics for time-series analysis
"""
import asyncio
import logging
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc
from sqlalchemy.exc import IntegrityError

from models.gaming import GamingProtocolMetricsHistory, GamingProtocolDailySummary
from models.base import get_db
from blockchain.gaming_analytics import gaming_analytics

logger = logging.getLogger(__name__)

class HistoricalDataCollector:
    """Service for collecting and managing historical gaming protocol data"""
    
    def __init__(self):
        self.collection_interval = 300  # 5 minutes
        self.retention_days = 90  # Keep 90 days of detailed history
        self.daily_summary_retention_days = 365  # Keep 1 year of daily summaries
        
    async def collect_current_metrics(self) -> Dict[str, Any]:
        """Collect current metrics from all gaming protocols"""
        logger.info("🔄 Collecting current gaming protocol metrics...")
        
        try:
            # Ensure gaming analytics is initialized
            await gaming_analytics.initialize()
            
            # Collect metrics for all protocols
            all_metrics = await gaming_analytics.collect_all_protocols_metrics()
            
            collection_timestamp = datetime.now()
            stored_count = 0
            
            # Store each protocol's metrics
            with next(get_db()) as db:
                for protocol_name, metrics in all_metrics.items():
                    try:
                        # Create historical record
                        historical_record = GamingProtocolMetricsHistory(
                            protocol_name=protocol_name,
                            protocol_display_name=gaming_analytics.supported_protocols.get(
                                protocol_name, {}
                            ).get('name', protocol_name),
                            chain=gaming_analytics.supported_protocols.get(
                                protocol_name, {}
                            ).get('primary_chain', 'unknown'),
                            
                            # Token metrics
                            token_price=float(metrics.token_price) if metrics.token_price else None,
                            token_price_change_24h=float(metrics.token_price_change_24h) if metrics.token_price_change_24h else None,
                            market_cap=int(metrics.market_cap) if metrics.market_cap else None,
                            trading_volume_24h=int(metrics.trading_volume_24h) if metrics.trading_volume_24h else None,
                            circulating_supply=int(metrics.circulating_supply) if metrics.circulating_supply else None,
                            
                            # User activity metrics
                            daily_active_users=int(metrics.daily_active_users) if metrics.daily_active_users else None,
                            monthly_active_users=int(metrics.monthly_active_users) if metrics.monthly_active_users else None,
                            new_users_24h=int(metrics.new_users_24h) if metrics.new_users_24h else None,
                            user_retention_rate=float(metrics.user_retention_rate) if metrics.user_retention_rate else None,
                            
                            # Transaction metrics
                            transaction_count_24h=int(metrics.transaction_count_24h) if metrics.transaction_count_24h else None,
                            transaction_volume_24h=int(metrics.transaction_volume_24h) if metrics.transaction_volume_24h else None,
                            average_transaction_value=float(metrics.average_transaction_value) if metrics.average_transaction_value else None,
                            gas_fees_24h=float(metrics.gas_fees_24h) if metrics.gas_fees_24h else None,
                            
                            # NFT metrics
                            nft_trades_24h=int(metrics.nft_trades_24h) if metrics.nft_trades_24h else None,
                            nft_volume_24h=int(metrics.nft_volume_24h) if metrics.nft_volume_24h else None,
                            floor_price=float(metrics.floor_price) if metrics.floor_price else None,
                            unique_holders=int(metrics.unique_holders) if metrics.unique_holders else None,
                            
                            # Protocol health metrics
                            total_value_locked=int(metrics.total_value_locked) if metrics.total_value_locked else None,
                            protocol_revenue_24h=int(metrics.protocol_revenue_24h) if metrics.protocol_revenue_24h else None,
                            staking_rewards_distributed=int(metrics.staking_rewards_distributed) if metrics.staking_rewards_distributed else None,
                            average_earnings_per_user=float(metrics.average_earnings_per_user) if metrics.average_earnings_per_user else None,
                            reward_token_distribution=int(metrics.reward_token_distribution) if metrics.reward_token_distribution else None,
                            gameplay_sessions_24h=int(metrics.gameplay_sessions_24h) if metrics.gameplay_sessions_24h else None,
                            protocol_uptime=float(metrics.protocol_uptime) if metrics.protocol_uptime else None,
                            smart_contract_interactions=int(metrics.smart_contract_interactions) if metrics.smart_contract_interactions else None,
                            developer_activity_score=float(metrics.developer_activity_score) if metrics.developer_activity_score else None,
                            
                            # Metadata
                            data_source='gaming_analytics',
                            collection_timestamp=collection_timestamp
                        )
                        
                        db.add(historical_record)
                        stored_count += 1
                        
                    except Exception as e:
                        logger.error(f"❌ Failed to store metrics for {protocol_name}: {e}")
                        continue
                
                # Commit all records
                db.commit()
                
            logger.info(f"✅ Stored {stored_count} historical records")
            
            return {
                "success": True,
                "records_stored": stored_count,
                "collection_timestamp": collection_timestamp.isoformat(),
                "protocols": list(all_metrics.keys())
            }
            
        except Exception as e:
            logger.error(f"❌ Historical data collection failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "collection_timestamp": datetime.now().isoformat()
            }
    
    async def generate_daily_summaries(self, target_date: Optional[date] = None) -> Dict[str, Any]:
        """Generate daily summary statistics from historical data"""
        if target_date is None:
            target_date = date.today() - timedelta(days=1)  # Yesterday
            
        logger.info(f"📊 Generating daily summaries for {target_date}")
        
        try:
            with next(get_db()) as db:
                # Get all protocols that have data for this date
                start_datetime = datetime.combine(target_date, datetime.min.time())
                end_datetime = start_datetime + timedelta(days=1)
                
                protocols_query = db.query(GamingProtocolMetricsHistory.protocol_name).filter(
                    and_(
                        GamingProtocolMetricsHistory.collection_timestamp >= start_datetime,
                        GamingProtocolMetricsHistory.collection_timestamp < end_datetime
                    )
                ).distinct()
                
                protocols = [p[0] for p in protocols_query.all()]
                summaries_created = 0
                
                for protocol_name in protocols:
                    # Get all metrics for this protocol on this date
                    metrics_query = db.query(GamingProtocolMetricsHistory).filter(
                        and_(
                            GamingProtocolMetricsHistory.protocol_name == protocol_name,
                            GamingProtocolMetricsHistory.collection_timestamp >= start_datetime,
                            GamingProtocolMetricsHistory.collection_timestamp < end_datetime
                        )
                    ).all()
                    
                    if not metrics_query:
                        continue
                    
                    # Calculate aggregated metrics
                    token_prices = [m.token_price for m in metrics_query if m.token_price is not None]
                    market_caps = [m.market_cap for m in metrics_query if m.market_cap is not None]
                    trading_volumes = [m.trading_volume_24h for m in metrics_query if m.trading_volume_24h is not None]
                    dau_values = [m.daily_active_users for m in metrics_query if m.daily_active_users is not None]
                    uptime_values = [m.protocol_uptime for m in metrics_query if m.protocol_uptime is not None]
                    
                    # Create or update daily summary
                    existing_summary = db.query(GamingProtocolDailySummary).filter(
                        and_(
                            GamingProtocolDailySummary.protocol_name == protocol_name,
                            GamingProtocolDailySummary.date == start_datetime
                        )
                    ).first()
                    
                    summary_data = {
                        'protocol_name': protocol_name,
                        'date': start_datetime,
                        'avg_token_price': sum(token_prices) / len(token_prices) if token_prices else None,
                        'min_token_price': min(token_prices) if token_prices else None,
                        'max_token_price': max(token_prices) if token_prices else None,
                        'price_change_percent': (
                            ((max(token_prices) - min(token_prices)) / min(token_prices) * 100)
                            if token_prices and len(token_prices) > 1 and min(token_prices) > 0 else None
                        ),
                        'avg_market_cap': int(sum(market_caps) / len(market_caps)) if market_caps else None,
                        'total_trading_volume': sum(trading_volumes) if trading_volumes else None,
                        'max_daily_active_users': max(dau_values) if dau_values else None,
                        'avg_protocol_uptime': sum(uptime_values) / len(uptime_values) if uptime_values else None,
                        'data_points_collected': len(metrics_query),
                        'last_updated': datetime.now()
                    }
                    
                    if existing_summary:
                        # Update existing summary
                        for key, value in summary_data.items():
                            if key != 'protocol_name' and key != 'date':
                                setattr(existing_summary, key, value)
                    else:
                        # Create new summary
                        daily_summary = GamingProtocolDailySummary(**summary_data)
                        db.add(daily_summary)
                        summaries_created += 1
                
                db.commit()
                
            logger.info(f"✅ Generated {summaries_created} daily summaries for {target_date}")
            
            return {
                "success": True,
                "summaries_created": summaries_created,
                "date": target_date.isoformat(),
                "protocols_processed": len(protocols)
            }
            
        except Exception as e:
            logger.error(f"❌ Daily summary generation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "date": target_date.isoformat()
            }
    
    async def cleanup_old_data(self) -> Dict[str, Any]:
        """Clean up old historical data based on retention policies"""
        logger.info("🧹 Cleaning up old historical data...")
        
        try:
            with next(get_db()) as db:
                # Clean up detailed historical data older than retention period
                cutoff_date = datetime.now() - timedelta(days=self.retention_days)
                
                deleted_history = db.query(GamingProtocolMetricsHistory).filter(
                    GamingProtocolMetricsHistory.collection_timestamp < cutoff_date
                ).delete()
                
                # Clean up daily summaries older than retention period
                summary_cutoff_date = datetime.now() - timedelta(days=self.daily_summary_retention_days)
                
                deleted_summaries = db.query(GamingProtocolDailySummary).filter(
                    GamingProtocolDailySummary.date < summary_cutoff_date
                ).delete()
                
                db.commit()
                
            logger.info(f"✅ Cleaned up {deleted_history} historical records and {deleted_summaries} daily summaries")
            
            return {
                "success": True,
                "deleted_history_records": deleted_history,
                "deleted_summary_records": deleted_summaries,
                "retention_days": self.retention_days,
                "summary_retention_days": self.daily_summary_retention_days
            }
            
        except Exception as e:
            logger.error(f"❌ Data cleanup failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# Global instance
historical_data_collector = HistoricalDataCollector()
