"""
CryptoRank API client for cryptocurrency data
"""
import logging
from typing import Dict, List, Optional, Any
from .base import BaseBlockchainDataClient
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class CryptoRankClient(BaseBlockchainDataClient):
    """Client for CryptoRank API"""
    
    def __init__(self):
        super().__init__(
            api_key=settings.blockchain_data.cryptorank_api_key,
            base_url=settings.blockchain_data.cryptorank_base_url,
            rate_limit=settings.blockchain_data.rate_limit_per_minute
        )
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for CryptoRank API"""
        return {
            'api-key': self.api_key,
            'Content-Type': 'application/json'
        }
    
    async def _test_endpoint(self):
        """Test CryptoRank API connection"""
        # Test with currencies endpoint
        await self._make_request('GET', '/currencies', params={'limit': 1})
    
    async def get_gaming_tokens_data(self, tokens: List[str]) -> List[Dict[str, Any]]:
        """Get gaming token data from CryptoRank"""
        if not tokens:
            return []
        
        results = []
        
        # CryptoRank API typically requires individual requests or specific symbol queries
        for token in tokens:
            try:
                params = {
                    'symbol': token,
                    'limit': 1
                }
                
                response = await self._make_request('GET', '/currencies', params=params)
                data = response.get('data', [])
                
                if data:
                    token_data = data[0]
                    results.append({
                        'symbol': token_data.get('symbol'),
                        'name': token_data.get('name'),
                        'price_usd': token_data.get('values', {}).get('USD', {}).get('price'),
                        'market_cap_usd': token_data.get('values', {}).get('USD', {}).get('marketCap'),
                        'volume_24h_usd': token_data.get('values', {}).get('USD', {}).get('volume24h'),
                        'price_change_24h_percent': token_data.get('values', {}).get('USD', {}).get('percentChange24h'),
                        'rank': token_data.get('rank'),
                        'category': token_data.get('category'),
                        'last_updated': token_data.get('lastUpdated')
                    })
                    
            except Exception as e:
                logger.error(f"Failed to get data for token {token}: {e}")
                continue
        
        return results
    
    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """Get NFT collection data from CryptoRank"""
        # CryptoRank may not have direct NFT collection endpoints
        # This would need to be implemented based on their actual API structure
        try:
            # Placeholder - would need actual NFT endpoints
            params = {
                'address': collection_address
            }
            
            # This endpoint may not exist - check CryptoRank API docs
            response = await self._make_request('GET', '/nft/collections', params=params)
            return response.get('data', {})
            
        except Exception as e:
            logger.error(f"Failed to get NFT collection data: {e}")
            return {}
    
    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """Get gaming protocol metrics from CryptoRank"""
        try:
            # Search for protocols by name
            params = {
                'query': protocol_name,
                'category': 'gaming',
                'limit': 10
            }
            
            response = await self._make_request('GET', '/currencies', params=params)
            data = response.get('data', [])
            
            # Filter for gaming-related protocols
            gaming_protocols = [
                item for item in data 
                if 'gaming' in item.get('category', '').lower() or 
                   'game' in item.get('name', '').lower() or
                   protocol_name.lower() in item.get('name', '').lower()
            ]
            
            return {
                'protocol_name': protocol_name,
                'protocols': gaming_protocols
            }
            
        except Exception as e:
            logger.error(f"Failed to get gaming protocol metrics: {e}")
            return {}
    
    async def get_gaming_categories_data(self) -> Dict[str, Any]:
        """Get data for gaming category cryptocurrencies"""
        try:
            params = {
                'category': 'gaming',
                'limit': 100,
                'sortBy': 'marketCap',
                'sortDirection': 'desc'
            }
            
            response = await self._make_request('GET', '/currencies', params=params)
            return {
                'gaming_category': response.get('data', [])
            }
            
        except Exception as e:
            logger.error(f"Failed to get gaming categories data: {e}")
            return {}
    
    async def get_metaverse_tokens_data(self) -> Dict[str, Any]:
        """Get metaverse token data"""
        try:
            params = {
                'category': 'metaverse',
                'limit': 50,
                'sortBy': 'marketCap',
                'sortDirection': 'desc'
            }
            
            response = await self._make_request('GET', '/currencies', params=params)
            return {
                'metaverse_tokens': response.get('data', [])
            }
            
        except Exception as e:
            logger.error(f"Failed to get metaverse tokens data: {e}")
            return {}
    
    async def get_nft_tokens_data(self) -> Dict[str, Any]:
        """Get NFT-related token data"""
        try:
            params = {
                'category': 'nft',
                'limit': 50,
                'sortBy': 'marketCap',
                'sortDirection': 'desc'
            }
            
            response = await self._make_request('GET', '/currencies', params=params)
            return {
                'nft_tokens': response.get('data', [])
            }
            
        except Exception as e:
            logger.error(f"Failed to get NFT tokens data: {e}")
            return {}
    
    async def get_token_historical_data(self, symbol: str, days: int = 30) -> Dict[str, Any]:
        """Get historical price data for a token"""
        try:
            # First get the currency ID
            params = {
                'symbol': symbol,
                'limit': 1
            }
            
            response = await self._make_request('GET', '/currencies', params=params)
            data = response.get('data', [])
            
            if not data:
                return {}
            
            currency_id = data[0].get('id')
            
            # Get historical data
            params = {
                'days': days,
                'interval': 'daily'
            }
            
            historical_response = await self._make_request(
                'GET', 
                f'/currencies/{currency_id}/chart', 
                params=params
            )
            
            return {
                'symbol': symbol,
                'historical_data': historical_response.get('data', [])
            }
            
        except Exception as e:
            logger.error(f"Failed to get historical data for {symbol}: {e}")
            return {}
    
    async def get_trending_gaming_tokens(self) -> Dict[str, Any]:
        """Get trending gaming tokens"""
        try:
            params = {
                'category': 'gaming',
                'sortBy': 'percentChange24h',
                'sortDirection': 'desc',
                'limit': 20
            }
            
            response = await self._make_request('GET', '/currencies', params=params)
            return {
                'trending_gaming_tokens': response.get('data', [])
            }
            
        except Exception as e:
            logger.error(f"Failed to get trending gaming tokens: {e}")
            return {}
    
    async def search_gaming_projects(self, query: str) -> Dict[str, Any]:
        """Search for gaming-related projects"""
        try:
            params = {
                'query': query,
                'limit': 20
            }
            
            response = await self._make_request('GET', '/currencies', params=params)
            data = response.get('data', [])
            
            # Filter for gaming-related results
            gaming_results = [
                item for item in data
                if any(keyword in item.get('name', '').lower() or 
                      keyword in item.get('category', '').lower()
                      for keyword in ['gaming', 'game', 'nft', 'metaverse', 'play'])
            ]
            
            return {
                'query': query,
                'gaming_projects': gaming_results
            }
            
        except Exception as e:
            logger.error(f"Failed to search gaming projects: {e}")
            return {}
