"""
Gaming Token Price and Market Data Integration
Integrates with multiple APIs to track gaming token prices and market data
"""
import asyncio
import logging
import aiohttp
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import time

from .gaming_contracts import gaming_contract_manager
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class TokenPrice:
    """Token price information"""
    symbol: str
    contract_address: str
    chain: str
    price_usd: float
    price_change_24h: float
    market_cap: Optional[float] = None
    volume_24h: Optional[float] = None
    circulating_supply: Optional[float] = None
    total_supply: Optional[float] = None
    last_updated: datetime = field(default_factory=datetime.utcnow)


@dataclass
class MarketData:
    """Comprehensive market data for gaming tokens"""
    token_price: TokenPrice
    trading_pairs: List[Dict[str, Any]] = field(default_factory=list)
    price_history: List[Dict[str, Any]] = field(default_factory=list)
    social_metrics: Dict[str, Any] = field(default_factory=dict)
    gaming_metrics: Dict[str, Any] = field(default_factory=dict)


class MarketDataProvider:
    """Base class for market data providers"""
    
    def __init__(self, name: str, base_url: str, api_key: Optional[str] = None):
        self.name = name
        self.base_url = base_url
        self.api_key = api_key
        self.session = None
        self.rate_limit_delay = 1.0  # seconds between requests
        self.last_request_time = 0
    
    async def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """Make rate-limited API request"""
        # Rate limiting
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            await asyncio.sleep(self.rate_limit_delay - time_since_last)
        
        if not self.session:
            self.session = aiohttp.ClientSession()
        
        url = f"{self.base_url}{endpoint}"
        headers = {}
        
        if self.api_key:
            headers['Authorization'] = f"Bearer {self.api_key}"
        
        try:
            async with self.session.get(
                url,
                params=params,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                self.last_request_time = time.time()
                
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:
                    # Rate limited, wait and retry
                    await asyncio.sleep(5)
                    return await self._make_request(endpoint, params)
                else:
                    logger.error(f"{self.name} API error {response.status}: {await response.text()}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error making request to {self.name}: {e}")
            return None
    
    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()


class CryptoRankProvider(MarketDataProvider):
    """CryptoRank.io API provider"""
    
    def __init__(self, api_key: Optional[str] = None):
        super().__init__(
            name="CryptoRank",
            base_url="https://api.cryptorank.io/v1/",
            api_key=api_key
        )
    
    async def get_token_price(self, symbol: str) -> Optional[TokenPrice]:
        """Get token price from CryptoRank"""
        try:
            data = await self._make_request(f"currencies/{symbol}")
            
            if not data or 'data' not in data:
                return None
            
            token_data = data['data']
            
            return TokenPrice(
                symbol=symbol,
                contract_address="",  # CryptoRank doesn't always provide contract address
                chain="",
                price_usd=float(token_data.get('values', {}).get('USD', {}).get('price', 0)),
                price_change_24h=float(token_data.get('values', {}).get('USD', {}).get('percentChange24h', 0)),
                market_cap=float(token_data.get('values', {}).get('USD', {}).get('marketCap', 0)) if token_data.get('values', {}).get('USD', {}).get('marketCap') else None,
                volume_24h=float(token_data.get('values', {}).get('USD', {}).get('volume24h', 0)) if token_data.get('values', {}).get('USD', {}).get('volume24h') else None,
                circulating_supply=float(token_data.get('circulatingSupply', 0)) if token_data.get('circulatingSupply') else None,
                total_supply=float(token_data.get('totalSupply', 0)) if token_data.get('totalSupply') else None
            )
            
        except Exception as e:
            logger.error(f"Error getting token price from CryptoRank: {e}")
            return None
    
    async def get_gaming_tokens(self) -> List[Dict[str, Any]]:
        """Get gaming category tokens from CryptoRank"""
        try:
            data = await self._make_request("currencies", {
                "category": "gaming",
                "limit": 100
            })
            
            if data and 'data' in data:
                return data['data']
            
            return []
            
        except Exception as e:
            logger.error(f"Error getting gaming tokens from CryptoRank: {e}")
            return []


class DexToolsProvider(MarketDataProvider):
    """DexTools.io API provider"""
    
    def __init__(self, api_key: Optional[str] = None):
        super().__init__(
            name="DexTools",
            base_url="https://www.dextools.io/shared/data/",
            api_key=api_key
        )
        self.rate_limit_delay = 2.0  # DexTools has stricter rate limits
    
    async def get_token_info(self, chain: str, contract_address: str) -> Optional[Dict[str, Any]]:
        """Get token information from DexTools"""
        try:
            # Map chain names to DexTools format
            chain_mapping = {
                'ethereum': 'ether',
                'bsc': 'bsc',
                'polygon': 'polygon',
                'arbitrum': 'arbitrum'
            }
            
            dex_chain = chain_mapping.get(chain.lower())
            if not dex_chain:
                return None
            
            data = await self._make_request(f"pair/{dex_chain}/{contract_address}")
            
            return data
            
        except Exception as e:
            logger.error(f"Error getting token info from DexTools: {e}")
            return None


class CoinGeckoProvider(MarketDataProvider):
    """CoinGecko API provider (free tier)"""
    
    def __init__(self):
        super().__init__(
            name="CoinGecko",
            base_url="https://api.coingecko.com/api/v3/"
        )
        self.rate_limit_delay = 1.2  # CoinGecko free tier limit
    
    async def get_token_price_by_contract(
        self,
        chain: str,
        contract_address: str
    ) -> Optional[TokenPrice]:
        """Get token price by contract address"""
        try:
            # Map chain names to CoinGecko platform IDs
            platform_mapping = {
                'ethereum': 'ethereum',
                'bsc': 'binance-smart-chain',
                'polygon': 'polygon-pos',
                'arbitrum': 'arbitrum-one',
                'optimism': 'optimistic-ethereum'
            }
            
            platform = platform_mapping.get(chain.lower())
            if not platform:
                return None
            
            data = await self._make_request(
                f"simple/token_price/{platform}",
                {
                    'contract_addresses': contract_address,
                    'vs_currencies': 'usd',
                    'include_24hr_change': 'true',
                    'include_market_cap': 'true',
                    'include_24hr_vol': 'true'
                }
            )
            
            if not data or contract_address.lower() not in data:
                return None
            
            token_data = data[contract_address.lower()]
            
            return TokenPrice(
                symbol="",  # CoinGecko doesn't provide symbol in this endpoint
                contract_address=contract_address,
                chain=chain,
                price_usd=float(token_data.get('usd', 0)),
                price_change_24h=float(token_data.get('usd_24h_change', 0)),
                market_cap=float(token_data.get('usd_market_cap', 0)) if token_data.get('usd_market_cap') else None,
                volume_24h=float(token_data.get('usd_24h_vol', 0)) if token_data.get('usd_24h_vol') else None
            )
            
        except Exception as e:
            logger.error(f"Error getting token price from CoinGecko: {e}")
            return None
    
    async def search_gaming_tokens(self) -> List[Dict[str, Any]]:
        """Search for gaming tokens on CoinGecko"""
        try:
            data = await self._make_request("search", {'query': 'gaming'})
            
            if data and 'coins' in data:
                return data['coins']
            
            return []
            
        except Exception as e:
            logger.error(f"Error searching gaming tokens on CoinGecko: {e}")
            return []


class GamingMarketDataManager:
    """Manages gaming token market data from multiple sources"""
    
    def __init__(self):
        self.providers = {
            'cryptorank': CryptoRankProvider(settings.blockchain_data.cryptorank_api_key),
            'dextools': DexToolsProvider(settings.blockchain_data.dextools_api_key),
            'coingecko': CoinGeckoProvider()
        }
        self.price_cache: Dict[str, TokenPrice] = {}
        self.cache_duration = timedelta(minutes=5)  # Cache prices for 5 minutes
        self.gaming_tokens = []
        self._load_gaming_tokens()
    
    def _load_gaming_tokens(self):
        """Load gaming tokens from contract manager"""
        gaming_contracts = gaming_contract_manager.get_gaming_tokens()
        
        for contract in gaming_contracts:
            self.gaming_tokens.append({
                'symbol': contract.token_symbol,
                'contract_address': contract.address,
                'chain': contract.chain,
                'name': contract.name
            })
    
    async def get_token_price(
        self,
        chain: str,
        contract_address: str,
        symbol: str = ""
    ) -> Optional[TokenPrice]:
        """Get token price from multiple sources with fallback"""
        cache_key = f"{chain}:{contract_address.lower()}"
        
        # Check cache first
        if cache_key in self.price_cache:
            cached_price = self.price_cache[cache_key]
            if datetime.utcnow() - cached_price.last_updated < self.cache_duration:
                return cached_price
        
        # Try providers in order of preference
        providers_to_try = ['coingecko', 'cryptorank', 'dextools']
        
        for provider_name in providers_to_try:
            try:
                provider = self.providers[provider_name]
                
                if provider_name == 'coingecko':
                    price = await provider.get_token_price_by_contract(chain, contract_address)
                elif provider_name == 'cryptorank' and symbol:
                    price = await provider.get_token_price(symbol)
                else:
                    continue
                
                if price:
                    # Update with missing info
                    price.symbol = symbol or price.symbol
                    price.contract_address = contract_address
                    price.chain = chain
                    
                    # Cache the result
                    self.price_cache[cache_key] = price
                    
                    logger.info(f"Got price for {symbol} from {provider_name}: ${price.price_usd}")
                    return price
                    
            except Exception as e:
                logger.error(f"Error getting price from {provider_name}: {e}")
                continue
        
        logger.warning(f"Could not get price for {symbol} ({chain}:{contract_address})")
        return None
    
    async def get_all_gaming_token_prices(self) -> Dict[str, TokenPrice]:
        """Get prices for all tracked gaming tokens"""
        prices = {}
        
        for token in self.gaming_tokens:
            try:
                price = await self.get_token_price(
                    token['chain'],
                    token['contract_address'],
                    token['symbol']
                )
                
                if price:
                    prices[token['symbol']] = price
                
                # Small delay to respect rate limits
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error getting price for {token['symbol']}: {e}")
        
        return prices
    
    async def get_market_data(
        self,
        chain: str,
        contract_address: str,
        symbol: str = ""
    ) -> Optional[MarketData]:
        """Get comprehensive market data for a token"""
        try:
            # Get basic price data
            token_price = await self.get_token_price(chain, contract_address, symbol)
            if not token_price:
                return None
            
            # Get additional data from DexTools if available
            dextools_data = None
            if chain in ['ethereum', 'bsc', 'polygon', 'arbitrum']:
                dextools_data = await self.providers['dextools'].get_token_info(
                    chain, contract_address
                )
            
            # Compile market data
            market_data = MarketData(token_price=token_price)
            
            if dextools_data:
                market_data.trading_pairs = dextools_data.get('pairs', [])
                market_data.social_metrics = dextools_data.get('social', {})
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return None
    
    async def discover_new_gaming_tokens(self) -> List[Dict[str, Any]]:
        """Discover new gaming tokens from various sources"""
        new_tokens = []
        
        try:
            # Search CryptoRank for gaming tokens
            cryptorank_tokens = await self.providers['cryptorank'].get_gaming_tokens()
            new_tokens.extend(cryptorank_tokens)
            
            # Search CoinGecko for gaming tokens
            coingecko_tokens = await self.providers['coingecko'].search_gaming_tokens()
            new_tokens.extend(coingecko_tokens)
            
            logger.info(f"Discovered {len(new_tokens)} potential gaming tokens")
            
        except Exception as e:
            logger.error(f"Error discovering new gaming tokens: {e}")
        
        return new_tokens
    
    def get_price_cache_stats(self) -> Dict[str, Any]:
        """Get price cache statistics"""
        now = datetime.utcnow()
        fresh_entries = sum(
            1 for price in self.price_cache.values()
            if now - price.last_updated < self.cache_duration
        )
        
        return {
            'total_cached': len(self.price_cache),
            'fresh_entries': fresh_entries,
            'stale_entries': len(self.price_cache) - fresh_entries,
            'cache_duration_minutes': self.cache_duration.total_seconds() / 60
        }
    
    async def close_all(self):
        """Close all provider sessions"""
        for provider in self.providers.values():
            await provider.close()


# Global gaming market data manager instance
gaming_market_data = GamingMarketDataManager()
