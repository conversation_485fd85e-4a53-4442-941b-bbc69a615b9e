"""
Blockchain Data Synchronization System
Handles data synchronization, reorg detection, and multi-chain coordination
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

from .multi_chain_client import multi_chain_manager
from .event_monitor import gaming_event_monitor
from .nft_tracker import nft_tracker
from .market_data import gaming_market_data
from models.gaming import BlockchainData
from models.crud import (
    create_blockchain_data, 
    get_blockchain_data_by_filters,
    update_blockchain_data,
    delete_blockchain_data
)
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class SyncStatus(Enum):
    """Synchronization status"""
    SYNCING = "syncing"
    SYNCED = "synced"
    ERROR = "error"
    REORG_DETECTED = "reorg_detected"
    PAUSED = "paused"


@dataclass
class ChainSyncState:
    """Synchronization state for a blockchain"""
    chain: str
    last_synced_block: int = 0
    latest_block: int = 0
    sync_status: SyncStatus = SyncStatus.SYNCING
    last_sync_time: datetime = field(default_factory=datetime.utcnow)
    error_count: int = 0
    reorg_depth: int = 0
    blocks_behind: int = 0
    sync_rate: float = 0.0  # blocks per second


@dataclass
class ReorgEvent:
    """Blockchain reorganization event"""
    chain: str
    detected_at: datetime
    old_block_hash: str
    new_block_hash: str
    reorg_depth: int
    affected_blocks: List[int]
    affected_transactions: List[str] = field(default_factory=list)


class BlockchainSyncManager:
    """Manages blockchain data synchronization across multiple chains"""
    
    def __init__(self):
        self.sync_states: Dict[str, ChainSyncState] = {}
        self.reorg_events: List[ReorgEvent] = []
        self.sync_active = False
        self.sync_interval = 30  # seconds
        self.max_blocks_per_sync = 100
        self.reorg_check_depth = 12  # blocks to check for reorgs
        self.max_error_count = 5
        self._initialize_sync_states()
    
    def _initialize_sync_states(self):
        """Initialize sync states for all supported chains"""
        gaming_chains = multi_chain_manager.get_gaming_chains()
        
        for chain in gaming_chains:
            self.sync_states[chain] = ChainSyncState(chain=chain)
            logger.info(f"Initialized sync state for {chain}")
    
    async def start_synchronization(self):
        """Start blockchain synchronization"""
        self.sync_active = True
        logger.info("Starting blockchain synchronization...")
        
        # Start sync tasks for each chain
        tasks = []
        for chain in self.sync_states.keys():
            task = asyncio.create_task(self._sync_chain(chain))
            tasks.append(task)
        
        # Start market data sync
        market_task = asyncio.create_task(self._sync_market_data())
        tasks.append(market_task)
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Error in synchronization: {e}")
        finally:
            self.sync_active = False
    
    async def stop_synchronization(self):
        """Stop blockchain synchronization"""
        self.sync_active = False
        logger.info("Stopping blockchain synchronization...")
    
    async def _sync_chain(self, chain: str):
        """Synchronize data for a specific chain"""
        sync_state = self.sync_states[chain]
        logger.info(f"Starting synchronization for {chain}")
        
        while self.sync_active:
            try:
                sync_state.sync_status = SyncStatus.SYNCING
                
                # Get client for chain
                client = multi_chain_manager.get_client(chain)
                if not client:
                    sync_state.sync_status = SyncStatus.ERROR
                    sync_state.error_count += 1
                    await asyncio.sleep(self.sync_interval)
                    continue
                
                # Get latest block
                latest_block = await client.get_latest_block_number()
                if not latest_block:
                    sync_state.sync_status = SyncStatus.ERROR
                    sync_state.error_count += 1
                    await asyncio.sleep(self.sync_interval)
                    continue
                
                sync_state.latest_block = latest_block
                sync_state.blocks_behind = latest_block - sync_state.last_synced_block
                
                # Check for reorgs if we have synced before
                if sync_state.last_synced_block > 0:
                    reorg_detected = await self._check_for_reorg(chain, sync_state)
                    if reorg_detected:
                        continue  # Reorg handling will update sync state
                
                # Sync new blocks
                await self._sync_new_blocks(chain, sync_state)
                
                # Update sync metrics
                sync_state.last_sync_time = datetime.utcnow()
                sync_state.error_count = 0
                sync_state.sync_status = SyncStatus.SYNCED
                
                await asyncio.sleep(self.sync_interval)
                
            except Exception as e:
                logger.error(f"Error syncing {chain}: {e}")
                sync_state.sync_status = SyncStatus.ERROR
                sync_state.error_count += 1
                
                # Pause if too many errors
                if sync_state.error_count >= self.max_error_count:
                    sync_state.sync_status = SyncStatus.PAUSED
                    logger.warning(f"Pausing sync for {chain} due to repeated errors")
                    await asyncio.sleep(self.sync_interval * 5)  # Longer pause
                else:
                    await asyncio.sleep(self.sync_interval)
    
    async def _check_for_reorg(self, chain: str, sync_state: ChainSyncState) -> bool:
        """Check for blockchain reorganization"""
        try:
            client = multi_chain_manager.get_client(chain)
            if not client:
                return False
            
            # Check recent blocks for hash changes
            check_from = max(0, sync_state.last_synced_block - self.reorg_check_depth)
            
            for block_num in range(check_from, sync_state.last_synced_block + 1):
                # Get current block hash
                current_block = await client.get_block(block_num)
                if not current_block:
                    continue
                
                current_hash = current_block['hash']
                
                # Get stored block hash from database
                stored_data = await get_blockchain_data_by_filters({
                    'blockchain': chain,
                    'block_number': block_num
                })
                
                if stored_data:
                    stored_hash = stored_data[0].event_data.get('block_hash')
                    
                    if stored_hash and stored_hash != current_hash:
                        # Reorg detected!
                        reorg_depth = sync_state.last_synced_block - block_num + 1
                        
                        reorg_event = ReorgEvent(
                            chain=chain,
                            detected_at=datetime.utcnow(),
                            old_block_hash=stored_hash,
                            new_block_hash=current_hash,
                            reorg_depth=reorg_depth,
                            affected_blocks=list(range(block_num, sync_state.last_synced_block + 1))
                        )
                        
                        await self._handle_reorg(reorg_event, sync_state)
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking for reorg on {chain}: {e}")
            return False
    
    async def _handle_reorg(self, reorg_event: ReorgEvent, sync_state: ChainSyncState):
        """Handle blockchain reorganization"""
        logger.warning(f"Reorg detected on {reorg_event.chain}: depth {reorg_event.reorg_depth}")
        
        try:
            sync_state.sync_status = SyncStatus.REORG_DETECTED
            sync_state.reorg_depth = reorg_event.reorg_depth
            
            # Remove affected data from database
            for block_num in reorg_event.affected_blocks:
                affected_data = await get_blockchain_data_by_filters({
                    'blockchain': reorg_event.chain,
                    'block_number': block_num
                })
                
                for data in affected_data:
                    reorg_event.affected_transactions.append(data.transaction_hash)
                    await delete_blockchain_data(data.id)
            
            # Reset sync position to before reorg
            sync_state.last_synced_block = min(reorg_event.affected_blocks) - 1
            
            # Store reorg event
            self.reorg_events.append(reorg_event)
            
            logger.info(f"Handled reorg on {reorg_event.chain}: "
                       f"removed {len(reorg_event.affected_transactions)} transactions")
            
        except Exception as e:
            logger.error(f"Error handling reorg: {e}")
    
    async def _sync_new_blocks(self, chain: str, sync_state: ChainSyncState):
        """Sync new blocks for a chain"""
        try:
            start_block = sync_state.last_synced_block + 1
            end_block = min(
                start_block + self.max_blocks_per_sync - 1,
                sync_state.latest_block
            )
            
            if start_block > end_block:
                return  # Nothing to sync
            
            sync_start_time = datetime.utcnow()
            
            # Process blocks in batches
            for block_num in range(start_block, end_block + 1):
                await self._process_block(chain, block_num)
                sync_state.last_synced_block = block_num
            
            # Calculate sync rate
            sync_duration = (datetime.utcnow() - sync_start_time).total_seconds()
            blocks_synced = end_block - start_block + 1
            sync_state.sync_rate = blocks_synced / max(sync_duration, 0.1)
            
            logger.info(f"Synced {blocks_synced} blocks for {chain} "
                       f"(rate: {sync_state.sync_rate:.2f} blocks/sec)")
            
        except Exception as e:
            logger.error(f"Error syncing new blocks for {chain}: {e}")
            raise
    
    async def _process_block(self, chain: str, block_number: int):
        """Process a single block"""
        try:
            client = multi_chain_manager.get_client(chain)
            if not client:
                return
            
            # Get block data
            block = await client.get_block(block_number)
            if not block:
                return
            
            # Store block metadata
            block_data = {
                'blockchain': chain,
                'block_number': block_number,
                'transaction_hash': '',  # No specific transaction
                'contract_address': '',
                'event_type': 'block_processed',
                'event_data': {
                    'block_hash': block['hash'],
                    'timestamp': block['timestamp'],
                    'transaction_count': len(block.get('transactions', [])),
                    'gas_used': block.get('gasUsed', 0),
                    'gas_limit': block.get('gasLimit', 0)
                },
                'block_timestamp': datetime.fromtimestamp(block['timestamp'])
            }
            
            await create_blockchain_data(block_data)
            
            # Process transactions in the block
            transactions = block.get('transactions', [])
            for tx in transactions:
                if isinstance(tx, dict):  # Full transaction object
                    await self._process_transaction(chain, tx, block)
            
        except Exception as e:
            logger.error(f"Error processing block {block_number} on {chain}: {e}")
    
    async def _process_transaction(self, chain: str, transaction: Dict, block: Dict):
        """Process a single transaction"""
        try:
            # Check if transaction is related to gaming contracts
            to_address = transaction.get('to', '').lower()
            from_address = transaction.get('from', '').lower()
            
            # Check if it's a gaming contract interaction
            gaming_contract = None
            if to_address:
                gaming_contract = gaming_contract_manager.get_contract(chain, to_address)
            
            if gaming_contract:
                # This is a gaming contract transaction
                tx_data = {
                    'blockchain': chain,
                    'block_number': block['number'],
                    'transaction_hash': transaction['hash'],
                    'contract_address': to_address,
                    'event_type': 'gaming_transaction',
                    'from_address': from_address,
                    'to_address': to_address,
                    'gas_used': transaction.get('gas'),
                    'gas_price': float(transaction.get('gasPrice', 0)) / 1e18 if transaction.get('gasPrice') else None,
                    'block_timestamp': datetime.fromtimestamp(block['timestamp']),
                    'event_data': {
                        'value': str(transaction.get('value', 0)),
                        'input_data': transaction.get('input', ''),
                        'gaming_project': gaming_contract.name,
                        'contract_type': gaming_contract.contract_type.value
                    }
                }
                
                await create_blockchain_data(tx_data)
            
        except Exception as e:
            logger.error(f"Error processing transaction {transaction.get('hash')}: {e}")
    
    async def _sync_market_data(self):
        """Sync market data for gaming tokens"""
        logger.info("Starting market data synchronization")
        
        while self.sync_active:
            try:
                # Get prices for all gaming tokens
                prices = await gaming_market_data.get_all_gaming_token_prices()
                
                # Store price data
                for symbol, price_data in prices.items():
                    price_entry = {
                        'blockchain': price_data.chain,
                        'block_number': 0,  # Market data doesn't have block number
                        'transaction_hash': '',
                        'contract_address': price_data.contract_address,
                        'event_type': 'price_update',
                        'token_symbol': symbol,
                        'event_data': {
                            'price_usd': price_data.price_usd,
                            'price_change_24h': price_data.price_change_24h,
                            'market_cap': price_data.market_cap,
                            'volume_24h': price_data.volume_24h,
                            'circulating_supply': price_data.circulating_supply,
                            'total_supply': price_data.total_supply
                        },
                        'block_timestamp': price_data.last_updated
                    }
                    
                    await create_blockchain_data(price_entry)
                
                logger.info(f"Updated prices for {len(prices)} gaming tokens")
                
                # Wait longer for market data (prices don't change as frequently)
                await asyncio.sleep(300)  # 5 minutes
                
            except Exception as e:
                logger.error(f"Error syncing market data: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    def get_sync_status(self) -> Dict[str, Any]:
        """Get synchronization status for all chains"""
        status = {
            'sync_active': self.sync_active,
            'total_chains': len(self.sync_states),
            'chains': {},
            'reorg_events': len(self.reorg_events),
            'recent_reorgs': []
        }
        
        for chain, sync_state in self.sync_states.items():
            status['chains'][chain] = {
                'last_synced_block': sync_state.last_synced_block,
                'latest_block': sync_state.latest_block,
                'blocks_behind': sync_state.blocks_behind,
                'sync_status': sync_state.sync_status.value,
                'last_sync_time': sync_state.last_sync_time.isoformat(),
                'error_count': sync_state.error_count,
                'sync_rate': sync_state.sync_rate,
                'reorg_depth': sync_state.reorg_depth
            }
        
        # Add recent reorg events
        recent_reorgs = [
            {
                'chain': reorg.chain,
                'detected_at': reorg.detected_at.isoformat(),
                'reorg_depth': reorg.reorg_depth,
                'affected_transactions': len(reorg.affected_transactions)
            }
            for reorg in self.reorg_events[-10:]  # Last 10 reorgs
        ]
        status['recent_reorgs'] = recent_reorgs
        
        return status
    
    async def force_resync_chain(self, chain: str, from_block: int = 0):
        """Force resync a chain from a specific block"""
        if chain in self.sync_states:
            self.sync_states[chain].last_synced_block = from_block
            self.sync_states[chain].sync_status = SyncStatus.SYNCING
            self.sync_states[chain].error_count = 0
            logger.info(f"Forced resync for {chain} from block {from_block}")


# Global blockchain sync manager instance
blockchain_sync_manager = BlockchainSyncManager()
