"""
Gaming Protocol Analytics System
Comprehensive on-chain gaming data collection and analytics
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

from .multi_chain_client import multi_chain_manager
from .data_clients.manager import BlockchainDataManager
from .gaming_contracts import gaming_contract_manager
from .market_data import gaming_market_data
from models.gaming import GamingProject, BlockchainData
from models.base import SessionLocal
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of gaming protocol metrics"""
    USER_ACTIVITY = "user_activity"
    TRANSACTION_VOLUME = "transaction_volume"
    TOKEN_METRICS = "token_metrics"
    NFT_ACTIVITY = "nft_activity"
    TVL = "total_value_locked"
    PROTOCOL_HEALTH = "protocol_health"
    P2E_ECONOMICS = "p2e_economics"


@dataclass
class GamingProtocolMetrics:
    """Gaming protocol metrics data structure"""
    protocol_name: str
    chain: str
    timestamp: datetime
    
    # User metrics
    daily_active_users: Optional[int] = None
    monthly_active_users: Optional[int] = None
    new_users_24h: Optional[int] = None
    user_retention_rate: Optional[float] = None
    
    # Transaction metrics
    transaction_count_24h: Optional[int] = None
    transaction_volume_24h: Optional[float] = None
    average_transaction_value: Optional[float] = None
    gas_fees_24h: Optional[float] = None
    
    # Token metrics
    token_price: Optional[float] = None
    token_price_change_24h: Optional[float] = None
    market_cap: Optional[float] = None
    trading_volume_24h: Optional[float] = None
    circulating_supply: Optional[float] = None
    
    # NFT metrics
    nft_trades_24h: Optional[int] = None
    nft_volume_24h: Optional[float] = None
    floor_price: Optional[float] = None
    unique_holders: Optional[int] = None
    
    # Protocol-specific metrics
    total_value_locked: Optional[float] = None
    protocol_revenue_24h: Optional[float] = None
    staking_rewards_distributed: Optional[float] = None
    
    # P2E economics
    average_earnings_per_user: Optional[float] = None
    reward_token_distribution: Optional[float] = None
    gameplay_sessions_24h: Optional[int] = None
    
    # Health indicators
    protocol_uptime: Optional[float] = None
    smart_contract_interactions: Optional[int] = None
    developer_activity_score: Optional[float] = None
    
    # Additional metadata
    raw_data: Dict[str, Any] = field(default_factory=dict)


class GamingProtocolAnalytics:
    """Main gaming protocol analytics system"""
    
    def __init__(self):
        self.blockchain_data_manager = BlockchainDataManager()
        self.metrics_cache: Dict[str, GamingProtocolMetrics] = {}
        self.cache_duration = timedelta(minutes=30)
        self.supported_protocols = self._load_supported_protocols()
        
    def _load_supported_protocols(self) -> Dict[str, Dict[str, Any]]:
        """Load supported gaming protocols configuration"""
        return {
            'axie-infinity': {
                'name': 'Axie Infinity',
                'chains': ['ethereum', 'ronin'],
                'contracts': {
                    'ethereum': {
                        'token': '******************************************',  # AXS
                        'nft': '******************************************',     # Axie NFT
                    },
                    'ronin': {
                        'token': '******************************************',  # AXS on Ronin
                        'slp': '******************************************',    # SLP
                    }
                },
                'metrics_sources': ['flipside', 'dextools', 'cryptorank']
            },
            'the-sandbox': {
                'name': 'The Sandbox',
                'chains': ['ethereum', 'polygon'],
                'contracts': {
                    'ethereum': {
                        'token': '******************************************',  # SAND
                        'land': '******************************************',   # LAND NFT
                        'assets': '******************************************', # ASSETS NFT
                    }
                },
                'metrics_sources': ['flipside', 'cryptorank']
            },
            'decentraland': {
                'name': 'Decentraland',
                'chains': ['ethereum', 'polygon'],
                'contracts': {
                    'ethereum': {
                        'token': '******************************************',  # MANA
                        'land': '******************************************',   # LAND
                        'estates': '******************************************', # Estates
                    }
                },
                'metrics_sources': ['flipside', 'cryptorank']
            },
            'splinterlands': {
                'name': 'Splinterlands',
                'chains': ['hive', 'bsc'],
                'contracts': {
                    'bsc': {
                        'token': '******************************************',  # SPS
                    }
                },
                'metrics_sources': ['cryptorank']
            },
            'gods-unchained': {
                'name': 'Gods Unchained',
                'chains': ['ethereum', 'immutable'],
                'contracts': {
                    'ethereum': {
                        'token': '******************************************',  # GODS
                        'cards': '******************************************',  # Cards
                    }
                },
                'metrics_sources': ['flipside', 'cryptorank']
            },
            'illuvium': {
                'name': 'Illuvium',
                'chains': ['ethereum'],
                'contracts': {
                    'ethereum': {
                        'token': '******************************************',  # ILV
                    }
                },
                'metrics_sources': ['cryptorank']
            },
            'gala-games': {
                'name': 'Gala Games',
                'chains': ['ethereum'],
                'contracts': {
                    'ethereum': {
                        'token': '******************************************',  # GALA
                    }
                },
                'metrics_sources': ['cryptorank']
            },
            'enjin': {
                'name': 'Enjin',
                'chains': ['ethereum'],
                'contracts': {
                    'ethereum': {
                        'token': '******************************************',  # ENJ
                    }
                },
                'metrics_sources': ['cryptorank']
            }
        }
    
    async def initialize(self):
        """Initialize the analytics system"""
        logger.info("🎮 Initializing Gaming Protocol Analytics...")
        await self.blockchain_data_manager.initialize_clients()
        logger.info(f"✅ Gaming Protocol Analytics initialized with {len(self.supported_protocols)} protocols")
    
    async def collect_protocol_metrics(self, protocol_name: str) -> Optional[GamingProtocolMetrics]:
        """Collect comprehensive metrics for a gaming protocol"""
        if protocol_name not in self.supported_protocols:
            logger.warning(f"Protocol {protocol_name} not supported")
            return None
        
        # Check cache first
        cache_key = f"{protocol_name}_{datetime.now().strftime('%Y%m%d_%H')}"
        if cache_key in self.metrics_cache:
            cached_metrics = self.metrics_cache[cache_key]
            if datetime.now() - cached_metrics.timestamp < self.cache_duration:
                return cached_metrics
        
        protocol_config = self.supported_protocols[protocol_name]
        logger.info(f"📊 Collecting metrics for {protocol_config['name']}...")
        
        # Initialize metrics object
        metrics = GamingProtocolMetrics(
            protocol_name=protocol_name,
            chain=protocol_config['chains'][0],  # Primary chain
            timestamp=datetime.now()
        )
        
        # Collect metrics from different sources
        tasks = [
            self._collect_token_metrics(protocol_name, protocol_config, metrics),
            self._collect_user_activity_metrics(protocol_name, protocol_config, metrics),
            self._collect_nft_metrics(protocol_name, protocol_config, metrics),
            self._collect_protocol_health_metrics(protocol_name, protocol_config, metrics),
        ]
        
        try:
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # Cache the results
            self.metrics_cache[cache_key] = metrics
            
            logger.info(f"✅ Collected metrics for {protocol_config['name']}")
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Failed to collect metrics for {protocol_name}: {e}")
            return None
    
    async def _collect_token_metrics(self, protocol_name: str, config: Dict, metrics: GamingProtocolMetrics):
        """Collect token-related metrics"""
        try:
            primary_chain = config['chains'][0]
            contracts = config['contracts'].get(primary_chain, {})
            
            if 'token' in contracts:
                token_address = contracts['token']
                
                # Get token price and market data
                token_data = await gaming_market_data.get_token_price(primary_chain, token_address)
                if token_data:
                    metrics.token_price = token_data.price_usd
                    metrics.token_price_change_24h = token_data.price_change_24h
                    metrics.market_cap = getattr(token_data, 'market_cap', None)
                    metrics.trading_volume_24h = getattr(token_data, 'volume_24h', None)
                    metrics.circulating_supply = getattr(token_data, 'circulating_supply', None)
                
                # Get additional token metrics from blockchain data clients
                if 'flipside' in self.blockchain_data_manager.clients:
                    try:
                        flipside_data = await self.blockchain_data_manager.get_gaming_tokens_data([token_address])
                        if flipside_data and 'flipside' in flipside_data:
                            token_metrics = flipside_data['flipside']
                            metrics.transaction_count_24h = token_metrics.get('transaction_count_24h')
                            metrics.transaction_volume_24h = token_metrics.get('volume_24h_usd')
                            metrics.unique_holders = token_metrics.get('unique_holders')
                    except Exception as e:
                        logger.debug(f"Flipside token metrics failed for {protocol_name}: {e}")
                
        except Exception as e:
            logger.error(f"Failed to collect token metrics for {protocol_name}: {e}")

    async def _collect_user_activity_metrics(self, protocol_name: str, config: Dict, metrics: GamingProtocolMetrics):
        """Collect user activity metrics"""
        try:
            # Get user activity data from blockchain data clients
            if 'flipside' in self.blockchain_data_manager.clients:
                try:
                    protocol_data = await self.blockchain_data_manager.get_gaming_protocol_metrics(protocol_name)
                    if protocol_data and 'flipside' in protocol_data:
                        flipside_metrics = protocol_data['flipside']
                        metrics.daily_active_users = flipside_metrics.get('daily_active_users')
                        metrics.monthly_active_users = flipside_metrics.get('monthly_active_users')
                        metrics.new_users_24h = flipside_metrics.get('new_users_24h')
                        metrics.user_retention_rate = flipside_metrics.get('user_retention_rate')
                        metrics.gameplay_sessions_24h = flipside_metrics.get('gameplay_sessions_24h')
                except Exception as e:
                    logger.debug(f"Flipside user metrics failed for {protocol_name}: {e}")

            # Get on-chain activity from direct blockchain analysis
            primary_chain = config['chains'][0]
            contracts = config['contracts'].get(primary_chain, {})

            if contracts:
                # Analyze recent transactions for user activity
                chain_client = multi_chain_manager.get_client(primary_chain)
                if chain_client:
                    # Get recent blocks and analyze unique addresses
                    try:
                        latest_block = await chain_client.get_latest_block_number()
                        if latest_block:
                            # Analyze last 24 hours of blocks (approximate)
                            blocks_per_day = 7200 if primary_chain == 'ethereum' else 28800  # Rough estimates
                            start_block = max(1, latest_block - blocks_per_day)

                            unique_addresses = set()
                            transaction_count = 0

                            # Sample every 100th block to avoid overwhelming the RPC
                            for block_num in range(start_block, latest_block, 100):
                                try:
                                    block_data = await chain_client.get_block(block_num)
                                    if block_data and 'transactions' in block_data:
                                        for tx in block_data['transactions']:
                                            if isinstance(tx, dict):
                                                # Check if transaction involves our contracts
                                                tx_to = tx.get('to', '').lower()
                                                if any(addr.lower() == tx_to for addr in contracts.values()):
                                                    unique_addresses.add(tx.get('from', '').lower())
                                                    transaction_count += 1
                                except Exception as e:
                                    logger.debug(f"Block analysis failed for block {block_num}: {e}")
                                    continue

                            if not metrics.daily_active_users and unique_addresses:
                                # Estimate daily active users based on sampled data
                                estimated_dau = len(unique_addresses) * 100  # Scale up from sampling
                                metrics.daily_active_users = estimated_dau

                            if not metrics.transaction_count_24h and transaction_count:
                                estimated_tx_count = transaction_count * 100  # Scale up from sampling
                                metrics.transaction_count_24h = estimated_tx_count

                    except Exception as e:
                        logger.debug(f"On-chain analysis failed for {protocol_name}: {e}")

        except Exception as e:
            logger.error(f"Failed to collect user activity metrics for {protocol_name}: {e}")

    async def _collect_nft_metrics(self, protocol_name: str, config: Dict, metrics: GamingProtocolMetrics):
        """Collect NFT-related metrics"""
        try:
            primary_chain = config['chains'][0]
            contracts = config['contracts'].get(primary_chain, {})

            # Look for NFT contracts (land, assets, cards, etc.)
            nft_contracts = {k: v for k, v in contracts.items() if k in ['nft', 'land', 'assets', 'cards']}

            if nft_contracts:
                for contract_type, contract_address in nft_contracts.items():
                    try:
                        # Get NFT collection data
                        nft_data = await self.blockchain_data_manager.get_nft_collection_data(contract_address)

                        if nft_data:
                            for source, data in nft_data.items():
                                if isinstance(data, dict):
                                    # Extract NFT metrics
                                    if not metrics.floor_price and 'floor_price' in data:
                                        metrics.floor_price = data['floor_price']

                                    if not metrics.nft_volume_24h and 'volume_24h' in data:
                                        metrics.nft_volume_24h = data['volume_24h']

                                    if not metrics.nft_trades_24h and 'trades_24h' in data:
                                        metrics.nft_trades_24h = data['trades_24h']

                                    if not metrics.unique_holders and 'unique_holders' in data:
                                        metrics.unique_holders = data['unique_holders']

                    except Exception as e:
                        logger.debug(f"NFT metrics failed for {contract_address}: {e}")

        except Exception as e:
            logger.error(f"Failed to collect NFT metrics for {protocol_name}: {e}")

    async def _collect_protocol_health_metrics(self, protocol_name: str, config: Dict, metrics: GamingProtocolMetrics):
        """Collect protocol health and performance metrics"""
        try:
            # Test protocol uptime by checking RPC connectivity
            uptime_checks = []
            for chain in config['chains']:
                chain_client = multi_chain_manager.get_client(chain)
                if chain_client:
                    try:
                        is_connected = await chain_client.test_connection()
                        uptime_checks.append(1.0 if is_connected else 0.0)
                    except Exception:
                        uptime_checks.append(0.0)

            if uptime_checks:
                metrics.protocol_uptime = sum(uptime_checks) / len(uptime_checks)

            # Get smart contract interaction count
            primary_chain = config['chains'][0]
            contracts = config['contracts'].get(primary_chain, {})

            if contracts and metrics.transaction_count_24h:
                # Use transaction count as proxy for smart contract interactions
                metrics.smart_contract_interactions = metrics.transaction_count_24h

            # Calculate developer activity score based on recent updates
            # This is a simplified metric - in production, you'd integrate with GitHub API
            metrics.developer_activity_score = 0.8  # Placeholder - would be calculated from real data

            # Calculate TVL if we have DeFi components
            if protocol_name in ['axie-infinity', 'the-sandbox', 'decentraland']:
                try:
                    # Get TVL data from DeFi protocols associated with gaming
                    tvl_data = await self._get_protocol_tvl(protocol_name, config)
                    if tvl_data:
                        metrics.total_value_locked = tvl_data
                except Exception as e:
                    logger.debug(f"TVL calculation failed for {protocol_name}: {e}")

        except Exception as e:
            logger.error(f"Failed to collect protocol health metrics for {protocol_name}: {e}")

    async def _get_protocol_tvl(self, protocol_name: str, config: Dict) -> Optional[float]:
        """Calculate Total Value Locked for gaming protocol"""
        try:
            # This would integrate with DeFi TVL APIs like DefiLlama
            # For now, return estimated values based on known protocols
            tvl_estimates = {
                'axie-infinity': 45000000.0,  # $45M estimated
                'the-sandbox': 25000000.0,   # $25M estimated
                'decentraland': 15000000.0,  # $15M estimated
            }
            return tvl_estimates.get(protocol_name)
        except Exception as e:
            logger.error(f"TVL calculation failed for {protocol_name}: {e}")
            return None

    async def collect_all_protocols_metrics(self) -> Dict[str, GamingProtocolMetrics]:
        """Collect metrics for all supported protocols"""
        logger.info("📊 Collecting metrics for all gaming protocols...")

        results = {}
        tasks = []

        for protocol_name in self.supported_protocols.keys():
            tasks.append(self.collect_protocol_metrics(protocol_name))

        try:
            metrics_list = await asyncio.gather(*tasks, return_exceptions=True)

            for i, protocol_name in enumerate(self.supported_protocols.keys()):
                if i < len(metrics_list) and not isinstance(metrics_list[i], Exception):
                    results[protocol_name] = metrics_list[i]
                else:
                    logger.warning(f"Failed to collect metrics for {protocol_name}")

            logger.info(f"✅ Collected metrics for {len(results)} protocols")
            return results

        except Exception as e:
            logger.error(f"❌ Failed to collect all protocol metrics: {e}")
            return {}

    async def get_protocol_summary(self) -> Dict[str, Any]:
        """Get summary of all gaming protocol metrics"""
        all_metrics = await self.collect_all_protocols_metrics()

        summary = {
            'total_protocols': len(all_metrics),
            'total_market_cap': 0.0,
            'total_daily_active_users': 0,
            'total_transaction_volume_24h': 0.0,
            'total_nft_volume_24h': 0.0,
            'average_protocol_uptime': 0.0,
            'protocols': {}
        }

        uptime_values = []

        for protocol_name, metrics in all_metrics.items():
            if metrics:
                # Add to totals
                if metrics.market_cap:
                    summary['total_market_cap'] += metrics.market_cap
                if metrics.daily_active_users:
                    summary['total_daily_active_users'] += metrics.daily_active_users
                if metrics.transaction_volume_24h:
                    summary['total_transaction_volume_24h'] += metrics.transaction_volume_24h
                if metrics.nft_volume_24h:
                    summary['total_nft_volume_24h'] += metrics.nft_volume_24h
                if metrics.protocol_uptime:
                    uptime_values.append(metrics.protocol_uptime)

                # Add individual protocol summary
                summary['protocols'][protocol_name] = {
                    'name': self.supported_protocols[protocol_name]['name'],
                    'market_cap': metrics.market_cap,
                    'daily_active_users': metrics.daily_active_users,
                    'token_price': metrics.token_price,
                    'token_price_change_24h': metrics.token_price_change_24h,
                    'protocol_uptime': metrics.protocol_uptime,
                    'last_updated': metrics.timestamp.isoformat()
                }

        if uptime_values:
            summary['average_protocol_uptime'] = sum(uptime_values) / len(uptime_values)

        summary['last_updated'] = datetime.now().isoformat()
        return summary


# Create global instance
gaming_analytics = GamingProtocolAnalytics()
