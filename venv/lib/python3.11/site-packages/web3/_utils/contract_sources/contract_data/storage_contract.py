"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.23.
"""

# source: web3/_utils/contract_sources/StorageContract.sol:StorageContract
STORAGE_CONTRACT_BYTECODE = "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"  # noqa: E501
STORAGE_CONTRACT_RUNTIME = "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"  # noqa: E501
STORAGE_CONTRACT_ABI = [
    {
        "inputs": [],
        "name": "slot0",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "slot1",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "slot2",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "slot3",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "slot4",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
]
STORAGE_CONTRACT_DATA = {
    "bytecode": STORAGE_CONTRACT_BYTECODE,
    "bytecode_runtime": STORAGE_CONTRACT_RUNTIME,
    "abi": STORAGE_CONTRACT_ABI,
}
