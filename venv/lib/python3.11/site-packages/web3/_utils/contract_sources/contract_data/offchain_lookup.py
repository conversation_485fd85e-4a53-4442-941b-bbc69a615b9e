"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.23.
"""

# source: web3/_utils/contract_sources/OffchainLookup.sol:OffchainLookup
OFFCHAIN_LOOKUP_BYTECODE = "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"  # noqa: E501
OFFCHAIN_LOOKUP_RUNTIME = "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"  # noqa: E501
OFFCHAIN_LOOKUP_ABI = [
    {
        "inputs": [
            {"internalType": "address", "name": "sender", "type": "address"},
            {"internalType": "string[]", "name": "urls", "type": "string[]"},
            {"internalType": "bytes", "name": "callData", "type": "bytes"},
            {"internalType": "bytes4", "name": "callbackFunction", "type": "bytes4"},
            {"internalType": "bytes", "name": "extraData", "type": "bytes"},
        ],
        "name": "OffchainLookup",
        "type": "error",
    },
    {
        "inputs": [],
        "name": "continuousOffchainLookup",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "specifiedDataFromTest", "type": "bytes"}
        ],
        "name": "testOffchainLookup",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "result", "type": "bytes"},
            {"internalType": "bytes", "name": "extraData", "type": "bytes"},
        ],
        "name": "testOffchainLookupWithProof",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
OFFCHAIN_LOOKUP_DATA = {
    "bytecode": OFFCHAIN_LOOKUP_BYTECODE,
    "bytecode_runtime": OFFCHAIN_LOOKUP_RUNTIME,
    "abi": OFFCHAIN_LOOKUP_ABI,
}
