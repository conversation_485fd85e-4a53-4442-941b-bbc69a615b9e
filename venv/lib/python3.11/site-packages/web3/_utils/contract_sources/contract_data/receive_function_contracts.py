"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.23.
"""

# source: web3/_utils/contract_sources/ReceiveFunctionContracts.sol:ReceiveFunctionContract  # noqa: E501
RECEIVE_FUNCTION_CONTRACT_BYTECODE = "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"  # noqa: E501
RECEIVE_FUNCTION_CONTRACT_RUNTIME = "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"  # noqa: E501
RECEIVE_FUNCTION_CONTRACT_ABI = [
    {"stateMutability": "payable", "type": "fallback"},
    {
        "inputs": [],
        "name": "getText",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "string", "name": "new_text", "type": "string"}],
        "name": "setText",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {"stateMutability": "payable", "type": "receive"},
]
RECEIVE_FUNCTION_CONTRACT_DATA = {
    "bytecode": RECEIVE_FUNCTION_CONTRACT_BYTECODE,
    "bytecode_runtime": RECEIVE_FUNCTION_CONTRACT_RUNTIME,
    "abi": RECEIVE_FUNCTION_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/ReceiveFunctionContracts.sol:NoReceiveFunctionContract  # noqa: E501
NO_RECEIVE_FUNCTION_CONTRACT_BYTECODE = "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"  # noqa: E501
NO_RECEIVE_FUNCTION_CONTRACT_RUNTIME = "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"  # noqa: E501
NO_RECEIVE_FUNCTION_CONTRACT_ABI = [
    {"stateMutability": "nonpayable", "type": "fallback"},
    {
        "inputs": [],
        "name": "getText",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "string", "name": "new_text", "type": "string"}],
        "name": "setText",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
NO_RECEIVE_FUNCTION_CONTRACT_DATA = {
    "bytecode": NO_RECEIVE_FUNCTION_CONTRACT_BYTECODE,
    "bytecode_runtime": NO_RECEIVE_FUNCTION_CONTRACT_RUNTIME,
    "abi": NO_RECEIVE_FUNCTION_CONTRACT_ABI,
}
