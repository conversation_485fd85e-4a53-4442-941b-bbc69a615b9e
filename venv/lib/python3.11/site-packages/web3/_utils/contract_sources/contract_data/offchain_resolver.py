"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.23.
"""

# source: web3/_utils/contract_sources/OffchainResolver.sol:OffchainResolver
OFFCHAIN_RESOLVER_BYTECODE = "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"  # noqa: E501
OFFCHAIN_RESOLVER_RUNTIME = "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"  # noqa: E501
OFFCHAIN_RESOLVER_ABI = [
    {
        "inputs": [
            {"internalType": "string[]", "name": "_urls", "type": "string[]"},
            {"internalType": "address[]", "name": "_signers", "type": "address[]"},
        ],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [
            {"internalType": "address", "name": "sender", "type": "address"},
            {"internalType": "string[]", "name": "urls", "type": "string[]"},
            {"internalType": "bytes", "name": "callData", "type": "bytes"},
            {"internalType": "bytes4", "name": "callbackFunction", "type": "bytes4"},
            {"internalType": "bytes", "name": "extraData", "type": "bytes"},
        ],
        "name": "OffchainLookup",
        "type": "error",
    },
    {
        "anonymous": False,
        "inputs": [
            {
                "indexed": False,
                "internalType": "address[]",
                "name": "signers",
                "type": "address[]",
            }
        ],
        "name": "NewSigners",
        "type": "event",
    },
    {
        "inputs": [
            {"internalType": "address", "name": "target", "type": "address"},
            {"internalType": "uint64", "name": "expires", "type": "uint64"},
            {"internalType": "bytes", "name": "request", "type": "bytes"},
            {"internalType": "bytes", "name": "result", "type": "bytes"},
        ],
        "name": "makeSignatureHash",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "name", "type": "bytes"},
            {"internalType": "bytes", "name": "data", "type": "bytes"},
        ],
        "name": "resolve",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [
            {"internalType": "bytes", "name": "response", "type": "bytes"},
            {"internalType": "bytes", "name": "extraData", "type": "bytes"},
        ],
        "name": "resolveWithProof",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "address", "name": "", "type": "address"}],
        "name": "signers",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes4", "name": "interfaceID", "type": "bytes4"}],
        "name": "supportsInterface",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "urls",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
]
OFFCHAIN_RESOLVER_DATA = {
    "bytecode": OFFCHAIN_RESOLVER_BYTECODE,
    "bytecode_runtime": OFFCHAIN_RESOLVER_RUNTIME,
    "abi": OFFCHAIN_RESOLVER_ABI,
}
