"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.23.
"""

# source: web3/_utils/contract_sources/BytesContracts.sol:BytesContract
BYTES_CONTRACT_BYTECODE = "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"  # noqa: E501
BYTES_CONTRACT_RUNTIME = "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"  # noqa: E501
BYTES_CONTRACT_ABI = [
    {
        "inputs": [{"internalType": "bytes", "name": "_value", "type": "bytes"}],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [],
        "name": "constValue",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getValue",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes", "name": "_value", "type": "bytes"}],
        "name": "setValue",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
BYTES_CONTRACT_DATA = {
    "bytecode": BYTES_CONTRACT_BYTECODE,
    "bytecode_runtime": BYTES_CONTRACT_RUNTIME,
    "abi": BYTES_CONTRACT_ABI,
}


# source: web3/_utils/contract_sources/BytesContracts.sol:Bytes32Contract
BYTES32_CONTRACT_BYTECODE = "0x60806040527f01230123012301230123012301230123012301230123012301230123012301235f55348015610032575f80fd5b5060405161024638038061024683398181016040528101906100549190610098565b80600181905550506100c3565b5f80fd5b5f819050919050565b61007781610065565b8114610081575f80fd5b50565b5f815190506100928161006e565b92915050565b5f602082840312156100ad576100ac610061565b5b5f6100ba84828501610084565b91505092915050565b610176806100d05f395ff3fe608060405234801561000f575f80fd5b506004361061003f575f3560e01c8063209652551461004357806330de3cee1461006157806358825b101461007f575b5f80fd5b61004b61009b565b60405161005891906100ce565b60405180910390f35b6100696100a4565b60405161007691906100ce565b60405180910390f35b61009960048036038101906100949190610115565b6100ac565b005b5f600154905090565b5f8054905090565b8060018190555050565b5f819050919050565b6100c8816100b6565b82525050565b5f6020820190506100e15f8301846100bf565b92915050565b5f80fd5b6100f4816100b6565b81146100fe575f80fd5b50565b5f8135905061010f816100eb565b92915050565b5f6020828403121561012a576101296100e7565b5b5f61013784828501610101565b9150509291505056fea2646970667358221220de78bb3b4ce5abca65e95cbab8873d9c91e78faac61ecc0c947d037bb15b719864736f6c63430008170033"  # noqa: E501
BYTES32_CONTRACT_RUNTIME = "0x608060405234801561000f575f80fd5b506004361061003f575f3560e01c8063209652551461004357806330de3cee1461006157806358825b101461007f575b5f80fd5b61004b61009b565b60405161005891906100ce565b60405180910390f35b6100696100a4565b60405161007691906100ce565b60405180910390f35b61009960048036038101906100949190610115565b6100ac565b005b5f600154905090565b5f8054905090565b8060018190555050565b5f819050919050565b6100c8816100b6565b82525050565b5f6020820190506100e15f8301846100bf565b92915050565b5f80fd5b6100f4816100b6565b81146100fe575f80fd5b50565b5f8135905061010f816100eb565b92915050565b5f6020828403121561012a576101296100e7565b5b5f61013784828501610101565b9150509291505056fea2646970667358221220de78bb3b4ce5abca65e95cbab8873d9c91e78faac61ecc0c947d037bb15b719864736f6c63430008170033"  # noqa: E501
BYTES32_CONTRACT_ABI = [
    {
        "inputs": [{"internalType": "bytes32", "name": "_value", "type": "bytes32"}],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {
        "inputs": [],
        "name": "constValue",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "getValue",
        "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "bytes32", "name": "_value", "type": "bytes32"}],
        "name": "setValue",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
]
BYTES32_CONTRACT_DATA = {
    "bytecode": BYTES32_CONTRACT_BYTECODE,
    "bytecode_runtime": BYTES32_CONTRACT_RUNTIME,
    "abi": BYTES32_CONTRACT_ABI,
}
