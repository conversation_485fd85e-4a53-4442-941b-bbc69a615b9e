"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.23.
"""

# source: web3/_utils/contract_sources/ContractCallerTester.sol:ContractCallerTester
CONTRACT_CALLER_TESTER_BYTECODE = "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"  # noqa: E501
CONTRACT_CALLER_TESTER_RUNTIME = "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"  # noqa: E501
CONTRACT_CALLER_TESTER_ABI = [
    {
        "inputs": [
            {"internalType": "int256", "name": "a", "type": "int256"},
            {"internalType": "int256", "name": "b", "type": "int256"},
        ],
        "name": "add",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "count",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "counter",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "increment",
        "outputs": [{"internalType": "int256", "name": "", "type": "int256"}],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "returnMeta",
        "outputs": [
            {"internalType": "address", "name": "", "type": "address"},
            {"internalType": "bytes", "name": "", "type": "bytes"},
            {"internalType": "uint256", "name": "", "type": "uint256"},
            {"internalType": "uint256", "name": "", "type": "uint256"},
            {"internalType": "uint256", "name": "", "type": "uint256"},
        ],
        "stateMutability": "payable",
        "type": "function",
    },
]
CONTRACT_CALLER_TESTER_DATA = {
    "bytecode": CONTRACT_CALLER_TESTER_BYTECODE,
    "bytecode_runtime": CONTRACT_CALLER_TESTER_RUNTIME,
    "abi": CONTRACT_CALLER_TESTER_ABI,
}
