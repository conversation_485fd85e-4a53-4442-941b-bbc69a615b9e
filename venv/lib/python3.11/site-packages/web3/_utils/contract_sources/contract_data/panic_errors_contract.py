"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.23.
"""

# source: web3/_utils/contract_sources/PanicErrorsContract.sol:PanicErrorsContract
PANIC_ERRORS_CONTRACT_BYTECODE = "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"  # noqa: E501
PANIC_ERRORS_CONTRACT_RUNTIME = "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"  # noqa: E501
PANIC_ERRORS_CONTRACT_ABI = [
    {
        "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "name": "emptyArray",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode01",
        "outputs": [],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode11",
        "outputs": [],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "uint256", "name": "zero", "type": "uint256"}],
        "name": "errorCode12",
        "outputs": [],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "int256", "name": "negativeInt", "type": "int256"}],
        "name": "errorCode21",
        "outputs": [],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode22",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode31",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode32",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode41",
        "outputs": [],
        "stateMutability": "pure",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "errorCode51",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "x",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "y",
        "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}],
        "stateMutability": "view",
        "type": "function",
    },
]
PANIC_ERRORS_CONTRACT_DATA = {
    "bytecode": PANIC_ERRORS_CONTRACT_BYTECODE,
    "bytecode_runtime": PANIC_ERRORS_CONTRACT_RUNTIME,
    "abi": PANIC_ERRORS_CONTRACT_ABI,
}
