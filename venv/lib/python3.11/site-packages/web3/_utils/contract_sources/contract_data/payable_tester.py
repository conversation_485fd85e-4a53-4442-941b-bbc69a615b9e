"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.23.
"""

# source: web3/_utils/contract_sources/PayableTester.sol:PayableTesterContract
PAYABLE_TESTER_CONTRACT_BYTECODE = "0x608060405234801561000f575f80fd5b5060e68061001c5f395ff3fe6080604052348015600e575f80fd5b50600436106030575f3560e01c8063c6803622146034578063e4cb8f5c14604e575b5f80fd5b603a6056565b604051604591906099565b60405180910390f35b60546066565b005b5f8054906101000a900460ff1681565b60015f806101000a81548160ff021916908315150217905550565b5f8115159050919050565b6093816081565b82525050565b5f60208201905060aa5f830184608c565b9291505056fea26469706673582212205035f139f1fe97a5af13c06ceda5c8789a427740c0533636f81c79307019759464736f6c63430008170033"  # noqa: E501
PAYABLE_TESTER_CONTRACT_RUNTIME = "0x6080604052348015600e575f80fd5b50600436106030575f3560e01c8063c6803622146034578063e4cb8f5c14604e575b5f80fd5b603a6056565b604051604591906099565b60405180910390f35b60546066565b005b5f8054906101000a900460ff1681565b60015f806101000a81548160ff021916908315150217905550565b5f8115159050919050565b6093816081565b82525050565b5f60208201905060aa5f830184608c565b9291505056fea26469706673582212205035f139f1fe97a5af13c06ceda5c8789a427740c0533636f81c79307019759464736f6c63430008170033"  # noqa: E501
PAYABLE_TESTER_CONTRACT_ABI = [
    {
        "inputs": [],
        "name": "doNoValueCall",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "wasCalled",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "view",
        "type": "function",
    },
]
PAYABLE_TESTER_CONTRACT_DATA = {
    "bytecode": PAYABLE_TESTER_CONTRACT_BYTECODE,
    "bytecode_runtime": PAYABLE_TESTER_CONTRACT_RUNTIME,
    "abi": PAYABLE_TESTER_CONTRACT_ABI,
}
