"""
Generated by `compile_contracts.py` script.
Compiled with Solidity v0.8.23.
"""

# source: web3/_utils/contract_sources/StringContract.sol:StringContract
STRING_CONTRACT_BYTECODE = "0x608060405234801562000010575f80fd5b5060405162000cb638038062000cb68339818101604052810190620000369190620001d3565b805f908162000046919062000459565b50506200053d565b5f604051905090565b5f80fd5b5f80fd5b5f80fd5b5f80fd5b5f601f19601f8301169050919050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52604160045260245ffd5b620000af8262000067565b810181811067ffffffffffffffff82111715620000d157620000d062000077565b5b80604052505050565b5f620000e56200004e565b9050620000f38282620000a4565b919050565b5f67ffffffffffffffff82111562000115576200011462000077565b5b620001208262000067565b9050602081019050919050565b5f5b838110156200014c5780820151818401526020810190506200012f565b5f8484015250505050565b5f6200016d6200016784620000f8565b620000da565b9050828152602081018484840111156200018c576200018b62000063565b5b620001998482856200012d565b509392505050565b5f82601f830112620001b857620001b76200005f565b5b8151620001ca84826020860162000157565b91505092915050565b5f60208284031215620001eb57620001ea62000057565b5b5f82015167ffffffffffffffff8111156200020b576200020a6200005b565b5b6200021984828501620001a1565b91505092915050565b5f81519050919050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52602260045260245ffd5b5f60028204905060018216806200027157607f821691505b6020821081036200028757620002866200022c565b5b50919050565b5f819050815f5260205f209050919050565b5f6020601f8301049050919050565b5f82821b905092915050565b5f60088302620002eb7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff82620002ae565b620002f78683620002ae565b95508019841693508086168417925050509392505050565b5f819050919050565b5f819050919050565b5f620003416200033b62000335846200030f565b62000318565b6200030f565b9050919050565b5f819050919050565b6200035c8362000321565b620003746200036b8262000348565b848454620002ba565b825550505050565b5f90565b6200038a6200037c565b6200039781848462000351565b505050565b5b81811015620003be57620003b25f8262000380565b6001810190506200039d565b5050565b601f8211156200040d57620003d7816200028d565b620003e2846200029f565b81016020851015620003f2578190505b6200040a62000401856200029f565b8301826200039c565b50505b505050565b5f82821c905092915050565b5f6200042f5f198460080262000412565b1980831691505092915050565b5f6200044983836200041e565b9150826002028217905092915050565b620004648262000222565b67ffffffffffffffff81111562000480576200047f62000077565b5b6200048c825462000259565b62000499828285620003c2565b5f60209050601f831160018114620004cf575f8415620004ba578287015190505b620004c685826200043c565b86555062000535565b601f198416620004df866200028d565b5f5b828110156200050857848901518255600182019150602085019450602081019050620004e1565b8683101562000528578489015162000524601f8916826200041e565b8355505b6001600288020188555050505b505050505050565b61076b806200054b5f395ff3fe608060405260043610610037575f3560e01c806320965255146100995780633fa4f245146100b757806393a09352146100e157610038565b5b348015610043575f80fd5b505f36606082828080601f0160208091040260200160405190810160405280939291908181526020018383808284375f81840152601f19601f820116905080830192505050505050509050915050805190602001f35b6100a1610109565b6040516100ae91906102bf565b60405180910390f35b3480156100c2575f80fd5b506100cb610198565b6040516100d891906102bf565b60405180910390f35b3480156100ec575f80fd5b506101076004803603810190610102919061041c565b610223565b005b60605f805461011790610490565b80601f016020809104026020016040519081016040528092919081815260200182805461014390610490565b801561018e5780601f106101655761010080835404028352916020019161018e565b820191905f5260205f20905b81548152906001019060200180831161017157829003601f168201915b5050505050905090565b5f80546101a490610490565b80601f01602080910402602001604051908101604052809291908181526020018280546101d090610490565b801561021b5780601f106101f25761010080835404028352916020019161021b565b820191905f5260205f20905b8154815290600101906020018083116101fe57829003601f168201915b505050505081565b805f90816102319190610666565b5050565b5f81519050919050565b5f82825260208201905092915050565b5f5b8381101561026c578082015181840152602081019050610251565b5f8484015250505050565b5f601f19601f8301169050919050565b5f61029182610235565b61029b818561023f565b93506102ab81856020860161024f565b6102b481610277565b840191505092915050565b5f6020820190508181035f8301526102d78184610287565b905092915050565b5f604051905090565b5f80fd5b5f80fd5b5f80fd5b5f80fd5b7f4e487b71000000000000000000000000000000000000000000000000000000005f52604160045260245ffd5b61032e82610277565b810181811067ffffffffffffffff8211171561034d5761034c6102f8565b5b80604052505050565b5f61035f6102df565b905061036b8282610325565b919050565b5f67ffffffffffffffff82111561038a576103896102f8565b5b61039382610277565b9050602081019050919050565b828183375f83830152505050565b5f6103c06103bb84610370565b610356565b9050828152602081018484840111156103dc576103db6102f4565b5b6103e78482856103a0565b509392505050565b5f82601f830112610403576104026102f0565b5b81356104138482602086016103ae565b91505092915050565b5f60208284031215610431576104306102e8565b5b5f82013567ffffffffffffffff81111561044e5761044d6102ec565b5b61045a848285016103ef565b91505092915050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52602260045260245ffd5b5f60028204905060018216806104a757607f821691505b6020821081036104ba576104b9610463565b5b50919050565b5f819050815f5260205f209050919050565b5f6020601f8301049050919050565b5f82821b905092915050565b5f6008830261051c7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff826104e1565b61052686836104e1565b95508019841693508086168417925050509392505050565b5f819050919050565b5f819050919050565b5f61056a6105656105608461053e565b610547565b61053e565b9050919050565b5f819050919050565b61058383610550565b61059761058f82610571565b8484546104ed565b825550505050565b5f90565b6105ab61059f565b6105b681848461057a565b505050565b5b818110156105d9576105ce5f826105a3565b6001810190506105bc565b5050565b601f82111561061e576105ef816104c0565b6105f8846104d2565b81016020851015610607578190505b61061b610613856104d2565b8301826105bb565b50505b505050565b5f82821c905092915050565b5f61063e5f1984600802610623565b1980831691505092915050565b5f610656838361062f565b9150826002028217905092915050565b61066f82610235565b67ffffffffffffffff811115610688576106876102f8565b5b6106928254610490565b61069d8282856105dd565b5f60209050601f8311600181146106ce575f84156106bc578287015190505b6106c6858261064b565b86555061072d565b601f1984166106dc866104c0565b5f5b82811015610703578489015182556001820191506020850194506020810190506106de565b86831015610720578489015161071c601f89168261062f565b8355505b6001600288020188555050505b50505050505056fea2646970667358221220d29fb758562d4c585b5c8e0bf5af7b6ab9ee2368e8edb2bf61b0eee3e98e866e64736f6c63430008170033"  # noqa: E501
STRING_CONTRACT_RUNTIME = "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"  # noqa: E501
STRING_CONTRACT_ABI = [
    {
        "inputs": [{"internalType": "string", "name": "_value", "type": "string"}],
        "stateMutability": "nonpayable",
        "type": "constructor",
    },
    {"stateMutability": "nonpayable", "type": "fallback"},
    {
        "inputs": [],
        "name": "getValue",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "payable",
        "type": "function",
    },
    {
        "inputs": [{"internalType": "string", "name": "_value", "type": "string"}],
        "name": "setValue",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function",
    },
    {
        "inputs": [],
        "name": "value",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function",
    },
]
STRING_CONTRACT_DATA = {
    "bytecode": STRING_CONTRACT_BYTECODE,
    "bytecode_runtime": STRING_CONTRACT_RUNTIME,
    "abi": STRING_CONTRACT_ABI,
}
