Metadata-Version: 2.1
Name: eth-account
Version: 0.10.0
Summary: eth-account: Sign Ethereum transactions and messages with local private keys
Home-page: https://github.com/ethereum/eth-account
Author: The Ethereum Foundation
Author-email: snakecharm<PERSON>@ethereum.org
License: MIT
Keywords: ethereum
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.7, <4
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: bitarray >=2.4.0
Requires-Dist: eth-abi >=4.0.0-b.2
Requires-Dist: eth-keyfile >=0.6.0
Requires-Dist: eth-keys >=0.4.0
Requires-Dist: eth-rlp >=0.3.0
Requires-Dist: eth-utils >=2.0.0
Requires-Dist: hexbytes <0.4.0,>=0.1.0
Requires-Dist: rlp >=1.0.0
Provides-Extra: dev
Requires-Dist: bumpversion >=0.5.3 ; extra == 'dev'
Requires-Dist: pytest-watch >=4.1.0 ; extra == 'dev'
Requires-Dist: tox >=4.0.0 ; extra == 'dev'
Requires-Dist: build >=0.9.0 ; extra == 'dev'
Requires-Dist: wheel ; extra == 'dev'
Requires-Dist: twine ; extra == 'dev'
Requires-Dist: ipython ; extra == 'dev'
Requires-Dist: pytest >=7.0.0 ; extra == 'dev'
Requires-Dist: pytest-xdist >=2.4.0 ; extra == 'dev'
Requires-Dist: hypothesis <5,>=4.18.0 ; extra == 'dev'
Requires-Dist: coverage ; extra == 'dev'
Requires-Dist: flake8 ==6.0.0 ; extra == 'dev'
Requires-Dist: flake8-bugbear ==23.3.23 ; extra == 'dev'
Requires-Dist: isort >=5.10.1 ; extra == 'dev'
Requires-Dist: mypy ==0.971 ; extra == 'dev'
Requires-Dist: pydocstyle >=6.0.0 ; extra == 'dev'
Requires-Dist: black >=23 ; extra == 'dev'
Requires-Dist: sphinx >=5.0.0 ; extra == 'dev'
Requires-Dist: sphinx-rtd-theme >=1.0.0 ; extra == 'dev'
Requires-Dist: towncrier <22,>=21 ; extra == 'dev'
Provides-Extra: doc
Requires-Dist: sphinx >=5.0.0 ; extra == 'doc'
Requires-Dist: sphinx-rtd-theme >=1.0.0 ; extra == 'doc'
Requires-Dist: towncrier <22,>=21 ; extra == 'doc'
Provides-Extra: lint
Requires-Dist: flake8 ==6.0.0 ; extra == 'lint'
Requires-Dist: flake8-bugbear ==23.3.23 ; extra == 'lint'
Requires-Dist: isort >=5.10.1 ; extra == 'lint'
Requires-Dist: mypy ==0.971 ; extra == 'lint'
Requires-Dist: pydocstyle >=6.0.0 ; extra == 'lint'
Requires-Dist: black >=23 ; extra == 'lint'
Provides-Extra: test
Requires-Dist: pytest >=7.0.0 ; extra == 'test'
Requires-Dist: pytest-xdist >=2.4.0 ; extra == 'test'
Requires-Dist: hypothesis <5,>=4.18.0 ; extra == 'test'
Requires-Dist: coverage ; extra == 'test'

# eth-account

[![Join the conversation on Discord](https://img.shields.io/discord/809793915578089484?color=blue&label=chat&logo=discord&logoColor=white)](https://discord.gg/GHryRvPB84)
[![Build Status](https://circleci.com/gh/ethereum/eth-account.svg?style=shield)](https://circleci.com/gh/ethereum/eth-account)
[![PyPI version](https://badge.fury.io/py/eth-account.svg)](https://badge.fury.io/py/eth-account)
[![Python versions](https://img.shields.io/pypi/pyversions/eth-account.svg)](https://pypi.python.org/pypi/eth-account)
[![Docs build](https://readthedocs.org/projects/eth-account/badge/?version=latest)](https://eth-account.readthedocs.io/en/latest/?badge=latest)


Sign Ethereum transactions and messages with local private keys

Read more in the [documentation on ReadTheDocs](https://eth-account.readthedocs.io/). [View the change log](https://eth-account.readthedocs.io/en/latest/release_notes.html).

## Quickstart

```sh
python -m pip install eth-account
```

## Developer Setup

If you would like to hack on eth-account, please check out the [Snake Charmers
Tactical Manual](https://github.com/ethereum/snake-charmers-tactical-manual)
for information on how we do:

-   Testing
-   Pull Requests
-   Code Style
-   Documentation

### Development Environment Setup

You can set up your dev environment with:

```sh
<NAME_EMAIL>:ethereum/eth-account.git
cd eth-account
virtualenv -p python3 venv
. venv/bin/activate
python -m pip install -e ".[dev]"
```

To run the integration test cases, you need to install node and the custom cli tool as follows:

```sh
apt-get install -y nodejs  # As sudo
./tests/integration/js-scripts/setup_node_v20.sh  # As sudo
cd tests/integration/js-scripts
npm install -g .  # As sudo
```

### Release setup

To release a new version:

```sh
make release bump=$$VERSION_PART_TO_BUMP$$
```

#### How to bumpversion

The version format for this repo is `{major}.{minor}.{patch}` for stable, and
`{major}.{minor}.{patch}-{stage}.{devnum}` for unstable (`stage` can be alpha or beta).

To issue the next version in line, specify which part to bump,
like `make release bump=minor` or `make release bump=devnum`. This is typically done from the
master branch, except when releasing a beta (in which case the beta is released from master,
and the previous stable branch is released from said branch).

If you are in a beta version, `make release bump=stage` will switch to a stable.

To issue an unstable version when the current version is stable, specify the
new version explicitly, like `make release bump="--new-version 4.0.0-alpha.1 devnum"`
