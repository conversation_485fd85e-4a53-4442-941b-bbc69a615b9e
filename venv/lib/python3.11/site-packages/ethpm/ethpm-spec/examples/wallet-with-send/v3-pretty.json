{"manifest": "ethpm/3", "version": "1.0.0", "name": "wallet-with-send", "sources": {"WalletWithSend.sol": {"type": "solidity", "installPath": "./WalletWithSend.sol", "urls": ["ipfs://QmQmKim8MpVGgkaJzHCAWkAJnr3hPLrUXmaz5Ao1gWjxNY"]}}, "compilers": [{"contractTypes": ["WalletWithSend"], "name": "solc", "version": "0.6.8+commit.0bbfe453", "settings": {"optimize": false}}], "contractTypes": {"WalletWithSend": {"sourceId": "WalletWithSend.sol", "deploymentBytecode": {"bytecode": "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", "linkReferences": [{"offsets": [768, 1117], "length": 20, "name": "wallet:safe-math-lib:SafeMathLib"}]}, "runtimeBytecode": {"bytecode": "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", "linkReferences": [{"offsets": [672, 1021], "length": 20, "name": "wallet:safe-math-lib:SafeMathLib"}]}, "abi": [{"stateMutability": "nonpayable", "type": "fallback"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "address payable", "name": "to", "type": "address"}], "name": "approvedSend", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "send", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "withdraw", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "devdoc": {"author": "<PERSON>am <<EMAIL>>", "methods": {"approve(address,uint256)": {"details": "Sets recipient to be approved to withdraw the specified amount"}, "send(address,uint256)": {"details": "Sends the recipient the specified amount"}, "approvedSend(uint256,address)": {"details": "Sends funds that have been approved to the specified address"}, "withdraw(uint256)": {"details": "Lets caller withdraw up to their approved amount"}}, "title": "Wallet contract with simple send and approval spending functionality"}, "userdoc": {"methods": {"approve(address,uint256)": {"notice": "This will set the recipient to be approved to withdraw the specified amount."}, "send(address,uint256)": {"notice": "This will send the reciepient the specified amount."}, "approvedSend(uint256,address)": {"notice": "This will send the reciepient the specified amount."}, "withdraw(uint256)": {"notice": "This will withdraw provided value, deducting it from your total allowance."}}}}}, "deployments": {"blockchain://41941023680923e0fe4d74a34bdac8141f2540e3ae90623718e47d66d1ca4a2d/block/b6d0d43f61e5e36d20eb3d5caca12220b024ed2861a814795d1fd6596fe041bf": {"Wallet": {"contractType": "WalletWithSend", "address": "******************************************", "transaction": "0xc1bdfc5eeb517aabb3b65cb24f8d0cd3fd4b8d71f94f90f3e76250cbb96b5369", "block": "0x48ed49ff47ecf433d7c8f1847ddae8c1250ad948fda22342f78f732823534780", "runtimeBytecode": {"linkDependencies": [{"offsets": [672, 1021], "type": "reference", "value": "wallet:safe-math-lib:SafeMathLib"}]}}}}, "buildDependencies": {"wallet": "ipfs://QmRALeFkttSr6DLmPiNtAqLcMJYXu4BK3SjZGVgW8VASnm"}}