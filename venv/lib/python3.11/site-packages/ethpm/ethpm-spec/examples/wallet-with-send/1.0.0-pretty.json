{"manifest_version": "2", "version": "1.0.0", "package_name": "wallet-with-send", "sources": {"./contracts/WalletWithSend.sol": "ipfs://QmRvVRn6USxJu1H4L51KyzxbQFVmCFqdzB4RAG9VeJ2s4W"}, "contract_types": {"WalletWithSend": {"deployment_bytecode": {"bytecode": "0x608060405260008054600160a060020a03191633179055610395806100256000396000f3006080604052600436106100615763ffffffff7c0100000000000000000000000000000000000000000000000000000000600035041663095ea7b381146100705780632e1a7d4d146100a8578063c577ff8b146100c0578063d0679d34146100e6575b34801561006d57600080fd5b50005b34801561007c57600080fd5b50610094600160a060020a036004351660243561010a565b604080519115158252519081900360200190f35b3480156100b457600080fd5b50610094600435610147565b3480156100cc57600080fd5b506100e4600435600160a060020a0360243516610233565b005b3480156100f257600080fd5b50610094600160a060020a0360043516602435610326565b60008054600160a060020a0316331461012257600080fd5b50600160a060020a038216600090815260016020819052604090912082905592915050565b3360009081526001602090815260408083205481517fa293d1e800000000000000000000000000000000000000000000000000000000815260048101919091526024810185905290517300000000000000000000000000000000000000009263a293d1e89260448082019391829003018186803b1580156101c757600080fd5b505af41580156101db573d6000803e3d6000fd5b505050506040513d60208110156101f157600080fd5b505133600081815260016020526040808220939093559151909184156108fc02918591818181858888f19350505050151561022b57600080fd5b506001919050565b336000908152600160209081526040918290205482517fa293d1e800000000000000000000000000000000000000000000000000000000815260048101919091526024810185905291517300000000000000000000000000000000000000009263a293d1e8926044808301939192829003018186803b1580156102b557600080fd5b505af41580156102c9573d6000803e3d6000fd5b505050506040513d60208110156102df57600080fd5b505133600090815260016020526040808220929092559051600160a060020a0383169184156108fc02918591818181858888f19350505050151561032257600080fd5b5050565b60008054600160a060020a0316331461033e57600080fd5b604051600160a060020a0384169083156108fc029084906000818181858888f19796505050505050505600a165627a7a72305820127a404b857b83aa26a51d55d8283bff1dfb5c18217a999c5fbd2862d240fb760029", "link_references": [{"offsets": [439, 676], "length": 20, "name": "SafeSendLib"}]}, "runtime_bytecode": {"bytecode": "0x6080604052600436106100615763ffffffff7c0100000000000000000000000000000000000000000000000000000000600035041663095ea7b381146100705780632e1a7d4d146100a8578063c577ff8b146100c0578063d0679d34146100e6575b34801561006d57600080fd5b50005b34801561007c57600080fd5b50610094600160a060020a036004351660243561010a565b604080519115158252519081900360200190f35b3480156100b457600080fd5b50610094600435610147565b3480156100cc57600080fd5b506100e4600435600160a060020a0360243516610233565b005b3480156100f257600080fd5b50610094600160a060020a0360043516602435610326565b60008054600160a060020a0316331461012257600080fd5b50600160a060020a038216600090815260016020819052604090912082905592915050565b3360009081526001602090815260408083205481517fa293d1e800000000000000000000000000000000000000000000000000000000815260048101919091526024810185905290517300000000000000000000000000000000000000009263a293d1e89260448082019391829003018186803b1580156101c757600080fd5b505af41580156101db573d6000803e3d6000fd5b505050506040513d60208110156101f157600080fd5b505133600081815260016020526040808220939093559151909184156108fc02918591818181858888f19350505050151561022b57600080fd5b506001919050565b336000908152600160209081526040918290205482517fa293d1e800000000000000000000000000000000000000000000000000000000815260048101919091526024810185905291517300000000000000000000000000000000000000009263a293d1e8926044808301939192829003018186803b1580156102b557600080fd5b505af41580156102c9573d6000803e3d6000fd5b505050506040513d60208110156102df57600080fd5b505133600090815260016020526040808220929092559051600160a060020a0383169184156108fc02918591818181858888f19350505050151561032257600080fd5b5050565b60008054600160a060020a0316331461033e57600080fd5b604051600160a060020a0384169083156108fc029084906000818181858888f19796505050505050505600a165627a7a72305820127a404b857b83aa26a51d55d8283bff1dfb5c18217a999c5fbd2862d240fb760029", "link_references": [{"offsets": [402, 639], "length": 20, "name": "SafeSendLib"}]}, "abi": [{"constant": false, "inputs": [{"name": "recipient", "type": "address"}, {"name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "value", "type": "uint256"}], "name": "withdraw", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "value", "type": "uint256"}, {"name": "to", "type": "address"}], "name": "approvedSend", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "recipient", "type": "address"}, {"name": "value", "type": "uint256"}], "name": "send", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"payable": false, "stateMutability": "nonpayable", "type": "fallback"}], "compiler": {"name": "solc", "version": "0.4.24+commit.e67f0147.Emscripten.clang", "settings": {"optimize": true}}, "natspec": {"author": "<PERSON>am <<EMAIL>>", "methods": {"approve(address,uint256)": {"details": "Sets recipient to be approved to withdraw the specified amount", "notice": "This will set the recipient to be approved to withdraw the specified amount."}, "send(address,uint256)": {"details": "Sends the recipient the specified amount", "notice": "This will send the reciepient the specified amount."}, "approvedSend(uint256,address)": {"details": "Sends funds that have been approved to the specified address", "notice": "This will send the reciepient the specified amount."}, "withdraw(uint256)": {"details": "Lets caller withdraw up to their approved amount", "notice": "This will withdraw provided value, deducting it from your total allowance."}}, "title": "Wallet contract with simple send and approval spending functionality"}}}, "deployments": {"blockchain://41941023680923e0fe4d74a34bdac8141f2540e3ae90623718e47d66d1ca4a2d/block/32a617527c183587710e28a6b66eb709a34e3cbc5ae5e3f1ed564ba1b46ab00c": {"Wallet": {"contract_type": "WalletWithSend", "address": "******************************************", "transaction": "0xb44bb94b553c8c63c6a2f0b3b5bea92c69621e65af56069254b36f6b5272e7d0", "block": "0xb44bb94b553c8c63c6a2f0b3b5bea92c69621e65af56069254b36f6b5272e7d0", "runtime_bytecode": {"link_dependencies": [{"offsets": [402, 639], "type": "reference", "value": "wallet:safe-math-lib:SafeMathLib"}]}}}}, "build_dependencies": {"wallet": "ipfs://QmPZ98R6wnyhiHAfE3D9eGnZDvUCBnhi2Vp5Wkdtax6cSn"}}