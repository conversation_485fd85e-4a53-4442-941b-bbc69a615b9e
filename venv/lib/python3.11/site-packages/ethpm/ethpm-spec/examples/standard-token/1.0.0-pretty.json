{"manifest_version": "2", "version": "1.0.0", "package_name": "standard-token", "sources": {"./contracts/AbstractToken.sol": "ipfs://Qma8tiYLSYGSjbLVn4RbTHBt2LBUXe2ADb37NVsG5UgXea", "./contracts/StandardToken.sol": "ipfs://QmRJHLmPVct2rbBpdGjP3xkXbF7romQigtmcs8TRfV1yC7"}, "contract_types": {"StandardToken": {"abi": [{"constant": false, "inputs": [{"name": "_spender", "type": "address"}, {"name": "_value", "type": "uint256"}], "name": "approve", "outputs": [{"name": "success", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "totalSupply", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "_from", "type": "address"}, {"name": "_to", "type": "address"}, {"name": "_value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"name": "success", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "_owner", "type": "address"}], "name": "balanceOf", "outputs": [{"name": "balance", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "_to", "type": "address"}, {"name": "_value", "type": "uint256"}], "name": "transfer", "outputs": [{"name": "success", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "_owner", "type": "address"}, {"name": "_spender", "type": "address"}], "name": "allowance", "outputs": [{"name": "remaining", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"inputs": [{"name": "_totalSupply", "type": "uint256"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "from", "type": "address"}, {"indexed": true, "name": "to", "type": "address"}, {"indexed": false, "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "owner", "type": "address"}, {"indexed": true, "name": "spender", "type": "address"}, {"indexed": false, "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}], "natspec": {"author": "<PERSON> - <<EMAIL>>", "methods": {"allowance(address,address)": {"details": "Returns number of allowed tokens for given address.", "params": {"_owner": "Address of token owner.", "_spender": "Address of token spender."}}, "approve(address,uint256)": {"details": "Sets approved amount of tokens for spender. Returns success.", "params": {"_spender": "Address of allowed account.", "_value": "Number of approved tokens."}}, "balanceOf(address)": {"details": "Returns number of tokens owned by given address.", "params": {"_owner": "Address of token owner."}}, "transfer(address,uint256)": {"details": "Transfers sender's tokens to a given address. Returns success.", "params": {"_to": "Address of token receiver.", "_value": "Number of tokens to transfer."}}, "transferFrom(address,address,uint256)": {"details": "Allows allowed third party to transfer tokens from one address to another. Returns success.", "params": {"_from": "Address from where tokens are withdrawn.", "_to": "Address to where tokens are sent.", "_value": "Number of tokens to transfer."}}}, "title": "Standard token contract"}}}}