{"manifest_version": "2", "version": "1.0.0", "package_name": "escrow", "sources": {"./contracts/SafeSendLib.sol": "ipfs://QmXsTBDZvtGBsJHg1HKinz1p6QvhphLV8UPX6Jqo3LcKW3", "./contracts/Escrow.sol": "ipfs://Qmbm91zWRqwjuRTSbuyVNUAV7umu5o594MzBMxWbEMRQPj"}, "contract_types": {"SafeSendLib": {"deployment_bytecode": {"bytecode": "0x61011c610030600b82828239805160001a6073146000811461002057610022565bfe5b5030600052607381538281f300730000000000000000000000000000000000000000301460806040526004361060555763ffffffff7c01000000000000000000000000000000000000000000000000000000006000350416639341231c8114605a575b600080fd5b818015606557600080fd5b50608873ffffffffffffffffffffffffffffffffffffffff60043516602435609c565b604080519115158252519081900360200190f35b6000303182111560ab57600080fd5b60405173ffffffffffffffffffffffffffffffffffffffff84169083156108fc029084906000818181858888f19350505050151560e757600080fd5b506001929150505600a165627a7a723058200300c406f268c8026cb55ced0cb3c2ee571e2225be95d6c43816ce75e303dd6f0029"}, "runtime_bytecode": {"bytecode": "0x734f5b11c860b37b68de6d14fb7e7b5f18a9a1bdc0301460806040526004361060555763ffffffff7c01000000000000000000000000000000000000000000000000000000006000350416639341231c8114605a575b600080fd5b818015606557600080fd5b50608873ffffffffffffffffffffffffffffffffffffffff60043516602435609c565b604080519115158252519081900360200190f35b6000303182111560ab57600080fd5b60405173ffffffffffffffffffffffffffffffffffffffff84169083156108fc029084906000818181858888f19350505050151560e757600080fd5b506001929150505600a165627a7a723058200300c406f268c8026cb55ced0cb3c2ee571e2225be95d6c43816ce75e303dd6f0029"}, "abi": [{"constant": false, "inputs": [{"name": "recipient", "type": "address"}, {"name": "value", "type": "uint256"}], "name": "sendOrThrow", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}], "compiler": {"name": "solc", "version": "0.4.24+commit.e67f0147.Emscripten.clang", "settings": {"optimize": true}}, "natspec": {"author": "<PERSON>am <<EMAIL>>", "methods": {"sendOrThrow(address,uint256)": {"details": "Attempts to send the specified amount to the recipient throwing an error if it fails", "params": {"recipient": "The address that the funds should be to.", "value": "The amount in wei that should be sent."}}}, "title": "Library for safe sending of ether."}}, "Escrow": {"runtime_bytecode": {"bytecode": "0x6080604052600436106100565763ffffffff7c010000000000000000000000000000000000000000000000000000000060003504166366d003ac811461005b57806367e404ce1461008c57806369d89575146100a1575b600080fd5b34801561006757600080fd5b506100706100b8565b60408051600160a060020a039092168252519081900360200190f35b34801561009857600080fd5b506100706100c7565b3480156100ad57600080fd5b506100b66100d6565b005b600154600160a060020a031681565b600054600160a060020a031681565b600054600160a060020a031633141561019857600154604080517f9341231c000000000000000000000000000000000000000000000000000000008152600160a060020a039092166004830152303160248301525173000000000000000000000000000000000000000091639341231c916044808301926020929190829003018186803b15801561016657600080fd5b505af415801561017a573d6000803e3d6000fd5b505050506040513d602081101561019057600080fd5b506102289050565b600154600160a060020a031633141561005657600054604080517f9341231c000000000000000000000000000000000000000000000000000000008152600160a060020a039092166004830152303160248301525173000000000000000000000000000000000000000091639341231c916044808301926020929190829003018186803b15801561016657600080fd5b5600a165627a7a723058201766d3411ff91d047cf900369478c682a497a6e560cd1b2fe4d9f2d6fe13b4210029", "link_references": [{"offsets": [301, 495], "length": 20, "name": "SafeSendLib"}]}, "abi": [{"constant": true, "inputs": [], "name": "recipient", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "sender", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [], "name": "releaseFunds", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"name": "_recipient", "type": "address"}], "payable": true, "stateMutability": "payable", "type": "constructor"}], "compiler": {"name": "solc", "version": "0.4.24+commit.e67f0147.Emscripten.clang", "settings": {"optimize": true}}, "natspec": {"author": "<PERSON>am <<EMAIL>>", "methods": {"releaseFunds()": {"details": "Releases the escrowed funds to the other party.", "notice": "This will release the escrowed funds to the other party."}}, "title": "Contract for holding funds in escrow between two semi trusted parties."}}}, "deployments": {"blockchain://41941023680923e0fe4d74a34bdac8141f2540e3ae90623718e47d66d1ca4a2d/block/d2e1b78094a358550ae340c47a00aee43a5444fb44235fdb73e7e07ff5faeadb": {"SafeSendLib": {"contract_type": "SafeSendLib", "address": "******************************************", "transaction": "0x208492026eff5fc838f23d12e930239dcfae9cde9cc96b87b38bb50bd1465b81", "block": "0x29e9393113b96ff1e9048710355d213dafc9896fd2b97ca74e3f3e6aaf4092c6"}, "Escrow": {"contract_type": "Escrow", "address": "******************************************", "transaction": "0xc60e32c63abf34579390ef65d83cc5eb52225de38c3eeca2e5afa961d71c16d0", "block": "0x4d1a618802bb87752d95db453dddeea622820424a2f836bedf8769a67ee276b8", "runtime_bytecode": {"link_dependencies": [{"offsets": [301, 495], "type": "reference", "value": "SafeSendLib"}]}}}}}