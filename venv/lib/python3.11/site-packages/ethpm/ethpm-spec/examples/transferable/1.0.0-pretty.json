{"manifest_version": "2", "version": "1.0.0", "package_name": "transferable", "meta": {"license": "MIT", "authors": ["<PERSON>am <<EMAIL>>"], "description": "Reusable contracts which implement a privileged 'owner' model for authorization with functionality for transfering ownership.", "keywords": ["authorization"]}, "sources": {"./contracts/Transferable.sol": "ipfs://QmZYkdUUTwREjfy4vQc3enzu6WKk8eNyvGERqy1cNNVkAD"}, "build_dependencies": {"owned": "ipfs://QmbeVyFLSuEUxiXKwSsEjef6icpdTdA4kGG9BcrJXKNKUW"}}