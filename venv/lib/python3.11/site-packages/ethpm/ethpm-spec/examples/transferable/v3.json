{"buildDependencies": {"owned": "ipfs://QmcxvhkJJVpbxEAa6cgW3B6XwPJb79w9GpNUv2P2THUzZR"}, "manifest": "ethpm/3", "meta": {"authors": ["<PERSON>am <<EMAIL>>"], "description": "Reusable contracts which implement a privileged 'owner' model for authorization with functionality for transfering ownership.", "keywords": ["authorization"], "license": "MIT"}, "name": "transferable", "sources": {"Transferable.sol": {"installPath": "./Transferable.sol", "type": "solidity", "urls": ["ipfs://QmdWB74Ca8tyXtS3UxzJqcvETv3LLkacX2ywfJfNNWVnYt"]}}, "version": "1.0.0"}