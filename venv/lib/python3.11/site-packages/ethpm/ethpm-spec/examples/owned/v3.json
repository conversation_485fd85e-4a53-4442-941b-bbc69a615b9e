{"manifest": "ethpm/3", "meta": {"authors": ["<PERSON>am <<EMAIL>>"], "description": "Reusable contracts which implement a privileged 'owner' model for authorization.", "keywords": ["authorization"], "license": "MIT", "links": {"documentation": "ipfs://QmUYcVzTfSwJoigggMxeo2g5STWAgJdisQsqcXHws7b1FW"}}, "name": "owned", "sources": {"Owned.sol": {"installPath": "./Owned.sol", "type": "solidity", "urls": ["ipfs://QmU8QUSt56ZoBDJgjjXvAZEPro9LmK1m2gjVG5Q4s9x29W"]}}, "version": "1.0.0"}