{"manifest_version": "2", "meta": {"authors": ["<PERSON>am <<EMAIL>>"], "description": "Reusable contracts which implement a privileged 'owner' model for authorization.", "keywords": ["authorization"], "license": "MIT", "links": {"documentation": "ipfs://QmUYcVzTfSwJoigggMxeo2g5STWAgJdisQsqcXHws7b1FW"}}, "package_name": "owned", "sources": {"./contracts/Owned.sol": "ipfs://Qme4otpS88NV8yQi8TfTP89EsQC5bko3F5N1yhRoi6cwGV"}, "version": "1.0.0"}