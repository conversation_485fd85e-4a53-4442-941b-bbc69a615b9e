{"contractTypes": {"ENS": {"abi": [{"constant": true, "inputs": [{"name": "node", "type": "bytes32"}], "name": "resolver", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "node", "type": "bytes32"}], "name": "owner", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "label", "type": "bytes32"}, {"name": "owner", "type": "address"}], "name": "setSubnodeOwner", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "ttl", "type": "uint64"}], "name": "setTTL", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "node", "type": "bytes32"}], "name": "ttl", "outputs": [{"name": "", "type": "uint64"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "resolver", "type": "address"}], "name": "setResolver", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "owner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": true, "name": "label", "type": "bytes32"}, {"indexed": false, "name": "owner", "type": "address"}], "name": "New<PERSON>wn<PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": false, "name": "owner", "type": "address"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": false, "name": "resolver", "type": "address"}], "name": "NewResolver", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": false, "name": "ttl", "type": "uint64"}], "name": "NewTTL", "type": "event"}], "deploymentbytecode": {"bytecode": "0x"}, "runtime_bytecode": {"bytecode": "0x"}}, "ENSRegistry": {"abi": [{"constant": true, "inputs": [{"name": "node", "type": "bytes32"}], "name": "resolver", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "node", "type": "bytes32"}], "name": "owner", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "label", "type": "bytes32"}, {"name": "owner", "type": "address"}], "name": "setSubnodeOwner", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "ttl", "type": "uint64"}], "name": "setTTL", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "node", "type": "bytes32"}], "name": "ttl", "outputs": [{"name": "", "type": "uint64"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "resolver", "type": "address"}], "name": "setResolver", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "owner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": true, "name": "label", "type": "bytes32"}, {"indexed": false, "name": "owner", "type": "address"}], "name": "New<PERSON>wn<PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": false, "name": "owner", "type": "address"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": false, "name": "resolver", "type": "address"}], "name": "NewResolver", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": false, "name": "ttl", "type": "uint64"}], "name": "NewTTL", "type": "event"}], "deploymentBytecode": {"bytecode": "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"}, "runtime_bytecode": {"bytecode": "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"}}, "FIFSRegistrar": {"abi": [{"constant": false, "inputs": [{"name": "label", "type": "bytes32"}, {"name": "owner", "type": "address"}], "name": "register", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"name": "ensAddr", "type": "address"}, {"name": "node", "type": "bytes32"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}], "deploymentBytecode": {"bytecode": "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"}, "runtime_bytecode": {"bytecode": "0x608060405260043610610041576000357c0100000000000000000000000000000000000000000000000000000000900463ffffffff168063d22057a914610046575b600080fd5b34801561005257600080fd5b506100956004803603810190808035600019169060200190929190803573ffffffffffffffffffffffffffffffffffffffff169060200190929190505050610097565b005b8160008060009054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff166302571be3600154846040516020018083600019166000191681526020018260001916600019168152602001925050506040516020818303038152906040526040518082805190602001908083835b6020831015156101465780518252602082019150602081019050602083039250610121565b6001836020036101000a03801982511681845116808217855250505050505090500191505060405180910390206040518263ffffffff167c0100000000000000000000000000000000000000000000000000000000028152600401808260001916600019168152602001915050602060405180830381600087803b1580156101cd57600080fd5b505af11580156101e1573d6000803e3d6000fd5b505050506040513d60208110156101f757600080fd5b8101908080519060200190929190505050905060008173ffffffffffffffffffffffffffffffffffffffff16148061025a57503373ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff16145b151561026557600080fd5b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff166306ab592360015486866040518463ffffffff167c010000000000000000000000000000000000000000000000000000000002815260040180846000191660001916815260200183600019166000191681526020018273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019350505050600060405180830381600087803b15801561034357600080fd5b505af1158015610357573d6000803e3d6000fd5b50505050505050505600a165627a7a72305820ec5a26df47d3f55c9f8759e24517f00718f8d416777fbb957f5f28957627add60029"}}, "PublicResolver": {"abi": [{"constant": true, "inputs": [{"name": "interfaceID", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "pure", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "key", "type": "string"}, {"name": "value", "type": "string"}], "name": "setText", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "contentTypes", "type": "uint256"}], "name": "ABI", "outputs": [{"name": "contentType", "type": "uint256"}, {"name": "data", "type": "bytes"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "x", "type": "bytes32"}, {"name": "y", "type": "bytes32"}], "name": "set<PERSON><PERSON><PERSON>", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "node", "type": "bytes32"}], "name": "content", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "node", "type": "bytes32"}], "name": "addr", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "key", "type": "string"}], "name": "text", "outputs": [{"name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "contentType", "type": "uint256"}, {"name": "data", "type": "bytes"}], "name": "setABI", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "node", "type": "bytes32"}], "name": "name", "outputs": [{"name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "name", "type": "string"}], "name": "setName", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "hash", "type": "bytes"}], "name": "setMultihash", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "hash", "type": "bytes32"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "node", "type": "bytes32"}], "name": "pubkey", "outputs": [{"name": "x", "type": "bytes32"}, {"name": "y", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "node", "type": "bytes32"}, {"name": "addr", "type": "address"}], "name": "setAddr", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "node", "type": "bytes32"}], "name": "multihash", "outputs": [{"name": "", "type": "bytes"}], "payable": false, "stateMutability": "view", "type": "function"}, {"inputs": [{"name": "ensAddr", "type": "address"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": false, "name": "a", "type": "address"}], "name": "AddrChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": false, "name": "hash", "type": "bytes32"}], "name": "ContentChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": false, "name": "name", "type": "string"}], "name": "NameChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": true, "name": "contentType", "type": "uint256"}], "name": "ABIChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": false, "name": "x", "type": "bytes32"}, {"indexed": false, "name": "y", "type": "bytes32"}], "name": "PubkeyChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": false, "name": "indexedKey", "type": "string"}, {"indexed": false, "name": "key", "type": "string"}], "name": "TextChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "node", "type": "bytes32"}, {"indexed": false, "name": "hash", "type": "bytes"}], "name": "MultihashChanged", "type": "event"}], "deploymentBytecode": {"bytecode": "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"}, "runtime_bytecode": {"bytecode": "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"}}, "TestRegistrar": {"abi": [{"constant": true, "inputs": [], "name": "ens", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "", "type": "bytes32"}], "name": "expiryTimes", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "label", "type": "bytes32"}, {"name": "owner", "type": "address"}], "name": "register", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "rootNode", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"inputs": [{"name": "ensAddr", "type": "address"}, {"name": "node", "type": "bytes32"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}], "deploymentBytecode": {"bytecode": "0x608060405234801561001057600080fd5b506040516040806103d78339810180604052810190808051906020019092919080519060200190929190505050816000806101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff1602179055508060018160001916905550505061033e806100996000396000f300608060405260043610610062576000357c0100000000000000000000000000000000000000000000000000000000900463ffffffff1680633f15457f14610067578063af9f26e4146100be578063d22057a914610103578063faff50a814610154575b600080fd5b34801561007357600080fd5b5061007c610187565b604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390f35b3480156100ca57600080fd5b506100ed60048036038101908080356000191690602001909291905050506101ac565b6040518082815260200191505060405180910390f35b34801561010f57600080fd5b506101526004803603810190808035600019169060200190929190803573ffffffffffffffffffffffffffffffffffffffff1690602001909291905050506101c4565b005b34801561016057600080fd5b5061016961030c565b60405180826000191660001916815260200191505060405180910390f35b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b60026020528060005260406000206000915090505481565b42600260008460001916600019168152602001908152602001600020541015156101ed57600080fd5b6224ea004201600260008460001916600019168152602001908152602001600020819055506000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff166306ab592360015484846040518463ffffffff167c010000000000000000000000000000000000000000000000000000000002815260040180846000191660001916815260200183600019166000191681526020018273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019350505050600060405180830381600087803b1580156102f057600080fd5b505af1158015610304573d6000803e3d6000fd5b505050505050565b600154815600a165627a7a723058202e62cf216496a54f98da0d79120cfce29a7e9f3f024d4be0e82b3abcc704d8a60029"}, "runtime_bytecode": {"bytecode": "0x608060405260043610610062576000357c0100000000000000000000000000000000000000000000000000000000900463ffffffff1680633f15457f14610067578063af9f26e4146100be578063d22057a914610103578063faff50a814610154575b600080fd5b34801561007357600080fd5b5061007c610187565b604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390f35b3480156100ca57600080fd5b506100ed60048036038101908080356000191690602001909291905050506101ac565b6040518082815260200191505060405180910390f35b34801561010f57600080fd5b506101526004803603810190808035600019169060200190929190803573ffffffffffffffffffffffffffffffffffffffff1690602001909291905050506101c4565b005b34801561016057600080fd5b5061016961030c565b60405180826000191660001916815260200191505060405180910390f35b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b60026020528060005260406000206000915090505481565b42600260008460001916600019168152602001908152602001600020541015156101ed57600080fd5b6224ea004201600260008460001916600019168152602001908152602001600020819055506000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff166306ab592360015484846040518463ffffffff167c010000000000000000000000000000000000000000000000000000000002815260040180846000191660001916815260200183600019166000191681526020018273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019350505050600060405180830381600087803b1580156102f057600080fd5b505af1158015610304573d6000803e3d6000fd5b505050505050565b600154815600a165627a7a723058202e62cf216496a54f98da0d79120cfce29a7e9f3f024d4be0e82b3abcc704d8a60029"}}}, "manifest": "ethpm/3", "name": "ens", "sources": {"ENS.sol": {"content": "pragma solidity ^0.4.24;\n\ninterface ENS {\n\n    // Logged when the owner of a node assigns a new owner to a subnode.\n    event NewOwner(bytes32 indexed node, bytes32 indexed label, address owner);\n\n    // Logged when the owner of a node transfers ownership to a new account.\n    event Transfer(bytes32 indexed node, address owner);\n\n    // Logged when the resolver for a node changes.\n    event NewResolver(bytes32 indexed node, address resolver);\n\n    // Logged when the TTL of a node changes\n    event NewTTL(bytes32 indexed node, uint64 ttl);\n\n\n    function setSubnodeOwner(bytes32 node, bytes32 label, address owner) public;\n    function setResolver(bytes32 node, address resolver) public;\n    function setOwner(bytes32 node, address owner) public;\n    function setTTL(bytes32 node, uint64 ttl) public;\n    function owner(bytes32 node) public view returns (address);\n    function resolver(bytes32 node) public view returns (address);\n    function ttl(bytes32 node) public view returns (uint64);\n\n}\n"}, "ENSRegistry.sol": {"content": "pragma solidity ^0.4.24;\n\nimport \"./ENS.sol\";\n\n/**\n * The ENS registry contract.\n */\ncontract ENSRegistry is ENS {\n    struct Record {\n        address owner;\n        address resolver;\n        uint64 ttl;\n    }\n\n    mapping (bytes32 => Record) records;\n\n    // Permits modifications only by the owner of the specified node.\n    modifier only_owner(bytes32 node) {\n        require(records[node].owner == msg.sender);\n        _;\n    }\n\n    /**\n     * @dev Constructs a new ENS registrar.\n     */\n    constructor() public {\n        records[0x0].owner = msg.sender;\n    }\n\n    /**\n     * @dev Transfers ownership of a node to a new address. May only be called by the current owner of the node.\n     * @param node The node to transfer ownership of.\n     * @param owner The address of the new owner.\n     */\n    function setOwner(bytes32 node, address owner) public only_owner(node) {\n        emit Transfer(node, owner);\n        records[node].owner = owner;\n    }\n\n    /**\n     * @dev Transfers ownership of a subnode keccak256(node, label) to a new address. May only be called by the owner of the parent node.\n     * @param node The parent node.\n     * @param label The hash of the label specifying the subnode.\n     * @param owner The address of the new owner.\n     */\n    function setSubnodeOwner(bytes32 node, bytes32 label, address owner) public only_owner(node) {\n        bytes32 subnode = keccak256(abi.encodePacked(node, label));\n        emit NewOwner(node, label, owner);\n        records[subnode].owner = owner;\n    }\n\n    /**\n     * @dev Sets the resolver address for the specified node.\n     * @param node The node to update.\n     * @param resolver The address of the resolver.\n     */\n    function setResolver(bytes32 node, address resolver) public only_owner(node) {\n        emit NewResolver(node, resolver);   \n        records[node].resolver = resolver;\n    }\n\n    /**\n     * @dev Sets the TTL for the specified node.\n     * @param node The node to update.\n     * @param ttl The TTL in seconds.\n     */\n    function setTTL(bytes32 node, uint64 ttl) public only_owner(node) {\n        emit NewTTL(node, ttl);\n        records[node].ttl = ttl;\n    }\n\n    /**\n     * @dev Returns the address that owns the specified node.\n     * @param node The specified node.\n     * @return address of the owner.\n     */\n    function owner(bytes32 node) public view returns (address) {\n        return records[node].owner;\n    }\n\n    /**\n     * @dev Returns the address of the resolver for the specified node.\n     * @param node The specified node.\n     * @return address of the resolver.\n     */\n    function resolver(bytes32 node) public view returns (address) {\n        return records[node].resolver;\n    }\n\n    /**\n     * @dev Returns the TTL of a node, and any records associated with it.\n     * @param node The specified node.\n     * @return ttl of the node.\n     */\n    function ttl(bytes32 node) public view returns (uint64) {\n        return records[node].ttl;\n    }\n\n}\n"}, "FIFSRegistrar.sol": {"content": "pragma solidity ^0.4.24;\n\nimport \"./ENS.sol\";\n\n/**\n * A registrar that allocates subdomains to the first person to claim them.\n */\ncontract FIFSRegistrar {\n    ENS ens;\n    bytes32 rootNode;\n\n    modifier only_owner(bytes32 label) {\n        address currentOwner = ens.owner(keccak256(abi.encodePacked(rootNode, label)));\n        require(currentOwner == 0 || currentOwner == msg.sender);\n        _;\n    }\n\n    /**\n     * Constructor.\n     * @param ensAddr The address of the ENS registry.\n     * @param node The node that this registrar administers.\n     */\n    constructor(ENS ensAddr, bytes32 node) public {\n        ens = ensAddr;\n        rootNode = node;\n    }\n\n    /**\n     * Register a name, or change the owner of an existing registration.\n     * @param label The hash of the label to register.\n     * @param owner The address of the new owner.\n     */\n    function register(bytes32 label, address owner) public only_owner(label) {\n        ens.setSubnodeOwner(rootNode, label, owner);\n    }\n}\n"}, "PublicResolver.sol": {"content": "pragma solidity ^0.4.24;\n\nimport \"./ENS.sol\";\n\n/**\n * A simple resolver anyone can use; only allows the owner of a node to set its\n * address.\n */\ncontract PublicResolver {\n\n    bytes4 constant INTERFACE_META_ID = 0x01ffc9a7;\n    bytes4 constant ADDR_INTERFACE_ID = 0x3b3b57de;\n    bytes4 constant CONTENT_INTERFACE_ID = 0xd8389dc5;\n    bytes4 constant NAME_INTERFACE_ID = 0x691f3431;\n    bytes4 constant ABI_INTERFACE_ID = 0x2203ab56;\n    bytes4 constant PUBKEY_INTERFACE_ID = 0xc8690233;\n    bytes4 constant TEXT_INTERFACE_ID = 0x59d1d43c;\n    bytes4 constant MULTIHASH_INTERFACE_ID = 0xe89401a1;\n\n    event AddrChanged(bytes32 indexed node, address a);\n    event ContentChanged(bytes32 indexed node, bytes32 hash);\n    event NameChanged(bytes32 indexed node, string name);\n    event ABIChanged(bytes32 indexed node, uint256 indexed contentType);\n    event PubkeyChanged(bytes32 indexed node, bytes32 x, bytes32 y);\n    event TextChanged(bytes32 indexed node, string indexedKey, string key);\n    event MultihashChanged(bytes32 indexed node, bytes hash);\n\n    struct PublicKey {\n        bytes32 x;\n        bytes32 y;\n    }\n\n    struct Record {\n        address addr;\n        bytes32 content;\n        string name;\n        PublicKey pubkey;\n        mapping(string=>string) text;\n        mapping(uint256=>bytes) abis;\n        bytes multihash;\n    }\n\n    ENS ens;\n\n    mapping (bytes32 => Record) records;\n\n    modifier only_owner(bytes32 node) {\n        require(ens.owner(node) == msg.sender);\n        _;\n    }\n\n    /**\n     * Constructor.\n     * @param ensAddr The ENS registrar contract.\n     */\n    constructor(ENS ensAddr) public {\n        ens = ensAddr;\n    }\n\n    /**\n     * Sets the address associated with an ENS node.\n     * May only be called by the owner of that node in the ENS registry.\n     * @param node The node to update.\n     * @param addr The address to set.\n     */\n    function setAddr(bytes32 node, address addr) public only_owner(node) {\n        records[node].addr = addr;\n        emit AddrChanged(node, addr);\n    }\n\n    /**\n     * Sets the content hash associated with an ENS node.\n     * May only be called by the owner of that node in the ENS registry.\n     * Note that this resource type is not standardized, and will likely change\n     * in future to a resource type based on multihash.\n     * @param node The node to update.\n     * @param hash The content hash to set\n     */\n    function setContent(bytes32 node, bytes32 hash) public only_owner(node) {\n        records[node].content = hash;\n        emit ContentChanged(node, hash);\n    }\n\n    /**\n     * Sets the multihash associated with an ENS node.\n     * May only be called by the owner of that node in the ENS registry.\n     * @param node The node to update.\n     * @param hash The multihash to set\n     */\n    function setMultihash(bytes32 node, bytes hash) public only_owner(node) {\n        records[node].multihash = hash;\n        emit MultihashChanged(node, hash);\n    }\n    \n    /**\n     * Sets the name associated with an ENS node, for reverse records.\n     * May only be called by the owner of that node in the ENS registry.\n     * @param node The node to update.\n     * @param name The name to set.\n     */\n    function setName(bytes32 node, string name) public only_owner(node) {\n        records[node].name = name;\n        emit NameChanged(node, name);\n    }\n\n    /**\n     * Sets the ABI associated with an ENS node.\n     * Nodes may have one ABI of each content type. To remove an ABI, set it to\n     * the empty string.\n     * @param node The node to update.\n     * @param contentType The content type of the ABI\n     * @param data The ABI data.\n     */\n    function setABI(bytes32 node, uint256 contentType, bytes data) public only_owner(node) {\n        // Content types must be powers of 2\n        require(((contentType - 1) & contentType) == 0);\n        \n        records[node].abis[contentType] = data;\n        emit ABIChanged(node, contentType);\n    }\n    \n    /**\n     * Sets the SECP256k1 public key associated with an ENS node.\n     * @param node The ENS node to query\n     * @param x the X coordinate of the curve point for the public key.\n     * @param y the Y coordinate of the curve point for the public key.\n     */\n    function setPubkey(bytes32 node, bytes32 x, bytes32 y) public only_owner(node) {\n        records[node].pubkey = PublicKey(x, y);\n        emit PubkeyChanged(node, x, y);\n    }\n\n    /**\n     * Sets the text data associated with an ENS node and key.\n     * May only be called by the owner of that node in the ENS registry.\n     * @param node The node to update.\n     * @param key The key to set.\n     * @param value The text data value to set.\n     */\n    function setText(bytes32 node, string key, string value) public only_owner(node) {\n        records[node].text[key] = value;\n        emit TextChanged(node, key, key);\n    }\n\n    /**\n     * Returns the text data associated with an ENS node and key.\n     * @param node The ENS node to query.\n     * @param key The text data key to query.\n     * @return The associated text data.\n     */\n    function text(bytes32 node, string key) public view returns (string) {\n        return records[node].text[key];\n    }\n\n    /**\n     * Returns the SECP256k1 public key associated with an ENS node.\n     * Defined in EIP 619.\n     * @param node The ENS node to query\n     * @return x, y the X and Y coordinates of the curve point for the public key.\n     */\n    function pubkey(bytes32 node) public view returns (bytes32 x, bytes32 y) {\n        return (records[node].pubkey.x, records[node].pubkey.y);\n    }\n\n    /**\n     * Returns the ABI associated with an ENS node.\n     * Defined in EIP205.\n     * @param node The ENS node to query\n     * @param contentTypes A bitwise OR of the ABI formats accepted by the caller.\n     * @return contentType The content type of the return value\n     * @return data The ABI data\n     */\n    function ABI(bytes32 node, uint256 contentTypes) public view returns (uint256 contentType, bytes data) {\n        Record storage record = records[node];\n        for (contentType = 1; contentType <= contentTypes; contentType <<= 1) {\n            if ((contentType & contentTypes) != 0 && record.abis[contentType].length > 0) {\n                data = record.abis[contentType];\n                return;\n            }\n        }\n        contentType = 0;\n    }\n\n    /**\n     * Returns the name associated with an ENS node, for reverse records.\n     * Defined in EIP181.\n     * @param node The ENS node to query.\n     * @return The associated name.\n     */\n    function name(bytes32 node) public view returns (string) {\n        return records[node].name;\n    }\n\n    /**\n     * Returns the content hash associated with an ENS node.\n     * Note that this resource type is not standardized, and will likely change\n     * in future to a resource type based on multihash.\n     * @param node The ENS node to query.\n     * @return The associated content hash.\n     */\n    function content(bytes32 node) public view returns (bytes32) {\n        return records[node].content;\n    }\n\n    /**\n     * Returns the multihash associated with an ENS node.\n     * @param node The ENS node to query.\n     * @return The associated multihash.\n     */\n    function multihash(bytes32 node) public view returns (bytes) {\n        return records[node].multihash;\n    }\n\n    /**\n     * Returns the address associated with an ENS node.\n     * @param node The ENS node to query.\n     * @return The associated address.\n     */\n    function addr(bytes32 node) public view returns (address) {\n        return records[node].addr;\n    }\n\n    /**\n     * Returns true if the resolver implements the interface specified by the provided hash.\n     * @param interfaceID The ID of the interface to check for.\n     * @return True if the contract implements the requested interface.\n     */\n    function supportsInterface(bytes4 interfaceID) public pure returns (bool) {\n        return interfaceID == ADDR_INTERFACE_ID ||\n        interfaceID == CONTENT_INTERFACE_ID ||\n        interfaceID == NAME_INTERFACE_ID ||\n        interfaceID == ABI_INTERFACE_ID ||\n        interfaceID == PUBKEY_INTERFACE_ID ||\n        interfaceID == TEXT_INTERFACE_ID ||\n        interfaceID == MULTIHASH_INTERFACE_ID ||\n        interfaceID == INTERFACE_META_ID;\n    }\n}\n"}, "TestRegistrar.sol": {"content": "pragma solidity ^0.4.24;\n\nimport \"./ENS.sol\";\n\n/**\n * A registrar that allocates subdomains to the first person to claim them, but\n * expires registrations a fixed period after they're initially claimed.\n */\ncontract TestRegistrar {\n    uint constant registrationPeriod = 4 weeks;\n\n    ENS public ens;\n    bytes32 public rootNode;\n    mapping (bytes32 => uint) public expiryTimes;\n\n    /**\n     * Constructor.\n     * @param ensAddr The address of the ENS registry.\n     * @param node The node that this registrar administers.\n     */\n    constructor(ENS ensAddr, bytes32 node) public {\n        ens = ensAddr;\n        rootNode = node;\n    }\n\n    /**\n     * Register a name that's not currently registered\n     * @param label The hash of the label to register.\n     * @param owner The address of the new owner.\n     */\n    function register(bytes32 label, address owner) public {\n        require(expiryTimes[label] < now);\n\n        expiryTimes[label] = now + registrationPeriod;\n        ens.setSubnodeOwner(rootNode, label, owner);\n    }\n}\n"}}, "version": "1.0.0"}