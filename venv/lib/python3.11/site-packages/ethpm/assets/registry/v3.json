{"compilers": [{"contractTypes": ["Authorized", "WhitelistAuthority", "IndexedOrderedSetLib", "PackageDB", "PackageRegistry", "ReleaseDB", "ReleaseValidator"], "name": "solc", "settings": {"optimize": false}, "version": "0.4.26+commit.4563c3fc"}], "contractTypes": {"AuthorityInterface": {"abi": [{"constant": true, "inputs": [{"name": "callerAddress", "type": "address"}, {"name": "codeAddress", "type": "address"}, {"name": "sig", "type": "bytes4"}], "name": "canCall", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}], "deploymentBytecode": {"bytecode": "0x"}, "devdoc": {"methods": {}}, "runtimeBytecode": {"bytecode": "0x"}, "sourceId": "Authority.sol"}, "Authorized": {"abi": [{"constant": false, "inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "newAuthority", "type": "address"}], "name": "setAuthority", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "owner", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "authority", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"inputs": [], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "OwnerUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "oldAuthority", "type": "address"}, {"indexed": true, "name": "newAuthority", "type": "address"}], "name": "AuthorityUpdate", "type": "event"}], "deploymentBytecode": {"bytecode": "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"}, "devdoc": {"methods": {}}, "runtimeBytecode": {"bytecode": "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"}, "sourceId": "Authority.sol"}, "AuthorizedInterface": {"abi": [{"constant": false, "inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "newAuthority", "type": "address"}], "name": "setAuthority", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "owner", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "authority", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "OwnerUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "oldAuthority", "type": "address"}, {"indexed": true, "name": "newAuthority", "type": "address"}], "name": "AuthorityUpdate", "type": "event"}], "deploymentBytecode": {"bytecode": "0x"}, "devdoc": {"methods": {}}, "runtimeBytecode": {"bytecode": "0x"}, "sourceId": "Authority.sol"}, "IndexedOrderedSetLib": {"abi": [{"constant": true, "inputs": [{"name": "self", "type": "IndexedOrderedSetLib.IndexedOrderedSet storage"}], "name": "size", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "self", "type": "IndexedOrderedSetLib.IndexedOrderedSet storage"}, {"name": "value", "type": "bytes32"}], "name": "indexOf", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "self", "type": "IndexedOrderedSetLib.IndexedOrderedSet storage"}, {"name": "value", "type": "bytes32"}], "name": "contains", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "self", "type": "IndexedOrderedSetLib.IndexedOrderedSet storage"}, {"name": "idx", "type": "uint256"}], "name": "get", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "self", "type": "IndexedOrderedSetLib.IndexedOrderedSet storage"}, {"name": "value", "type": "bytes32"}], "name": "add", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "self", "type": "IndexedOrderedSetLib.IndexedOrderedSet storage"}, {"name": "value", "type": "bytes32"}], "name": "remove", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "self", "type": "IndexedOrderedSetLib.IndexedOrderedSet storage"}, {"name": "idx", "type": "uint256"}], "name": "pop", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}], "deploymentBytecode": {"bytecode": "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"}, "devdoc": {"author": "<PERSON>am <<EMAIL>>", "methods": {"add(IndexedOrderedSetLib.IndexedOrderedSet storage,bytes32)": {"details": "Pushes the new value onto the set", "params": {"self": "The set", "value": "The value to push."}}, "contains(IndexedOrderedSetLib.IndexedOrderedSet storage,bytes32)": {"details": "Returns boolean if the key is in the set", "params": {"self": "The set", "value": "The value to check"}}, "get(IndexedOrderedSetLib.IndexedOrderedSet storage,uint256)": {"details": "Retrieves the element at the provided index.", "params": {"idx": "The index to retrieve.", "self": "The set"}}, "indexOf(IndexedOrderedSetLib.IndexedOrderedSet storage,bytes32)": {"details": "Returns the index of the value in the set.", "params": {"self": "The set", "value": "The value to look up the index for."}}, "pop(IndexedOrderedSetLib.IndexedOrderedSet storage,uint256)": {"details": "Removes the element at index idx from the set and returns it.", "params": {"idx": "The index to remove and return.", "self": "The set"}}, "remove(IndexedOrderedSetLib.IndexedOrderedSet storage,bytes32)": {"details": "Removes the element at index idx from the set", "params": {"self": "The set", "value": "The value to remove from the set."}}, "size(IndexedOrderedSetLib.IndexedOrderedSet storage)": {"details": "Returns the size of the set", "params": {"self": "The set"}}}, "title": "Library implementing an array type which allows O(1) lookups on values."}, "runtimeBytecode": {"bytecode": "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"}, "sourceId": "IndexedOrderedSetLib.sol"}, "PackageDB": {"abi": [{"constant": false, "inputs": [{"name": "nameHash", "type": "bytes32"}, {"name": "reason", "type": "string"}], "name": "removePackage", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "nameHash", "type": "bytes32"}], "name": "getPackageName", "outputs": [{"name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "name", "type": "string"}], "name": "setPackage", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "nameHash", "type": "bytes32"}, {"name": "newPackageOwner", "type": "address"}], "name": "setPackageOwner", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "_offset", "type": "uint256"}, {"name": "limit", "type": "uint256"}], "name": "getAllPackageIds", "outputs": [{"name": "packageIds", "type": "bytes32[]"}, {"name": "offset", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getNumPackages", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "newAuthority", "type": "address"}], "name": "setAuthority", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "owner", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "idx", "type": "uint256"}], "name": "getPackageNameHash", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "nameHash", "type": "bytes32"}], "name": "packageExists", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "name", "type": "string"}], "name": "hashName", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "pure", "type": "function"}, {"constant": true, "inputs": [{"name": "nameHash", "type": "bytes32"}], "name": "getPackageData", "outputs": [{"name": "packageOwner", "type": "address"}, {"name": "createdAt", "type": "uint256"}, {"name": "updatedAt", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "authority", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "nameHash", "type": "bytes32"}, {"indexed": true, "name": "releaseHash", "type": "bytes32"}], "name": "PackageReleaseAdd", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "nameHash", "type": "bytes32"}, {"indexed": true, "name": "releaseHash", "type": "bytes32"}], "name": "PackageReleaseRemove", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "nameHash", "type": "bytes32"}], "name": "PackageCreate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "nameHash", "type": "bytes32"}, {"indexed": false, "name": "reason", "type": "string"}], "name": "PackageDelete", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "nameHash", "type": "bytes32"}, {"indexed": true, "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "PackageOwnerUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "OwnerUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "oldAuthority", "type": "address"}, {"indexed": true, "name": "newAuthority", "type": "address"}], "name": "AuthorityUpdate", "type": "event"}], "deploymentBytecode": {"bytecode": "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", "linkReferences": [{"length": 20, "name": "IndexedOrderedSetLib", "offsets": [2510, 3349, 4795, 5370]}]}, "devdoc": {"author": "<PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>", "methods": {"getAllPackageIds(uint256,uint256)": {"details": "Returns a slice of the array of all package hashes for the named package.", "params": {"limit": "The length of the slice", "offset": "The starting index for the slice."}}, "getNumPackages()": {"details": "Return the total number of packages"}, "getPackageData(bytes32)": {"details": "Returns information about the package.", "params": {"nameHash": "The name hash to look up."}}, "getPackageName(bytes32)": {"details": "Returns the package name for the given name<PERSON>h", "params": {"nameHash": "The name hash to look up."}}, "getPackageNameHash(uint256)": {"details": "Returns package namehash at the provided index from the set of all known name hashes.", "params": {"idx": "The index of the package name hash to retrieve."}}, "hashName(string)": {"details": "Returns name hash for a given package name.", "params": {"name": "Package name"}}, "packageExists(bytes32)": {"details": "Query the existence of a package with the given name.  Returns boolean indicating whether the package exists.", "params": {"nameHash": "The name hash of a package."}}, "removePackage(bytes32,string)": {"details": "Removes a package from the package db.  Packages with existing releases may not be removed.  Returns success.", "params": {"nameHash": "The name hash of a package."}}, "setPackage(string)": {"details": "Creates or updates a release for a package.  Returns success.", "params": {"name": "Package name"}}, "setPackageOwner(bytes32,address)": {"details": "Sets the owner of a package to the provided address.  Returns success.", "params": {"nameHash": "The name hash of a package.", "newPackageOwner": "The address of the new owner."}}}, "title": "Database contract for a package index package data."}, "runtimeBytecode": {"bytecode": "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", "linkReferences": [{"length": 20, "name": "IndexedOrderedSetLib", "offsets": [2325, 3164, 4610, 5185]}]}, "sourceId": "PackageDB.sol"}, "PackageRegistry": {"abi": [{"constant": true, "inputs": [{"name": "packageId", "type": "bytes32"}], "name": "getPackageName", "outputs": [{"name": "packageName", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "newReleaseValidator", "type": "address"}], "name": "setReleaseValidator", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}], "name": "getReleaseId", "outputs": [{"name": "releaseId", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getPackageDb", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "newPackageDb", "type": "address"}], "name": "setPackageDb", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}, {"name": "manifestURI", "type": "string"}], "name": "release", "outputs": [{"name": "releaseId", "type": "bytes32"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "offset", "type": "uint256"}, {"name": "limit", "type": "uint256"}], "name": "getAllPackageIds", "outputs": [{"name": "packageIds", "type": "bytes32[]"}, {"name": "pointer", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getReleaseValidator", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "releaseId", "type": "bytes32"}], "name": "getReleaseData", "outputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}, {"name": "manifestURI", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "name", "type": "string"}, {"name": "newPackageOwner", "type": "address"}], "name": "transferPackageOwner", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "newAuthority", "type": "address"}], "name": "setAuthority", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "name", "type": "string"}], "name": "packageExists", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "owner", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "numPackageIds", "outputs": [{"name": "totalCount", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}], "name": "generateReleaseId", "outputs": [{"name": "releaseId", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "authority", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "name", "type": "string"}], "name": "getPackageData", "outputs": [{"name": "packageOwner", "type": "address"}, {"name": "createdAt", "type": "uint256"}, {"name": "numReleases", "type": "uint256"}, {"name": "updatedAt", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "offset", "type": "uint256"}, {"name": "limit", "type": "uint256"}], "name": "getAllReleaseIds", "outputs": [{"name": "releaseIds", "type": "bytes32[]"}, {"name": "pointer", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}], "name": "numReleaseIds", "outputs": [{"name": "totalCount", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "name", "type": "string"}, {"name": "version", "type": "string"}], "name": "releaseExists", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "newReleaseDb", "type": "address"}], "name": "setReleaseDb", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "getReleaseDb", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": false, "name": "packageName", "type": "string"}, {"indexed": false, "name": "version", "type": "string"}, {"indexed": false, "name": "manifestURI", "type": "string"}], "name": "VersionRelease", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "PackageTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "OwnerUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "oldAuthority", "type": "address"}, {"indexed": true, "name": "newAuthority", "type": "address"}], "name": "AuthorityUpdate", "type": "event"}], "deploymentBytecode": {"bytecode": "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"}, "devdoc": {"author": "<PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>", "methods": {"generateReleaseId(string,string)": {"details": "Returns release id that *would* be generated for a name and version pair on `release`.", "params": {"packageName": "Package name", "version": "Version string (ex: '1.0.0')"}}, "getAllPackageIds(uint256,uint256)": {"details": "Returns a slice of the array of all package hashes for the named package.", "params": {"limit": "The length of the slice", "offset": "The starting index for the slice."}}, "getAllReleaseIds(string,uint256,uint256)": {"details": "Returns a slice of the array of all package hashes for the named package.", "params": {"limit": "The length of the slice", "offset": "The starting index for the slice."}}, "getPackageData(string)": {"details": "Returns the package data.", "params": {"name": "Package name"}}, "getPackageDb()": {"details": "Returns the address of the packageDb"}, "getPackageName(bytes32)": {"details": "Retrieves the name for the given name hash.", "params": {"packageId": "The name hash of package to lookup the name for."}}, "getReleaseData(bytes32)": {"details": "Returns the release data for the release associated with the given release hash.", "params": {"releaseId": "The release hash."}}, "getReleaseDb()": {"details": "Returns the address of the releaseDb"}, "getReleaseId(string,string)": {"details": "Returns the release id for a given name and version pair if present on registry.", "params": {"packageName": "Package name", "version": "Version string(ex: '1.0.0')"}}, "getReleaseValidator()": {"details": "Returns the address of the releaseV<PERSON><PERSON>tor"}, "numPackageIds()": {"details": "Returns the number of packages stored on the registry"}, "numReleaseIds(string)": {"details": "Returns the number of releases for a given package name on the registry", "params": {"packageName": "Package name"}}, "packageExists(string)": {"details": "Query the existence of a package with the given name.  Returns boolean indicating whether the package exists.", "params": {"name": "Package name"}}, "release(string,string,string)": {"details": "Creates a a new release for the named package.  If this is the first release for the given package then this will also assign msg.sender as the owner of the package.  Returns success.", "params": {"manifestURI": "The URI for the release manifest for this release.", "packageName": "Package name", "version": "Version string (ex: '1.0.0')"}}, "releaseExists(string,string)": {"details": "Query the existence of a release at the provided version for the named package.  Returns boolean indicating whether such a release exists.", "params": {"name": "Package name", "version": "Version string (ex: '1.0.0')"}}, "setPackageDb(address)": {"details": "Sets the address of the PackageDb contract.", "params": {"newPackageDb": "The address to set for the PackageDb."}}, "setReleaseDb(address)": {"details": "Sets the address of the ReleaseDb contract.", "params": {"newReleaseDb": "The address to set for the ReleaseDb."}}, "setReleaseValidator(address)": {"details": "Sets the address of the ReleaseValidator contract.", "params": {"newReleaseValidator": "The address to set for the ReleaseValidator."}}, "transferPackageOwner(string,address)": {"details": "Transfers package ownership to the provider new owner address.", "params": {"name": "Package name", "newPackageOwner": "The address of the new owner."}}}, "title": "Database contract for a package index."}, "runtimeBytecode": {"bytecode": "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"}, "sourceId": "PackageRegistry.sol"}, "PackageRegistryInterface": {"abi": [{"constant": true, "inputs": [{"name": "packageId", "type": "bytes32"}], "name": "getPackageName", "outputs": [{"name": "packageName", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}], "name": "getReleaseId", "outputs": [{"name": "releaseId", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}, {"name": "manifestURI", "type": "string"}], "name": "release", "outputs": [{"name": "releaseId", "type": "bytes32"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "offset", "type": "uint256"}, {"name": "limit", "type": "uint256"}], "name": "getAllPackageIds", "outputs": [{"name": "packageIds", "type": "bytes32[]"}, {"name": "pointer", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "releaseId", "type": "bytes32"}], "name": "getReleaseData", "outputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}, {"name": "manifestURI", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "numPackageIds", "outputs": [{"name": "totalCount", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "version", "type": "string"}], "name": "generateReleaseId", "outputs": [{"name": "releaseId", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}, {"name": "offset", "type": "uint256"}, {"name": "limit", "type": "uint256"}], "name": "getAllReleaseIds", "outputs": [{"name": "releaseIds", "type": "bytes32[]"}, {"name": "pointer", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageName", "type": "string"}], "name": "numReleaseIds", "outputs": [{"name": "totalCount", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}], "deploymentBytecode": {"bytecode": "0x"}, "devdoc": {"author": "<PERSON> <piperm<PERSON><EMAIL>>, <PERSON> <christopher<PERSON><PERSON><PERSON>@gmail.com>", "methods": {"getAllPackageIds(uint256,uint256)": {"details": "Returns a slice of the array of all package ids for the named package.", "params": {"limit": "The length of the slice", "offset": "The starting index for the slice."}}, "getAllReleaseIds(string,uint256,uint256)": {"details": "Returns a slice of the array of all release hashes for the named package.", "params": {"limit": "The length of the slice", "offset": "The starting index for the slice.", "packageName": "Package name"}}, "getPackageName(bytes32)": {"details": "Returns the string name of the package associated with a package id", "params": {"packageId": "The package id to look up"}}, "getReleaseData(bytes32)": {"details": "Returns the package data for a release.", "params": {"releaseId": "Release id"}}, "getReleaseId(string,string)": {"details": "Returns the release id for a given name and version pair if present on registry.", "params": {"packageName": "Package name", "version": "Version string(ex: '1.0.0')"}}, "numPackageIds()": {"details": "Returns the number of packages stored on the registry"}, "numReleaseIds(string)": {"details": "Returns the number of releases for a given package name on the registry", "params": {"packageName": "Package name"}}, "release(string,string,string)": {"details": "Creates a a new release for the named package.", "params": {"manifestURI": "The URI for the release manifest for this release.", "packageName": "Package name", "version": "Version string (ex: 1.0.0)"}}}, "title": "EIP 1319 Smart Contract Package Registry Interface"}, "runtimeBytecode": {"bytecode": "0x"}, "sourceId": "PackageRegistryInterface.sol"}, "ReleaseDB": {"abi": [{"constant": false, "inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "nameHash", "type": "bytes32"}], "name": "getNumReleasesForNameHash", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "version", "type": "string"}], "name": "hashVersion", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "pure", "type": "function"}, {"constant": false, "inputs": [{"name": "releaseId", "type": "bytes32"}, {"name": "reason", "type": "string"}], "name": "removeRelease", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"name": "releaseId", "type": "bytes32"}], "name": "releaseExists", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "releaseId", "type": "bytes32"}], "name": "getReleaseData", "outputs": [{"name": "nameHash", "type": "bytes32"}, {"name": "versionHash", "type": "bytes32"}, {"name": "createdAt", "type": "uint256"}, {"name": "updatedAt", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "version", "type": "string"}], "name": "setVersion", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "newAuthority", "type": "address"}], "name": "setAuthority", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "nameHash", "type": "bytes32"}, {"name": "versionHash", "type": "bytes32"}, {"name": "manifestURI", "type": "string"}], "name": "setRelease", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "owner", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "nameHash", "type": "bytes32"}, {"name": "versionHash", "type": "bytes32"}], "name": "hashRelease", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "pure", "type": "function"}, {"constant": true, "inputs": [{"name": "versionHash", "type": "bytes32"}], "name": "getVersion", "outputs": [{"name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "versionHash", "type": "bytes32"}], "name": "versionExists", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "authority", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "releaseHash", "type": "bytes32"}], "name": "releaseExisted", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "nameHash", "type": "bytes32"}, {"name": "idx", "type": "uint256"}], "name": "getReleaseIdForNameHash", "outputs": [{"name": "", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "releaseId", "type": "bytes32"}], "name": "getManifestURI", "outputs": [{"name": "", "type": "string"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "nameHash", "type": "bytes32"}, {"name": "_offset", "type": "uint256"}, {"name": "limit", "type": "uint256"}], "name": "getAllReleaseIds", "outputs": [{"name": "releaseIds", "type": "bytes32[]"}, {"name": "offset", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "releaseId", "type": "bytes32"}], "name": "ReleaseCreate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "releaseId", "type": "bytes32"}], "name": "ReleaseUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "releaseId", "type": "bytes32"}, {"indexed": false, "name": "reason", "type": "string"}], "name": "ReleaseDelete", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "OwnerUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "oldAuthority", "type": "address"}, {"indexed": true, "name": "newAuthority", "type": "address"}], "name": "AuthorityUpdate", "type": "event"}], "deploymentBytecode": {"bytecode": "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", "linkReferences": [{"length": 20, "name": "IndexedOrderedSetLib", "offsets": [2828, 3708, 3919, 5666, 5877, 6880]}]}, "devdoc": {"author": "<PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>", "methods": {"getAllReleaseIds(bytes32,uint256,uint256)": {"details": "Returns a slice of the array of all releases hashes for the named package.", "params": {"limit": "The length of the slice", "offset": "The starting index for the slice."}}, "getManifestURI(bytes32)": {"details": "Returns the URI of the release manifest for the given release hash.", "params": {"releaseId": "Release hash"}}, "getNumReleasesForNameHash(bytes32)": {"details": "Get the total number of releases", "params": {"nameHash": "the name hash to lookup."}}, "getReleaseData(bytes32)": {"details": "Returns the releaseData for the given release has a package.", "params": {"releaseId": "The release hash."}}, "getReleaseIdForNameHash(bytes32,uint256)": {"details": "Release hash for a Package at a given index", "params": {"idx": "The index of the release hash to retrieve.", "nameHash": "the name hash to lookup."}}, "getVersion(bytes32)": {"details": "Returns string version identifier from the version of the given release hash.", "params": {"versionHash": "the version hash"}}, "hashRelease(bytes32,bytes32)": {"details": "Returns release hash for the given release", "params": {"nameHash": "The name hash of the package name.", "versionHash": "The version hash for the release version."}}, "hashVersion(string)": {"details": "Returns version hash for the given semver version.", "params": {"version": "Version string"}}, "releaseExisted(bytes32)": {"details": "Query the past existence of a release at the provided version for a package.  Returns boolean indicating whether such a release ever existed.", "params": {"releaseHash": "The release hash to query."}}, "releaseExists(bytes32)": {"details": "Query the existence of a release at the provided version for a package.  Returns boolean indicating whether such a release exists.", "params": {"releaseId": "The release hash to query."}}, "removeRelease(bytes32,string)": {"details": "Removes a release from a package.  Returns success.", "params": {"reason": "Explanation for why the removal happened.", "releaseId": "The release hash to be removed"}}, "setRelease(bytes32,bytes32,string)": {"details": "Creates or updates a release for a package.  Returns success.", "params": {"manifestURI": "The URI for the release manifest for this release.", "nameHash": "The name hash of the package.", "versionHash": "The version hash for the release version."}}, "setVersion(string)": {"details": "Adds the given version to the local version database.  Returns the versionHash for the provided version.", "params": {"version": "Version string (ex: '1.0.0')"}}, "versionExists(bytes32)": {"details": "Query the existence of the provided version in the recorded versions.  Returns boolean indicating whether such a version exists.", "params": {"versionHash": "the version hash to check."}}}, "title": "Database contract for a package index."}, "runtimeBytecode": {"bytecode": "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", "linkReferences": [{"length": 20, "name": "IndexedOrderedSetLib", "offsets": [2643, 3523, 3734, 5481, 5692, 6695]}]}, "sourceId": "ReleaseDB.sol"}, "ReleaseValidator": {"abi": [{"constant": true, "inputs": [{"name": "packageDb", "type": "address"}, {"name": "releaseDb", "type": "address"}, {"name": "callerAddress", "type": "address"}, {"name": "name", "type": "string"}, {"name": "version", "type": "string"}, {"name": "manifestURI", "type": "string"}], "name": "validateRelease", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageDb", "type": "address"}, {"name": "releaseDb", "type": "address"}, {"name": "name", "type": "string"}, {"name": "version", "type": "string"}], "name": "validateIsNewRelease", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageDb", "type": "address"}, {"name": "callerAddress", "type": "address"}, {"name": "name", "type": "string"}], "name": "validateAuthorization", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "packageDb", "type": "address"}, {"name": "name", "type": "string"}], "name": "validatePackageName", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "value", "type": "string"}], "name": "validateStringIdentifier", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "pure", "type": "function"}], "deploymentBytecode": {"bytecode": "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"}, "devdoc": {"author": "<PERSON>am <<EMAIL>>", "methods": {"validateAuthorization(address,address,string)": {"details": "Validate whether the caller<PERSON>ddress is authorized to make this release.", "params": {"callerAddress": "The address which is attempting to create the release.", "name": "The name of the package.", "packageDb": "The address of the PackageDB"}}, "validateIsNewRelease(address,address,string,string)": {"details": "Validate that the version being released has not already been released.", "params": {"name": "The name of the package.", "packageDb": "The address of the PackageDB", "releaseDb": "The address of the ReleaseDB", "version": "The version string for the release"}}, "validatePackageName(address,string)": {"details": "Returns boolean whether the provided package name is valid.", "params": {"name": "The name of the package.", "packageDb": "The address of the PackageDB"}}, "validateRelease(address,address,address,string,string,string)": {"details": "Runs validation on all of the data needed for releasing a package.  Returns success.", "params": {"callerAddress": "The address which is attempting to create the release.", "manifestURI": "The URI of the release manifest.", "name": "The name of the package.", "packageDb": "The address of the PackageDB", "releaseDb": "The address of the ReleaseDB", "version": "The version string of the package (ex: `1.0.0`)"}}, "validateStringIdentifier(string)": {"details": "Returns boolean whether the input string has a length", "params": {"value": "The string to validate."}}}, "title": "Database contract for a package index."}, "runtimeBytecode": {"bytecode": "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"}, "sourceId": "ReleaseValidator.sol"}, "WhitelistAuthority": {"abi": [{"constant": false, "inputs": [{"name": "codeAddress", "type": "address"}, {"name": "sig", "type": "bytes4"}, {"name": "can", "type": "bool"}], "name": "setAnyoneCanCall", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "newAuthority", "type": "address"}], "name": "setAuthority", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "callerAddress", "type": "address"}, {"name": "codeAddress", "type": "address"}, {"name": "sig", "type": "bytes4"}, {"name": "can", "type": "bool"}], "name": "setCanCall", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "owner", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "callerAddress", "type": "address"}, {"name": "codeAddress", "type": "address"}, {"name": "sig", "type": "bytes4"}], "name": "canCall", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "authority", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "callerAddress", "type": "address"}, {"indexed": true, "name": "codeAddress", "type": "address"}, {"indexed": true, "name": "sig", "type": "bytes4"}, {"indexed": false, "name": "can", "type": "bool"}], "name": "SetCanCall", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "codeAddress", "type": "address"}, {"indexed": true, "name": "sig", "type": "bytes4"}, {"indexed": false, "name": "can", "type": "bool"}], "name": "SetAnyoneCanCall", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "OwnerUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "oldAuthority", "type": "address"}, {"indexed": true, "name": "newAuthority", "type": "address"}], "name": "AuthorityUpdate", "type": "event"}], "deploymentBytecode": {"bytecode": "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"}, "devdoc": {"methods": {}}, "runtimeBytecode": {"bytecode": "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"}, "sourceId": "Authority.sol"}, "WhitelistAuthorityInterface": {"abi": [{"constant": false, "inputs": [{"name": "codeAddress", "type": "address"}, {"name": "sig", "type": "bytes4"}, {"name": "can", "type": "bool"}], "name": "setAnyoneCanCall", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "newAuthority", "type": "address"}], "name": "setAuthority", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"name": "callerAddress", "type": "address"}, {"name": "codeAddress", "type": "address"}, {"name": "sig", "type": "bytes4"}, {"name": "can", "type": "bool"}], "name": "setCanCall", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "owner", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"name": "callerAddress", "type": "address"}, {"name": "codeAddress", "type": "address"}, {"name": "sig", "type": "bytes4"}], "name": "canCall", "outputs": [{"name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "authority", "outputs": [{"name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "callerAddress", "type": "address"}, {"indexed": true, "name": "codeAddress", "type": "address"}, {"indexed": true, "name": "sig", "type": "bytes4"}, {"indexed": false, "name": "can", "type": "bool"}], "name": "SetCanCall", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "codeAddress", "type": "address"}, {"indexed": true, "name": "sig", "type": "bytes4"}, {"indexed": false, "name": "can", "type": "bool"}], "name": "SetAnyoneCanCall", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "old<PERSON>wner", "type": "address"}, {"indexed": true, "name": "new<PERSON>wner", "type": "address"}], "name": "OwnerUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "name": "oldAuthority", "type": "address"}, {"indexed": true, "name": "newAuthority", "type": "address"}], "name": "AuthorityUpdate", "type": "event"}], "deploymentBytecode": {"bytecode": "0x"}, "devdoc": {"methods": {}}, "runtimeBytecode": {"bytecode": "0x"}, "sourceId": "Authority.sol"}}, "manifest": "ethpm/3", "name": "solidity-registry", "sources": {"./Authority.sol": {"content": "pragma solidity ^0.4.24;\npragma experimental \"v0.5.0\";\n\n\ncontract AuthorityInterface {\n  function canCall(\n    address callerAddress,\n    address codeAddress,\n    bytes4 sig\n  )\n    public\n    view\n    returns (bool);\n}\n\n\ncontract AuthorizedInterface {\n  address public owner;\n  AuthorityInterface public authority;\n\n  modifier auth {\n    require(isAuthorized(),\"escape:Authority:caller-not-authorized\");\n    _;\n  }\n\n  event OwnerUpdate(address indexed oldOwner, address indexed newOwner);\n  event AuthorityUpdate(address indexed oldAuthority, address indexed newAuthority);\n\n  function setOwner(address newOwner) public returns (bool);\n\n  function setAuthority(AuthorityInterface newAuthority) public returns (bool);\n\n  function isAuthorized() internal returns (bool);\n}\n\n\ncontract Authorized is AuthorizedInterface {\n  constructor() public {\n    owner = msg.sender;\n    emit OwnerUpdate(0x0, owner);\n  }\n\n  function setOwner(address newOwner)\n    public\n    auth\n    returns (bool)\n  {\n    emit OwnerUpdate(owner, newOwner);\n    owner = newOwner;\n    return true;\n  }\n\n  function setAuthority(AuthorityInterface newAuthority)\n    public\n    auth\n    returns (bool)\n  {\n    emit AuthorityUpdate(authority, newAuthority);\n    authority = newAuthority;\n    return true;\n  }\n\n  function isAuthorized() internal returns (bool) {\n    if (msg.sender == owner) {\n      return true;\n    } else if (address(authority) == (0)) {\n      return false;\n    } else {\n      return authority.canCall(msg.sender, this, msg.sig);\n    }\n  }\n}\n\n\ncontract WhitelistAuthorityInterface is AuthorityInterface, AuthorizedInterface {\n  event SetCanCall(\n    address indexed callerAddress,\n    address indexed codeAddress,\n    bytes4 indexed sig,\n    bool can\n  );\n\n  event SetAnyoneCanCall(\n    address indexed codeAddress,\n    bytes4 indexed sig,\n    bool can\n  );\n\n  function setCanCall(\n    address callerAddress,\n    address codeAddress,\n    bytes4 sig,\n    bool can\n  )\n    public\n    returns (bool);\n\n  function setAnyoneCanCall(\n    address codeAddress,\n    bytes4 sig,\n    bool can\n  )\n    public\n    returns (bool);\n}\n\n\ncontract WhitelistAuthority is WhitelistAuthorityInterface, Authorized {\n  mapping (address => mapping (address => mapping (bytes4 => bool))) _canCall;\n  mapping (address => mapping (bytes4 => bool)) _anyoneCanCall;\n\n  function canCall(\n    address callerAddress,\n    address codeAddress,\n    bytes4 sig\n  )\n    public\n    view\n    returns (bool)\n  {\n    if (_anyoneCanCall[codeAddress][sig]) {\n      return true;\n    } else {\n      return _canCall[callerAddress][codeAddress][sig];\n    }\n  }\n\n  function setCanCall(\n    address callerAddress,\n    address codeAddress,\n    bytes4 sig,\n    bool can\n  )\n    public\n    auth\n    returns (bool)\n  {\n    _canCall[callerAddress][codeAddress][sig] = can;\n    emit SetCanCall(callerAddress, codeAddress, sig, can);\n    return true;\n  }\n\n  function setAnyoneCanCall(\n    address codeAddress,\n    bytes4 sig,\n    bool can\n  )\n    public\n    auth\n    returns (bool)\n  {\n    _anyoneCanCall[codeAddress][sig] = can;\n    emit SetAnyoneCanCall(codeAddress, sig, can);\n    return true;\n  }\n}", "installPath": "./Authority.sol", "type": "solidity"}, "./IndexedOrderedSetLib.sol": {"content": "pragma solidity ^0.4.24;\npragma experimental \"v0.5.0\";\n\n/// @title Library implementing an array type which allows O(1) lookups on values.\n/// <AUTHOR> <<EMAIL>>\nlibrary IndexedOrderedSetLib {\n  struct IndexedOrderedSet {\n    bytes32[] _values;\n    mapping (bytes32 => uint) _valueIndices;\n    mapping (bytes32 => bool) _exists;\n  }\n\n  modifier requireValue(IndexedOrderedSet storage self, bytes32 value) {\n    require(contains(self, value), \"escape:IndexedOrderedSetLib:value-not-found\");\n    _;\n  }\n\n  /// @dev Returns the size of the set\n  /// @param self The set\n  function size(IndexedOrderedSet storage self)\n    public\n    view\n    returns (uint)\n  {\n    return self._values.length;\n  }\n\n  /// @dev Returns boolean if the key is in the set\n  /// @param self The set\n  /// @param value The value to check\n  function contains(IndexedOrderedSet storage self, bytes32 value)\n    public\n    view\n    returns (bool)\n  {\n    return self._exists[value];\n  }\n\n  /// @dev Returns the index of the value in the set.\n  /// @param self The set\n  /// @param value The value to look up the index for.\n  function indexOf(IndexedOrderedSet storage self, bytes32 value)\n    public\n    view\n    requireValue(self, value)\n    returns (uint)\n  {\n    return self._valueIndices[value];\n  }\n\n  /// @dev Removes the element at index idx from the set and returns it.\n  /// @param self The set\n  /// @param idx The index to remove and return.\n  function pop(IndexedOrderedSet storage self, uint idx) public returns (bytes32) {\n    bytes32 value = get(self, idx);\n\n    if (idx != self._values.length - 1) {\n      bytes32 movedValue = self._values[self._values.length - 1];\n      self._values[idx] = movedValue;\n      self._valueIndices[movedValue] = idx;\n    }\n    self._values.length -= 1;\n\n    delete self._valueIndices[value];\n    delete self._exists[value];\n\n    return value;\n  }\n\n  /// @dev Removes the element at index idx from the set\n  /// @param self The set\n  /// @param value The value to remove from the set.\n  function remove(IndexedOrderedSet storage self, bytes32 value)\n    public\n    requireValue(self, value)\n    returns (bool)\n  {\n    uint idx = indexOf(self, value);\n    pop(self, idx);\n    return true;\n  }\n\n  /// @dev Retrieves the element at the provided index.\n  /// @param self The set\n  /// @param idx The index to retrieve.\n  function get(IndexedOrderedSet storage self, uint idx)\n    public\n    view\n    returns (bytes32)\n  {\n    return self._values[idx];\n  }\n\n  /// @dev Pushes the new value onto the set\n  /// @param self The set\n  /// @param value The value to push.\n  function add(IndexedOrderedSet storage self, bytes32 value) public returns (bool) {\n    if (contains(self, value)) return true;\n\n    self._valueIndices[value] = self._values.length;\n    self._values.push(value);\n    self._exists[value] = true;\n\n    return true;\n  }\n}", "installPath": "./IndexedOrderedSetLib.sol", "type": "solidity"}, "./PackageDB.sol": {"content": "pragma solidity ^0.4.24;\npragma experimental \"v0.5.0\";\n\nimport {IndexedOrderedSetLib} from \"./IndexedOrderedSetLib.sol\";\nimport {Authorized} from \"./Authority.sol\";\n\n\n/// @title Database contract for a package index package data.\n/// <AUTHOR> <<EMAIL>>, <PERSON> <<EMAIL>>\ncontract PackageDB is Authorized {\n  using IndexedOrderedSetLib for IndexedOrderedSetLib.IndexedOrderedSet;\n\n  struct Package {\n    bool exists;\n    uint createdAt;\n    uint updatedAt;\n    string name;\n    address owner;\n  }\n\n  // Package Data: (nameHash => value)\n  mapping (bytes32 => Package) _recordedPackages;\n  IndexedOrderedSetLib.IndexedOrderedSet _allPackageNameHashes;\n\n  // Events\n  event PackageReleaseAdd(bytes32 indexed nameHash, bytes32 indexed releaseHash);\n  event PackageReleaseRemove(bytes32 indexed nameHash, bytes32 indexed releaseHash);\n  event PackageCreate(bytes32 indexed nameHash);\n  event PackageDelete(bytes32 indexed nameHash, string reason);\n  event PackageOwnerUpdate(bytes32 indexed nameHash, address indexed oldOwner, address indexed newOwner);\n\n  /*\n   *  Modifiers\n   */\n  modifier onlyIfPackageExists(bytes32 nameHash) {\n    require(packageExists(nameHash), \"escape:PackageDB:package-not-found\");\n    _;\n  }\n\n  //\n  //  +-------------+\n  //  |  Write API  |\n  //  +-------------+\n  //\n\n  /// @dev Creates or updates a release for a package.  Returns success.\n  /// @param name Package name\n  function setPackage(string name)\n    public\n    auth\n    returns (bool)\n  {\n    // Hash the name and the version for storing data\n    bytes32 nameHash = hashName(name);\n\n    Package storage package = _recordedPackages[nameHash];\n\n    // Mark the package as existing if it isn't already tracked.\n    if (!packageExists(nameHash)) {\n\n      // Set package data\n      package.exists = true;\n      package.createdAt = block.timestamp; // solium-disable-line security/no-block-members\n      package.name = name;\n\n      // Add the nameHash to the list of all package nameHashes.\n      _allPackageNameHashes.add(nameHash);\n\n      emit PackageCreate(nameHash);\n    }\n\n    package.updatedAt = block.timestamp; // solium-disable-line security/no-block-members\n\n    return true;\n  }\n\n  /// @dev Removes a package from the package db.  Packages with existing releases may not be removed.  Returns success.\n  /// @param nameHash The name hash of a package.\n  function removePackage(bytes32 nameHash, string reason)\n    public\n    auth\n    onlyIfPackageExists(nameHash)\n    returns (bool)\n  {\n    emit PackageDelete(nameHash, reason);\n\n    delete _recordedPackages[nameHash];\n    _allPackageNameHashes.remove(nameHash);\n\n    return true;\n  }\n\n  /// @dev Sets the owner of a package to the provided address.  Returns success.\n  /// @param nameHash The name hash of a package.\n  /// @param newPackageOwner The address of the new owner.\n  function setPackageOwner(bytes32 nameHash, address newPackageOwner)\n    public\n    auth\n    onlyIfPackageExists(nameHash)\n    returns (bool)\n  {\n    emit PackageOwnerUpdate(nameHash, _recordedPackages[nameHash].owner, newPackageOwner);\n\n    _recordedPackages[nameHash].owner = newPackageOwner;\n    _recordedPackages[nameHash].updatedAt = block.timestamp; // solium-disable-line security/no-block-members\n\n    return true;\n  }\n\n  //\n  //  +------------+\n  //  |  Read API  |\n  //  +------------+\n  //\n\n  /// @dev Query the existence of a package with the given name.  Returns boolean indicating whether the package exists.\n  /// @param nameHash The name hash of a package.\n  function packageExists(bytes32 nameHash)\n    public\n    view\n    returns (bool)\n  {\n    return _recordedPackages[nameHash].exists;\n  }\n\n  /// @dev Return the total number of packages\n  function getNumPackages()\n    public\n    view\n    returns (uint)\n  {\n    return _allPackageNameHashes.size();\n  }\n\n  /// @dev Returns package namehash at the provided index from the set of all known name hashes.\n  /// @param idx The index of the package name hash to retrieve.\n  function getPackageNameHash(uint idx)\n    public\n    view\n    returns (bytes32)\n  {\n    return _allPackageNameHashes.get(idx);\n  }\n\n  /// @dev Returns information about the package.\n  /// @param nameHash The name hash to look up.\n  function getPackageData(bytes32 nameHash)\n    public\n    view\n    onlyIfPackageExists(nameHash)\n    returns (\n      address packageOwner,\n      uint createdAt,\n      uint updatedAt\n    )\n  {\n    Package storage package = _recordedPackages[nameHash];\n    return (package.owner, package.createdAt, package.updatedAt);\n  }\n\n  /// @dev Returns the package name for the given namehash\n  /// @param nameHash The name hash to look up.\n  function getPackageName(bytes32 nameHash)\n    public\n    view\n    onlyIfPackageExists(nameHash)\n    returns (string)\n  {\n    return _recordedPackages[nameHash].name;\n  }\n\n  /// @dev Returns a slice of the array of all package hashes for the named package.\n  /// @param offset The starting index for the slice.\n  /// @param limit  The length of the slice\n  function getAllPackageIds(uint _offset, uint limit)\n    public\n    view\n    returns (\n      bytes32[] packageIds,\n      uint offset\n    )\n  {\n    bytes32[] memory hashes;                 // Array of package ids to return\n    uint cursor = _offset;                   // Index counter to traverse DB array\n    uint remaining;                          // Counter to collect `limit` packages\n    uint totalPackages = getNumPackages();   // Total number of packages in registry\n\n    // Is request within range?\n    if (cursor < totalPackages){\n\n      // Get total remaining records\n      remaining = totalPackages - cursor;\n\n      // Number of records to collect is lesser of `remaining` and `limit`\n      if (remaining > limit ){\n        remaining = limit;\n      }\n\n      // Allocate return array\n      hashes = new bytes32[](remaining);\n\n      // Collect records. (IndexedOrderedSet manages deletions.)\n      while(remaining > 0){\n        bytes32 hash = getPackageNameHash(cursor);\n        hashes[remaining - 1] = hash;\n        remaining--;\n        cursor++;\n      }\n    }\n    return (hashes, cursor);\n  }\n\n  /*\n   *  Hash Functions\n   */\n  /// @dev Returns name hash for a given package name.\n  /// @param name Package name\n  function hashName(string name)\n    public\n    pure\n    returns (bytes32)\n  {\n    return keccak256(abi.encodePacked(name));\n  }\n}", "installPath": "./PackageDB.sol", "type": "solidity"}, "./PackageRegistry.sol": {"content": "pragma solidity ^0.4.24;\npragma experimental \"v0.5.0\";\n\n\nimport {PackageDB} from \"./PackageDB.sol\";\nimport {ReleaseDB} from \"./ReleaseDB.sol\";\nimport {ReleaseValidator} from \"./ReleaseValidator.sol\";\nimport {PackageRegistryInterface} from \"./PackageRegistryInterface.sol\";\nimport {Authorized} from \"./Authority.sol\";\n\n\n/// @title Database contract for a package index.\n/// <AUTHOR> <<EMAIL>>, <PERSON>rriam <<EMAIL>>\ncontract PackageRegistry is Authorized, PackageRegistryInterface {\n  PackageDB private packageDb;\n  ReleaseDB private releaseDb;\n  ReleaseValidator private releaseValidator;\n\n  // Events\n  event VersionRelease(string packageName, string version, string manifestURI);\n  event PackageTransfer(address indexed oldOwner, address indexed newOwner);\n\n  //\n  // Administrative API\n  //\n  /// @dev Sets the address of the PackageDb contract.\n  /// @param newPackageDb The address to set for the PackageDb.\n  function setPackageDb(address newPackageDb)\n    public\n    auth\n    returns (bool)\n  {\n    packageDb = PackageDB(newPackageDb);\n    return true;\n  }\n\n  /// @dev Sets the address of the ReleaseDb contract.\n  /// @param newReleaseDb The address to set for the ReleaseDb.\n  function setReleaseDb(address newReleaseDb)\n    public\n    auth\n    returns (bool)\n  {\n    releaseDb = ReleaseDB(newReleaseDb);\n    return true;\n  }\n\n  /// @dev Sets the address of the ReleaseValidator contract.\n  /// @param newReleaseValidator The address to set for the ReleaseValidator.\n  function setReleaseValidator(address newReleaseValidator)\n    public\n    auth\n    returns (bool)\n  {\n    releaseValidator = ReleaseValidator(newReleaseValidator);\n    return true;\n  }\n\n  //\n  // +-------------+\n  // |  Write API  |\n  // +-------------+\n  //\n  /// @dev Creates a a new release for the named package.  If this is the first release for the given package then this will also assign msg.sender as the owner of the package.  Returns success.\n  /// @notice Will create a new release the given package with the given release information.\n  /// @param packageName Package name\n  /// @param version Version string (ex: '1.0.0')\n  /// @param manifestURI The URI for the release manifest for this release.\n  function release(\n    string packageName,\n    string version,\n    string manifestURI\n  )\n    public\n    auth\n    returns (bytes32 releaseId)\n  {\n    require(address(packageDb) != 0x0,        \"escape:PackageIndex:package-db-not-set\");\n    require(address(releaseDb) != 0x0,        \"escape:PackageIndex:release-db-not-set\");\n    require(address(releaseValidator) != 0x0, \"escape:PackageIndex:release-validator-not-set\");\n\n    bytes32 versionHash = releaseDb.hashVersion(version);\n\n    // If the version for this release is not in the version database, populate\n    // it.  This must happen prior to validation to ensure that the version is\n    // present in the releaseDb.\n    if (!releaseDb.versionExists(versionHash)) {\n      releaseDb.setVersion(version);\n    }\n\n    // Run release validator. This method reverts with an error message string\n    // on failure.\n    releaseValidator.validateRelease(\n      packageDb,\n      releaseDb,\n      msg.sender,\n      packageName,\n      version,\n      manifestURI\n    );\n\n    // Compute hashes\n    bool _packageExists = packageExists(packageName);\n\n    // Both creates the package if it is new as well as updating the updatedAt\n    // timestamp on the package.\n    packageDb.setPackage(packageName);\n\n    bytes32 nameHash = packageDb.hashName(packageName);\n\n    // If the package does not yet exist create it and set the owner\n    if (!_packageExists) {\n      packageDb.setPackageOwner(nameHash, msg.sender);\n    }\n\n    // Create the release and add it to the list of package release hashes.\n    releaseDb.setRelease(nameHash, versionHash, manifestURI);\n\n    // Log the release.\n    releaseId = releaseDb.hashRelease(nameHash, versionHash);\n    emit VersionRelease(packageName, version, manifestURI);\n\n    return releaseId;\n  }\n\n  /// @dev Transfers package ownership to the provider new owner address.\n  /// @notice Will transfer ownership of this package to the provided new owner address.\n  /// @param name Package name\n  /// @param newPackageOwner The address of the new owner.\n  function transferPackageOwner(string name, address newPackageOwner)\n    public\n    auth\n    returns (bool)\n  {\n    if (isPackageOwner(name, msg.sender)) {\n      // Only the package owner may transfer package ownership.\n      return false;\n    }\n\n    // Lookup the current owner\n    address packageOwner;\n    (packageOwner,,,) = getPackageData(name);\n\n    // Log the transfer\n    emit PackageTransfer(packageOwner, newPackageOwner);\n\n    // Update the owner.\n    packageDb.setPackageOwner(packageDb.hashName(name), newPackageOwner);\n\n    return true;\n  }\n\n  //\n  // +------------+\n  // |  Read API  |\n  // +------------+\n  //\n\n  /// @dev Returns the address of the packageDb\n  function getPackageDb()\n    public\n    view\n    returns (address)\n  {\n    return address(packageDb);\n  }\n\n  /// @dev Returns the address of the releaseDb\n  function getReleaseDb()\n    public\n    view\n    returns (address)\n  {\n    return address(releaseDb);\n  }\n\n  /// @dev Returns the address of the releaseValidator\n  function getReleaseValidator()\n    public\n    view\n    returns (address)\n  {\n    return address(releaseValidator);\n  }\n\n  /// @dev Query the existence of a package with the given name.  Returns boolean indicating whether the package exists.\n  /// @param name Package name\n  function packageExists(string name)\n    public\n    view\n    returns (bool)\n  {\n    return packageDb.packageExists(packageDb.hashName(name));\n  }\n\n  /// @dev Query the existence of a release at the provided version for the named package.  Returns boolean indicating whether such a release exists.\n  /// @param name Package name\n  /// @param version Version string (ex: '1.0.0')\n  function releaseExists(\n    string name,\n    string version\n  )\n    public\n    view\n    returns (bool)\n  {\n    bytes32 nameHash = packageDb.hashName(name);\n    bytes32 versionHash = releaseDb.hashVersion(version);\n    return releaseDb.releaseExists(releaseDb.hashRelease(nameHash, versionHash));\n  }\n\n  /// @dev Returns a slice of the array of all package hashes for the named package.\n  /// @param offset The starting index for the slice.\n  /// @param limit  The length of the slice\n  function getAllPackageIds(uint offset, uint limit)\n    public\n    view\n    returns(\n      bytes32[] packageIds,\n      uint pointer\n    )\n  {\n    return packageDb.getAllPackageIds(offset, limit);\n  }\n\n  /// @dev Retrieves the name for the given name hash.\n  /// @param packageId The name hash of package to lookup the name for.\n  function getPackageName(bytes32 packageId)\n    public\n    view\n    returns (string packageName)\n  {\n    return packageDb.getPackageName(packageId);\n  }\n\n  /// @dev Returns the package data.\n  /// @param name Package name\n  function getPackageData(string name)\n    public\n    view\n    returns (\n      address packageOwner,\n      uint createdAt,\n      uint numReleases,\n      uint updatedAt\n    )\n  {\n    bytes32 nameHash = packageDb.hashName(name);\n    (packageOwner, createdAt, updatedAt) = packageDb.getPackageData(nameHash);\n    numReleases = releaseDb.getNumReleasesForNameHash(nameHash);\n    return (packageOwner, createdAt, numReleases, updatedAt);\n  }\n\n  /// @dev Returns the release data for the release associated with the given release hash.\n  /// @param releaseId The release hash.\n  function getReleaseData(bytes32 releaseId)\n    public\n    view\n    returns (\n      string packageName,\n      string version,\n      string manifestURI\n    )\n  {\n    bytes32 versionHash;\n    bytes32 nameHash;\n    (nameHash,versionHash, ,) = releaseDb.getReleaseData(releaseId);\n\n    packageName = packageDb.getPackageName(nameHash);\n    version = releaseDb.getVersion(versionHash);\n    manifestURI = releaseDb.getManifestURI(releaseId);\n\n    return (packageName, version, manifestURI);\n  }\n\n  /// @dev Returns a slice of the array of all package hashes for the named package.\n  /// @param offset The starting index for the slice.\n  /// @param limit  The length of the slice\n  function getAllReleaseIds(string packageName, uint offset, uint limit)\n    public\n    view\n    returns (\n      bytes32[] releaseIds,\n      uint pointer\n    )\n  {\n    bytes32 nameHash = packageDb.hashName(packageName);\n    return releaseDb.getAllReleaseIds(nameHash, offset, limit);\n  }\n\n  /// @dev Returns release id that *would* be generated for a name and version pair on `release`.\n  /// @param packageName Package name\n  /// @param version Version string (ex: '1.0.0')\n  function generateReleaseId(string packageName, string version)\n    public\n    view\n    returns (bytes32 releaseId)\n  {\n    bytes32 nameHash = packageDb.hashName(packageName);\n    bytes32 versionHash = releaseDb.hashVersion(version);\n    return keccak256(abi.encodePacked(nameHash, versionHash));\n  }\n\n  /// @dev Returns the release id for a given name and version pair if present on registry.\n  /// @param packageName Package name\n  /// @param version Version string(ex: '1.0.0')\n  function getReleaseId(string packageName, string version)\n    public\n    view\n    returns (bytes32 releaseId)\n  {\n    releaseId = generateReleaseId(packageName, version);\n    bool _releaseExists = releaseDb.releaseExists(releaseId);\n    if (!_releaseExists) {\n      return 0;\n    }\n    return releaseId;\n  }\n\n  /// @dev Returns the number of packages stored on the registry\n  function numPackageIds()\n    public\n    view\n    returns (uint totalCount)\n  {\n    return packageDb.getNumPackages();\n  }\n\n  /// @dev Returns the number of releases for a given package name on the registry\n  /// @param packageName Package name\n  function numReleaseIds(string packageName)\n    public\n    view\n    returns (uint totalCount)\n  {\n    bool _packageExists = packageExists(packageName);\n    if (!_packageExists) {\n      return 0;\n    }\n    bytes32 nameHash = packageDb.hashName(packageName);\n    return releaseDb.getNumReleasesForNameHash(nameHash);\n  }\n\n  //\n  // +----------------+\n  // |  Internal API  |\n  // +----------------+\n  //\n  /// @dev Returns boolean whether the provided address is the package owner\n  /// @param name The name of the package\n  /// @param _address The address to check\n  function isPackageOwner(string name, address _address)\n    internal\n    view\n    returns (bool)\n  {\n    address packageOwner;\n    (packageOwner,,,) = getPackageData(name);\n    return (packageOwner != _address);\n  }\n}", "installPath": "./PackageRegistry.sol", "type": "solidity"}, "./PackageRegistryInterface.sol": {"content": "pragma solidity ^0.4.24;\npragma experimental \"v0.5.0\";\n\n\n/// @title EIP 1319 Smart Contract Package Registry Interface\n/// <AUTHOR> <<EMAIL>>, <PERSON> <christopher<PERSON><PERSON><PERSON>@gmail.com>\ncontract PackageRegistryInterface {\n\n  //\n  // +-------------+\n  // |  Write API  |\n  // +-------------+\n  //\n\n  /// @dev Creates a a new release for the named package.\n  /// @notice Will create a new release the given package with the given release information.\n  /// @param packageName Package name\n  /// @param version Version string (ex: 1.0.0)\n  /// @param manifestURI The URI for the release manifest for this release.\n  function release(\n    string packageName,\n    string version,\n    string manifestURI\n  )\n    public\n    returns (bytes32 releaseId);\n\n  //\n  // +------------+\n  // |  Read API  |\n  // +------------+\n  //\n\n  /// @dev Returns the string name of the package associated with a package id\n  /// @param packageId The package id to look up\n  function getPackageName(bytes32 packageId)\n    public\n    view\n    returns (string packageName);\n\n  /// @dev Returns a slice of the array of all package ids for the named package.\n  /// @param offset The starting index for the slice.\n  /// @param limit  The length of the slice\n  function getAllPackageIds(uint offset, uint limit)\n    public\n    view\n    returns (\n      bytes32[] packageIds,\n      uint pointer\n    );\n\n  /// @dev Returns a slice of the array of all release hashes for the named package.\n  /// @param packageName Package name\n  /// @param offset The starting index for the slice.\n  /// @param limit  The length of the slice\n  function getAllReleaseIds(string packageName, uint offset, uint limit)\n    public\n    view\n    returns (\n      bytes32[] releaseIds,\n      uint pointer\n    );\n\n  /// @dev Returns the package data for a release.\n  /// @param releaseId Release id\n  function getReleaseData(bytes32 releaseId)\n    public\n    view\n    returns (\n      string packageName,\n      string version,\n      string manifestURI\n    );\n\n  // @dev Returns release id that *would* be generated for a name and version pair on `release`.\n  // @param packageName Package name\n  // @param version Version string (ex: '1.0.0')\n  function generateReleaseId(string packageName, string version)\n    public\n    view\n    returns (bytes32 releaseId);\n\n  /// @dev Returns the release id for a given name and version pair if present on registry.\n  /// @param packageName Package name\n  /// @param version Version string(ex: '1.0.0')\n  function getReleaseId(string packageName, string version)\n    public\n    view\n    returns (bytes32 releaseId);\n\n  /// @dev Returns the number of packages stored on the registry\n  function numPackageIds() public view returns (uint totalCount);\n\n  /// @dev Returns the number of releases for a given package name on the registry\n  /// @param packageName Package name\n  function numReleaseIds(string packageName) public view returns (uint totalCount);\n}", "installPath": "./PackageRegistryInterface.sol", "type": "solidity"}, "./ReleaseDB.sol": {"content": "pragma solidity ^0.4.24;\npragma experimental \"v0.5.0\";\n\n\nimport {IndexedOrderedSetLib} from \"./IndexedOrderedSetLib.sol\";\nimport {Authorized} from \"./Authority.sol\";\n\n\n/// @title Database contract for a package index.\n/// <AUTHOR> <<EMAIL>>, <PERSON> <<EMAIL>>\ncontract ReleaseDB is Authorized {\n  using IndexedOrderedSetLib for IndexedOrderedSetLib.IndexedOrderedSet;\n\n  struct Release {\n    bool exists;\n    uint createdAt;\n    uint updatedAt;\n    bytes32 nameHash;\n    bytes32 versionHash;\n    string manifestURI;\n  }\n\n  // Release Data: (releaseId => value)\n  mapping (bytes32 => Release) _recordedReleases;\n  mapping (bytes32 => bool) _removedReleases;\n  IndexedOrderedSetLib.IndexedOrderedSet _allReleaseIds;\n  mapping (bytes32 => IndexedOrderedSetLib.IndexedOrderedSet) _releaseIdsByNameHash;\n\n  // Version Data: (versionHash => value)\n  mapping (bytes32 => string) _recordedVersions;\n  mapping (bytes32 => bool) _versionExists;\n\n  // Events\n  event ReleaseCreate(bytes32 indexed releaseId);\n  event ReleaseUpdate(bytes32 indexed releaseId);\n  event ReleaseDelete(bytes32 indexed releaseId, string reason);\n\n  /*\n   *  Modifiers\n   */\n  modifier onlyIfVersionExists(bytes32 versionHash) {\n    require(versionExists(versionHash), \"escape:ReleaseDB:version-not-found\");\n    _;\n  }\n\n  modifier onlyIfReleaseExists(bytes32 releaseId) {\n    require(releaseExists(releaseId), \"escape:ReleaseDB:release-not-found\");\n    _;\n  }\n\n  //\n  // +-------------+\n  // |  Write API  |\n  // +-------------+\n  //\n\n  /// @dev Creates or updates a release for a package.  Returns success.\n  /// @param nameHash The name hash of the package.\n  /// @param versionHash The version hash for the release version.\n  /// @param manifestURI The URI for the release manifest for this release.\n  function setRelease(\n    bytes32 nameHash,\n    bytes32 versionHash,\n    string manifestURI\n  )\n    public\n    auth\n    returns (bool)\n  {\n    bytes32 releaseId = hashRelease(nameHash, versionHash);\n\n    Release storage release = _recordedReleases[releaseId];\n\n    // If this is a new version push it onto the array of version hashes for\n    // this package.\n    if (release.exists) {\n      emit ReleaseUpdate(releaseId);\n    } else {\n      // Populate the basic release data.\n      release.exists = true;\n      release.createdAt = block.timestamp; // solium-disable-line security/no-block-members\n      release.nameHash = nameHash;\n      release.versionHash = versionHash;\n\n      // Push the release hash into the array of all release hashes.\n      _allReleaseIds.add(releaseId);\n      _releaseIdsByNameHash[nameHash].add(releaseId);\n\n      emit ReleaseCreate(releaseId);\n    }\n\n    // Record the last time the release was updated.\n    release.updatedAt = block.timestamp; // solium-disable-line security/no-block-members\n\n    // Save the release manifest URI\n    release.manifestURI = manifestURI;\n\n    return true;\n  }\n\n  /// @dev Removes a release from a package.  Returns success.\n  /// @param releaseId The release hash to be removed\n  /// @param reason Explanation for why the removal happened.\n  function removeRelease(bytes32 releaseId, string reason)\n    public\n    auth\n    onlyIfReleaseExists(releaseId)\n    returns (bool)\n  {\n    bytes32 nameHash;\n    bytes32 versionHash;\n\n    (nameHash, versionHash,,) = getReleaseData(releaseId);\n\n    // Zero out the release data.\n    delete _recordedReleases[releaseId];\n    delete _recordedVersions[versionHash];\n\n    // Remove the release hash from the list of all release hashes\n    _allReleaseIds.remove(releaseId);\n    _releaseIdsByNameHash[nameHash].remove(releaseId);\n\n    // Add the release hash to the map of removed releases\n    _removedReleases[releaseId] = true;\n\n    // Log the removal.\n    emit ReleaseDelete(releaseId, reason);\n\n    return true;\n  }\n\n\n  /// @dev Adds the given version to the local version database.  Returns the versionHash for the provided version.\n  /// @param version Version string (ex: '1.0.0')\n  function setVersion(string version)\n    public\n    auth\n    returns (bytes32)\n  {\n    bytes32 versionHash = hashVersion(version);\n\n    if (!_versionExists[versionHash]) {\n      _recordedVersions[versionHash] = version;\n      _versionExists[versionHash] = true;\n    }\n    return versionHash;\n  }\n\n  //\n  // +------------+\n  // |  Read API  |\n  // +------------+\n  //\n\n  /// @dev Returns a slice of the array of all releases hashes for the named package.\n  /// @param offset The starting index for the slice.\n  /// @param limit  The length of the slice\n  function getAllReleaseIds(bytes32 nameHash, uint _offset, uint limit)\n    public\n    view\n    returns (\n      bytes32[] releaseIds,\n      uint offset\n    )\n  {\n    bytes32[] memory hashes;                                  // Release ids to return\n    uint cursor = _offset;                                    // Index counter to traverse DB array\n    uint remaining;                                           // Counter to collect `limit` packages\n    uint totalReleases = getNumReleasesForNameHash(nameHash); // Total number of packages in registry\n\n    // Is request within range?\n    if (cursor < totalReleases){\n\n      // Get total remaining records\n      remaining = totalReleases - cursor;\n\n      // Number of records to collect is lesser of `remaining` and `limit`\n      if (remaining > limit ){\n        remaining = limit;\n      }\n\n      // Allocate return array\n      hashes = new bytes32[](remaining);\n\n      // Collect records. (IndexedOrderedSet manages deletions.)\n      while(remaining > 0){\n        bytes32 hash = getReleaseIdForNameHash(nameHash, cursor);\n        hashes[remaining - 1] = hash;\n        remaining--;\n        cursor++;\n      }\n    }\n    return (hashes, cursor);\n  }\n\n  /// @dev Get the total number of releases\n  /// @param nameHash the name hash to lookup.\n  function getNumReleasesForNameHash(bytes32 nameHash)\n    public\n    view\n    returns (uint)\n  {\n    return _releaseIdsByNameHash[nameHash].size();\n  }\n\n  /// @dev Release hash for a Package at a given index\n  /// @param nameHash the name hash to lookup.\n  /// @param idx The index of the release hash to retrieve.\n  function getReleaseIdForNameHash(bytes32 nameHash, uint idx)\n    public\n    view\n    returns (bytes32)\n  {\n    return _releaseIdsByNameHash[nameHash].get(idx);\n  }\n\n  /// @dev Query the existence of a release at the provided version for a package.  Returns boolean indicating whether such a release exists.\n  /// @param releaseId The release hash to query.\n  function releaseExists(bytes32 releaseId)\n    public\n    view\n    returns (bool)\n  {\n    return _recordedReleases[releaseId].exists;\n  }\n\n  /// @dev Query the past existence of a release at the provided version for a package.  Returns boolean indicating whether such a release ever existed.\n  /// @param releaseHash The release hash to query.\n  function releaseExisted(bytes32 releaseHash)\n    public\n    view\n    returns (bool)\n  {\n    return _removedReleases[releaseHash];\n  }\n\n  /// @dev Query the existence of the provided version in the recorded versions.  Returns boolean indicating whether such a version exists.\n  /// @param versionHash the version hash to check.\n  function versionExists(bytes32 versionHash)\n    public\n    view\n    returns (bool)\n  {\n    return _versionExists[versionHash];\n  }\n\n  /// @dev Returns the releaseData for the given release has a package.\n  /// @param releaseId The release hash.\n  function getReleaseData(bytes32 releaseId)\n    public\n    view\n    onlyIfReleaseExists(releaseId)\n    returns (\n      bytes32 nameHash,\n      bytes32 versionHash,\n      uint createdAt,\n      uint updatedAt\n    )\n  {\n    Release storage release = _recordedReleases[releaseId];\n    return (release.nameHash, release.versionHash, release.createdAt, release.updatedAt);\n  }\n\n  /// @dev Returns string version identifier from the version of the given release hash.\n  /// @param versionHash the version hash\n  function getVersion(bytes32 versionHash)\n    public\n    view\n    onlyIfVersionExists(versionHash)\n    returns (string)\n  {\n    return _recordedVersions[versionHash];\n  }\n\n  /// @dev Returns the URI of the release manifest for the given release hash.\n  /// @param releaseId Release hash\n  function getManifestURI(bytes32 releaseId)\n    public\n    view\n    onlyIfReleaseExists(releaseId)\n    returns (string)\n  {\n    return _recordedReleases[releaseId].manifestURI;\n  }\n\n  /*\n   *  Hash Functions\n   */\n  /// @dev Returns version hash for the given semver version.\n  /// @param version Version string\n  function hashVersion(string version)\n    public\n    pure\n    returns (bytes32)\n  {\n    return keccak256(abi.encodePacked(version));\n  }\n\n  /// @dev Returns release hash for the given release\n  /// @param nameHash The name hash of the package name.\n  /// @param versionHash The version hash for the release version.\n  function hashRelease(bytes32 nameHash, bytes32 versionHash)\n    public\n    pure\n    returns (bytes32)\n  {\n    return keccak256(abi.encodePacked(nameHash, versionHash));\n  }\n}", "installPath": "./ReleaseDB.sol", "type": "solidity"}, "./ReleaseValidator.sol": {"content": "pragma solidity ^0.4.24;\npragma experimental \"v0.5.0\";\n\nimport {PackageDB} from \"./PackageDB.sol\";\nimport {ReleaseDB} from \"./ReleaseDB.sol\";\n\n/// @title Database contract for a package index.\n/// <AUTHOR> <<EMAIL>>\ncontract ReleaseValidator {\n  /// @dev Runs validation on all of the data needed for releasing a package.  Returns success.\n  /// @param packageDb The address of the PackageDB\n  /// @param releaseDb The address of the ReleaseDB\n  /// @param callerAddress The address which is attempting to create the release.\n  /// @param name The name of the package.\n  /// @param version The version string of the package (ex: `1.0.0`)\n  /// @param manifestURI The URI of the release manifest.\n  function validateRelease(\n    PackageDB packageDb,\n    ReleaseDB releaseDb,\n    address callerAddress,\n    string name,\n    string version,\n    string manifestURI\n  )\n    public\n    view\n    returns (bool)\n  {\n    if (address(packageDb) == 0x0){\n      // packageDb address is null\n      revert(\"escape:ReleaseValidator:package-db-not-set\");\n    } else if (address(releaseDb) == 0x0){\n      // releaseDb address is null\n      revert(\"escape:ReleaseValidator:release-db-not-set\");\n    } else if (!validateAuthorization(packageDb, callerAddress, name)) {\n      // package exists and msg.sender is not the owner not the package owner.\n      revert(\"escape:ReleaseValidator:caller-not-authorized\");\n    } else if (!validateIsNewRelease(packageDb, releaseDb, name, version)) {\n      // this version has already been released.\n      revert(\"escape:ReleaseValidator:version-previously-published\");\n    } else if (!validatePackageName(packageDb, name)) {\n      // invalid package name.\n      revert(\"escape:ReleaseValidator:invalid-package-name\");\n    } else if (!validateStringIdentifier(manifestURI)) {\n      // disallow empty release manifest URI\n      revert(\"escape:ReleaseValidator:invalid-manifest-uri\");\n    } else if (!validateStringIdentifier(version)) {\n      // disallow version 0.0.0\n      revert(\"escape:ReleaseValidator:invalid-release-version\");\n    }\n    return true;\n  }\n\n  /// @dev Validate whether the callerAddress is authorized to make this release.\n  /// @param packageDb The address of the PackageDB\n  /// @param callerAddress The address which is attempting to create the release.\n  /// @param name The name of the package.\n  function validateAuthorization(\n    PackageDB packageDb,\n    address callerAddress,\n    string name\n  )\n    public\n    view\n    returns (bool)\n  {\n    bytes32 nameHash = packageDb.hashName(name);\n    if (!packageDb.packageExists(nameHash)) {\n      return true;\n    }\n    address packageOwner;\n\n    (packageOwner,,) = packageDb.getPackageData(nameHash);\n\n    if (packageOwner == callerAddress) {\n      return true;\n    }\n    return false;\n  }\n\n  /// @dev Validate that the version being released has not already been released.\n  /// @param packageDb The address of the PackageDB\n  /// @param releaseDb The address of the ReleaseDB\n  /// @param name The name of the package.\n  /// @param version The version string for the release\n  function validateIsNewRelease(\n    PackageDB packageDb,\n    ReleaseDB releaseDb,\n    string name,\n    string version\n  )\n    public\n    view\n    returns (bool)\n  {\n    bytes32 nameHash = packageDb.hashName(name);\n    bytes32 versionHash = releaseDb.hashVersion(version);\n    bytes32 releaseHash = releaseDb.hashRelease(nameHash, versionHash);\n    return !releaseDb.releaseExists(releaseHash) && !releaseDb.releaseExisted(releaseHash);\n  }\n\n  uint constant DIGIT_0 = uint(bytes1(\"0\"));\n  uint constant DIGIT_9 = uint(bytes1(\"9\"));\n  uint constant LETTER_a = uint(bytes1(\"a\"));\n  uint constant LETTER_z = uint(bytes1(\"z\"));\n  bytes1 constant DASH = bytes1(\"-\");\n\n  /// @dev Returns boolean whether the provided package name is valid.\n  /// @param packageDb The address of the PackageDB\n  /// @param name The name of the package.\n  function validatePackageName(PackageDB packageDb, string name)\n    public\n    view\n    returns (bool)\n  {\n    bytes32 nameHash = packageDb.hashName(name);\n\n    if (packageDb.packageExists(nameHash)) {\n      // existing names are always valid.\n      return true;\n    }\n\n    if (bytes(name).length < 2 || bytes(name).length > 255) {\n      return false;\n    }\n    for (uint i = 0; i < bytes(name).length; i++) {\n      if (bytes(name)[i] == DASH && i > 0) {\n        continue;\n      } else if (i > 0 && uint(bytes(name)[i]) >= DIGIT_0 && uint(bytes(name)[i]) <= DIGIT_9) {\n        continue;\n      } else if (uint(bytes(name)[i]) >= LETTER_a && uint(bytes(name)[i]) <= LETTER_z) {\n        continue;\n      } else {\n        return false;\n      }\n    }\n    return true;\n  }\n\n  /// @dev Returns boolean whether the input string has a length\n  /// @param value The string to validate.\n  function validateStringIdentifier(string value)\n    public\n    pure\n    returns (bool)\n  {\n    if (bytes(value).length == 0) {\n      return false;\n    }\n    return true;\n  }\n}", "installPath": "./ReleaseValidator.sol", "type": "solidity"}}, "version": "2.0.0"}