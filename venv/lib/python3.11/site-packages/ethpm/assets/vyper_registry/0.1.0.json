{"contract_types": {"registry": {"abi": [{"anonymous": false, "inputs": [{"indexed": true, "name": "_package", "type": "bytes32"}, {"indexed": false, "name": "_version", "type": "bytes32"}, {"indexed": false, "name": "_uri", "type": "bytes"}], "name": "VersionRelease", "type": "event"}, {"constant": false, "inputs": [], "name": "__init__", "outputs": [], "payable": false, "type": "constructor"}, {"constant": false, "gas": 35627, "inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "transferOwner", "outputs": [], "payable": false, "type": "function"}, {"constant": false, "gas": 1954, "inputs": [{"name": "packageName", "type": "bytes32"}, {"name": "version", "type": "bytes32"}], "name": "getReleaseId", "outputs": [{"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": false, "gas": 1509, "inputs": [{"name": "packageName", "type": "bytes32"}, {"name": "version", "type": "bytes32"}], "name": "generateReleaseId", "outputs": [{"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": false, "gas": 1259, "inputs": [{"name": "packageId", "type": "bytes32"}], "name": "getPackageName", "outputs": [{"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": false, "gas": 1865, "inputs": [{"name": "packageName", "type": "bytes32"}], "name": "getPackageData", "outputs": [{"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "int128"}], "payable": false, "type": "function"}, {"constant": false, "gas": 633, "inputs": [], "name": "numPackageIds", "outputs": [{"name": "out", "type": "int128"}], "payable": false, "type": "function"}, {"constant": false, "gas": 1436, "inputs": [{"name": "packageName", "type": "bytes32"}], "name": "numReleaseIds", "outputs": [{"name": "out", "type": "int128"}], "payable": false, "type": "function"}, {"constant": false, "gas": 4764, "inputs": [{"name": "offset", "type": "uint256"}, {"name": "length", "type": "uint256"}], "name": "getAllPackageIds", "outputs": [{"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": false, "gas": 15167, "inputs": [{"name": "packageName", "type": "bytes32"}, {"name": "offset", "type": "uint256"}, {"name": "length", "type": "uint256"}], "name": "getAllReleaseIds", "outputs": [{"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": false, "gas": 15331, "inputs": [{"name": "releaseId", "type": "bytes32"}], "name": "getReleaseData", "outputs": [{"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes"}], "payable": false, "type": "function"}, {"constant": false, "gas": 1865737, "inputs": [{"name": "packageName", "type": "bytes32"}, {"name": "version", "type": "bytes32"}, {"name": "manifestURI", "type": "bytes"}], "name": "release", "outputs": [], "payable": false, "type": "function"}, {"constant": true, "gas": 873, "inputs": [], "name": "owner", "outputs": [{"name": "out", "type": "address"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1108, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "packages__exists", "outputs": [{"name": "out", "type": "bool"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1144, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "packages__createdAt", "outputs": [{"name": "out", "type": "uint256", "unit": "sec"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1174, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "packages__updatedAt", "outputs": [{"name": "out", "type": "uint256", "unit": "sec"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1204, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "packages__name", "outputs": [{"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1234, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "packages__releaseCount", "outputs": [{"name": "out", "type": "int128"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1258, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "releases__exists", "outputs": [{"name": "out", "type": "bool"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1294, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "releases__createdAt", "outputs": [{"name": "out", "type": "uint256", "unit": "sec"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1324, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "releases__packageId", "outputs": [{"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1354, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "releases__version", "outputs": [{"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": true, "gas": 163068, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "releases__uri", "outputs": [{"name": "out", "type": "bytes"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1203, "inputs": [], "name": "packageCount", "outputs": [{"name": "out", "type": "int128"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1233, "inputs": [], "name": "releaseCount", "outputs": [{"name": "out", "type": "int128"}], "payable": false, "type": "function"}], "deployment_bytecode": {"bytecode": "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"}, "runtime_bytecode": {"bytecode": "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"}}, "registry_with_delete": {"abi": [{"anonymous": false, "inputs": [{"indexed": true, "name": "_package", "type": "bytes32"}, {"indexed": false, "name": "_version", "type": "bytes32"}, {"indexed": false, "name": "_uri", "type": "bytes"}], "name": "VersionRelease", "type": "event"}, {"constant": false, "inputs": [], "name": "__init__", "outputs": [], "payable": false, "type": "constructor"}, {"constant": false, "gas": 35627, "inputs": [{"name": "new<PERSON>wner", "type": "address"}], "name": "transferOwner", "outputs": [], "payable": false, "type": "function"}, {"constant": false, "gas": 1954, "inputs": [{"name": "packageName", "type": "bytes32"}, {"name": "version", "type": "bytes32"}], "name": "getReleaseId", "outputs": [{"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": false, "gas": 1509, "inputs": [{"name": "packageName", "type": "bytes32"}, {"name": "version", "type": "bytes32"}], "name": "generateReleaseId", "outputs": [{"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": false, "gas": 1259, "inputs": [{"name": "packageId", "type": "bytes32"}], "name": "getPackageName", "outputs": [{"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": false, "gas": 1865, "inputs": [{"name": "packageName", "type": "bytes32"}], "name": "getPackageData", "outputs": [{"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "int128"}], "payable": false, "type": "function"}, {"constant": false, "gas": 633, "inputs": [], "name": "numPackageIds", "outputs": [{"name": "out", "type": "int128"}], "payable": false, "type": "function"}, {"constant": false, "gas": 1436, "inputs": [{"name": "packageName", "type": "bytes32"}], "name": "numReleaseIds", "outputs": [{"name": "out", "type": "int128"}], "payable": false, "type": "function"}, {"constant": false, "gas": 7252, "inputs": [{"name": "offset", "type": "uint256"}, {"name": "length", "type": "uint256"}], "name": "getAllPackageIds", "outputs": [{"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": false, "gas": 17655, "inputs": [{"name": "packageName", "type": "bytes32"}, {"name": "offset", "type": "uint256"}, {"name": "length", "type": "uint256"}], "name": "getAllReleaseIds", "outputs": [{"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": false, "gas": 15331, "inputs": [{"name": "releaseId", "type": "bytes32"}], "name": "getReleaseData", "outputs": [{"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes32"}, {"name": "out", "type": "bytes"}], "payable": false, "type": "function"}, {"constant": false, "gas": 1937386, "inputs": [{"name": "packageName", "type": "bytes32"}, {"name": "version", "type": "bytes32"}, {"name": "manifestURI", "type": "bytes"}], "name": "release", "outputs": [], "payable": false, "type": "function"}, {"constant": false, "gas": 134511, "inputs": [{"name": "releaseId", "type": "bytes32"}], "name": "deleteReleaseId", "outputs": [], "payable": false, "type": "function"}, {"constant": true, "gas": 903, "inputs": [], "name": "owner", "outputs": [{"name": "out", "type": "address"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1138, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "packages__exists", "outputs": [{"name": "out", "type": "bool"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1174, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "packages__createdAt", "outputs": [{"name": "out", "type": "uint256", "unit": "sec"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1204, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "packages__updatedAt", "outputs": [{"name": "out", "type": "uint256", "unit": "sec"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1234, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "packages__name", "outputs": [{"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1264, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "packages__releaseCount", "outputs": [{"name": "out", "type": "int128"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1288, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "releases__exists", "outputs": [{"name": "out", "type": "bool"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1324, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "releases__createdAt", "outputs": [{"name": "out", "type": "uint256", "unit": "sec"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1354, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "releases__packageId", "outputs": [{"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1384, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "releases__version", "outputs": [{"name": "out", "type": "bytes32"}], "payable": false, "type": "function"}, {"constant": true, "gas": 163098, "inputs": [{"name": "arg0", "type": "bytes32"}], "name": "releases__uri", "outputs": [{"name": "out", "type": "bytes"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1233, "inputs": [], "name": "totalPackageCount", "outputs": [{"name": "out", "type": "int128"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1263, "inputs": [], "name": "activePackageCount", "outputs": [{"name": "out", "type": "int128"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1293, "inputs": [], "name": "totalReleaseCount", "outputs": [{"name": "out", "type": "int128"}], "payable": false, "type": "function"}, {"constant": true, "gas": 1323, "inputs": [], "name": "activeReleaseCount", "outputs": [{"name": "out", "type": "int128"}], "payable": false, "type": "function"}], "deployment_bytecode": {"bytecode": "0x600035601c52740100000000000000000000000000000000000000006020526f7fffffffffffffffffffffffffffffff6040527fffffffffffffffffffffffffffffffff8000000000000000000000000000000060605274012a05f1fffffffffffffffffffffffffdabf41c006080527ffffffffffffffffffffffffed5fa0e000000000000000000000000000000000060a052341561009e57600080fd5b33600055611cf556600035601c52740100000000000000000000000000000000000000006020526f7fffffffffffffffffffffffffffffff6040527fffffffffffffffffffffffffffffffff8000000000000000000000000000000060605274012a05f1fffffffffffffffffffffffffdabf41c006080527ffffffffffffffffffffffffed5fa0e000000000000000000000000000000000060a052634fb2e45d60005114156100dd57602060046101403734156100b457600080fd5b60043560205181106100c557600080fd5b5033600054146100d457600080fd5b61014051600055005b632244dda0600051141561019357604060046101403734156100fe57600080fd5b6000610140516020826102000101526020810190506101605160208261020001015260208101905080610200526102009050805160200180610180828460006004600a8704601201f161015057600080fd5b50506101808051602082012090506102805260026102805160e05260c052604060c02060c052602060c0205461018557600080fd5b6102805160005260206000f3005b6336739429600051141561022657604060046101403734156101b457600080fd5b6000610140516020826102000101526020810190506101605160208261020001015260208101905080610200526102009050805160200180610180828460006004600a8704601201f161020657600080fd5b5050610180805160208201209050610280526102805160005260206000f3005b6306fe1fd76000511415610291576020600461014037341561024757600080fd5b60016101405160e05260c052604060c02060c052602060c0205461026a57600080fd5b600360016101405160e05260c052604060c02060c052602060c020015460005260206000f3005b63b4d6d4c7600051141561034357602060046101403734156102b257600080fd5b6101405160c052602060c0206101605260016101605160e05260c052604060c02060c052602060c020546102e557600080fd5b6060610180526101a0600360016101605160e05260c052604060c02060c052602060c02001548152610160518160200152600460016101605160e05260c052604060c02060c052602060c0200154816040015250610180516101a0f3005b63a54fb685600051141561036957341561035c57600080fd5b60055460005260206000f3005b63c18466cb60005114156103e4576020600461014037341561038a57600080fd5b6101405160c052602060c0206101605260016101605160e05260c052604060c02060c052602060c020546103bd57600080fd5b600460016101605160e05260c052604060c02060c052602060c020015460005260206000f3005b6343212cf160005114156105e8576040600461014037341561040557600080fd5b6101405160405181111561041857600080fd5b610180526101605160405181111561042f57600080fd5b6101a05260056101a0511461044357600080fd5b60055461018051131561045557600080fd5b610260610180516004818352015b600554610260511315156105365760086102605160e05260c052604060c020546102805260016102805160e05260c052604060c02060c052602060c02054156104ee57610280516101c0606051610180516102605103806040519013156104c957600080fd5b80919012156104d757600080fd5b600581106104e457600080fd5b6020020152610531565b600a546101c06060516101805161026051038060405190131561051057600080fd5b809190121561051e57600080fd5b6005811061052b57600080fd5b60200201525b610579565b600a546101c06060516101805161026051038060405190131561055857600080fd5b809190121561056657600080fd5b6005811061057357600080fd5b60200201525b5b8151600101808352811415610463575b505060a06102a0526102c06101c06000602002015181526101c06001602002015181602001526101c06002602002015181604001526101c06003602002015181606001526101c0600460200201518160800152506102a0516102c0f3005b60001561067a575b610180526101405261016052610160516101a0526000610140516020826102400101526020810190506101a051602082610240010152602081019050806102405261024090508051602001806101c0828460006004600a8704601201f161065657600080fd5b50506101c08051602082012090506102c0526102c051600052600051610180515650005b63fa6bf4da60005114156109a7576060600461014037341561069b57600080fd5b610160516040518111156106ae57600080fd5b6101a052610180516040518111156106c557600080fd5b6101c05260056101c051146106d957600080fd5b6101405160c052602060c0206101e05260016101e05160e05260c052604060c02060c052602060c0205461070c57600080fd5b600460016101e05160e05260c052604060c02060c052602060c02001546101a051131561073857600080fd5b6102a06101a0516004818352015b600460016101e05160e05260c052604060c02060c052602060c02001546102a0511315156108f5576101405161016051610180516101a0516101c0516101e05161020051610220516102405161026051610280516102a0516102c05163c658b60e6102e0526101e0516103005260605160016102a05101806040519013156107cd57600080fd5b80919012156107db57600080fd5b610320526103205161030051600658016105f0565b610380526102c0526102a05261028052610260526102405261022052610200526101e0526101c0526101a052610180526101605261014052610380516102c05260036102c05160e05260c052604060c020546103a05260026103a05160e05260c052604060c02060c052602060c02054156108ad576103a0516102006060516101a0516102a051038060405190131561088857600080fd5b809190121561089657600080fd5b600581106108a357600080fd5b60200201526108f0565b600a546102006060516101a0516102a05103806040519013156108cf57600080fd5b80919012156108dd57600080fd5b600581106108ea57600080fd5b60200201525b610938565b600a546102006060516101a0516102a051038060405190131561091757600080fd5b809190121561092557600080fd5b6005811061093257600080fd5b60200201525b5b8151600101808352811415610746575b505060a06103c0526103e0610200600060200201518152610200600160200201518160200152610200600260200201518160400152610200600360200201518160600152610200600460200201518160800152506103c0516103e0f3005b634c4aea876000511415610b0257602060046101403734156109c857600080fd5b60026101405160e05260c052604060c02060c052602060c020546109eb57600080fd5b600260026101405160e05260c052604060c02060c052602060c0200154610160526060610180526101a0600360016101605160e05260c052604060c02060c052602060c02001548152600360026101405160e05260c052604060c02060c052602060c02001548160200152610180518160400152600460026101405160e05260c052604060c02060c052602060c020018060c052602060c020610180518301602082540161012060006021818352015b82610120516020021115610aae57610ad0565b61012051850154610120516020028501525b8151600101808352811415610a9b575b5050505050506101805160206101805183015160206001820306601f820103905001016101805250610180516101a0f3005b600015610f67575b6101e0526101405261016052610180526101a0526101c052610200526000610640525b6102005160206001820306601f820103905061064051101515610b4f57610b68565b6106405161022001526106405160200161064052610b2d565b600050600160026101405160e05260c052604060c02060c052602060c020015415610b9257600080fd5b60026101405160e05260c052604060c02060c052602060c02060018155426001820155610160516002820155610180516003820155610200806004830160c052602060c020602082510161012060006021818352015b82610120516020021115610bfb57610c1d565b61012051602002850151610120518501555b8151600101808352811415610be8575b50505050505050600460016101605160e05260c052604060c02060c052602060c02001606051600182540180604051901315610c5857600080fd5b8091901215610c6657600080fd5b81555061014051600960065460e05260c052604060c020556006606051600182540180604051901315610c9857600080fd5b8091901215610ca657600080fd5b8155506007606051600182540180604051901315610cc357600080fd5b8091901215610cd157600080fd5b8155506101405161016051610180516101a0516101c0516101e05161020051610220516102405161026051610280516102a0516102c0516102e05161030051610320516103405161036051610380516103a0516103c0516103e05161040051610420516104405161046051610480516104a0516104c0516104e05161050051610520516105405161056051610580516105a0516105c0516105e0516106005161062051610640516106605163c658b60e61068052610160516106a052600460016101605160e05260c052604060c02060c052602060c02001546106c0526106c0516106a051600658016105f0565b61072052610660526106405261062052610600526105e0526105c0526105a05261058052610560526105405261052052610500526104e0526104c0526104a05261048052610460526104405261042052610400526103e0526103c0526103a05261038052610360526103405261032052610300526102e0526102c0526102a05261028052610260526102405261022052610200526101e0526101c0526101a05261018052610160526101405261072051610660526101405160036106605160e05260c052604060c02055610180516107a052604061076052610760516107c052610200805160200180610760516107a001828460006004600a8704601201f1610ec757600080fd5b5050610760516107a0016107408151610400818352015b61040061074051101515610ef157610f0e565b6000610740516020850101535b8151600101808352811415610ede575b5050506020610760516107a0015160206001820306601f8201039050610760510101610760526101c0517f350213af825a48db104cd58c2128537a8ece5b4f20ecb0de0680781cfd906582610760516107a0a26101e051565b63142f178c600051141561160a5760606004610140373415610f8857600080fd5b6104086044356004016101a0376103e8604435600401351115610faa57600080fd5b600a54610140511415610fbc57600080fd5b600a54610160511415610fce57600080fd5b60006101a05113610fde57600080fd5b3360005414610fec57600080fd5b6101405160c052602060c0206105e05260206106c060446336739429610620526101405161064052610160516106605261063c6000305af161102d57600080fd5b6106c05161060052600160016105e05160e05260c052604060c02060c052602060c0205414156113045760016105e05160e05260c052604060c02060c052602060c02060018155600160016105e05160e05260c052604060c02060c052602060c02001546001820155426002820155610140516003820155600460016105e05160e05260c052604060c02060c052602060c02001546004820155506101405161016051610180516101a0516101c0516101e05161020051610220516102405161026051610280516102a0516102c0516102e05161030051610320516103405161036051610380516103a0516103c0516103e05161040051610420516104405161046051610480516104a0516104c0516104e05161050051610520516105405161056051610580516105a0516105c0516105e05161060051610620516106405161066051610680516106a0516106c05160a063836ee66e610c205261060051610c40526105e051610c605261016051610c805280610ca0526101a08080516020018084610c4001828460006004600a8704601201f16111ca57600080fd5b50508051820160206001820306601f820103905060200191505061014051610cc05250611100611140525b610ce06111405110156112075761122b565b6000611140515114151561121b5761114051515b60206111405103611140526111f5565b610cc051610ca051610c8051610c6051610c405160065801610b0a565b6106c0526106a05261068052610660526106405261062052610600526105e0526105c0526105a05261058052610560526105405261052052610500526104e0526104c0526104a05261048052610460526104405261042052610400526103e0526103c0526103a05261038052610360526103405261032052610300526102e0526102c0526102a05261028052610260526102405261022052610200526101e0526101c0526101a052610180526101605261014052600050611608565b600160016105e05160e05260c052604060c02060c052602060c02001541561132b57600080fd5b60016105e05160e05260c052604060c02060c052602060c0206001815542600182015542600282015561014051600382015560006004820155506105e051600860045460e05260c052604060c02055600460605160018254018060405190131561139457600080fd5b80919012156113a257600080fd5b81555060056060516001825401806040519013156113bf57600080fd5b80919012156113cd57600080fd5b8155506101405161016051610180516101a0516101c0516101e05161020051610220516102405161026051610280516102a0516102c0516102e05161030051610320516103405161036051610380516103a0516103c0516103e05161040051610420516104405161046051610480516104a0516104c0516104e05161050051610520516105405161056051610580516105a0516105c0516105e05161060051610620516106405161066051610680516106a0516106c05160a063836ee66e6106e05261060051610700526105e05161072052610160516107405280610760526101a0808051602001808461070001828460006004600a8704601201f16114d257600080fd5b50508051820160206001820306601f8201039050602001915050610140516107805250610bc0610c00525b6107a0610c0051101561150f57611533565b6000610c00515114151561152357610c0051515b6020610c005103610c00526114fd565b610780516107605161074051610720516107005160065801610b0a565b6106c0526106a05261068052610660526106405261062052610600526105e0526105c0526105a05261058052610560526105405261052052610500526104e0526104c0526104a05261048052610460526104405261042052610400526103e0526103c0526103a05261038052610360526103405261032052610300526102e0526102c0526102a05261028052610260526102405261022052610200526101e0526101c0526101a0526101805261016052610140526000505b005b637cfd7cd26000511415611813576020600461014037341561162b57600080fd5b336000541461163957600080fd5b60026101405160e05260c052604060c02060c052602060c0205461165c57600080fd5b600260026101405160e05260c052604060c02060c052602060c02001546101605260016101605160e05260c052604060c02060c052602060c020546116a057600080fd5b6000600460016101605160e05260c052604060c02060c052602060c0200154136116c957600080fd5b6001600460016101605160e05260c052604060c02060c052602060c02001541415611784576000600460016101605160e05260c052604060c02060c052602060c0200155600060016101605160e05260c052604060c02060c052602060c02055600560605160018254038060405190131561174357600080fd5b809190121561175157600080fd5b815550600760605160018254038060405190131561176e57600080fd5b809190121561177c57600080fd5b8155506117f5565b600460016101605160e05260c052604060c02060c052602060c020016060516001825403806040519013156117b857600080fd5b80919012156117c657600080fd5b81555060076060516001825403806040519013156117e357600080fd5b80919012156117f157600080fd5b8155505b600060026101405160e05260c052604060c02060c052602060c02055005b638da5cb5b600051141561183957341561182c57600080fd5b60005460005260206000f3005b63c7f8a044600051141561187e576020600461014037341561185a57600080fd5b60016101405160e05260c052604060c02060c052602060c0205460005260206000f3005b636366b2f360005114156118c6576020600461014037341561189f57600080fd5b600160016101405160e05260c052604060c02060c052602060c020015460005260206000f3005b63c40509d8600051141561190e57602060046101403734156118e757600080fd5b600260016101405160e05260c052604060c02060c052602060c020015460005260206000f3005b63cb4e26ec6000511415611956576020600461014037341561192f57600080fd5b600360016101405160e05260c052604060c02060c052602060c020015460005260206000f3005b632fa39cad600051141561199e576020600461014037341561197757600080fd5b600460016101405160e05260c052604060c02060c052602060c020015460005260206000f3005b63c67790b860005114156119e357602060046101403734156119bf57600080fd5b60026101405160e05260c052604060c02060c052602060c0205460005260206000f3005b63f1849be46000511415611a2b5760206004610140373415611a0457600080fd5b600160026101405160e05260c052604060c02060c052602060c020015460005260206000f3005b63138b7ae06000511415611a735760206004610140373415611a4c57600080fd5b600260026101405160e05260c052604060c02060c052602060c020015460005260206000f3005b632ee2bcc16000511415611abb5760206004610140373415611a9457600080fd5b600360026101405160e05260c052604060c02060c052602060c020015460005260206000f3005b635036899c6000511415611bb05760206004610140373415611adc57600080fd5b600460026101405160e05260c052604060c02060c052602060c020018060c052602060c0206101a0602082540161012060006021818352015b82610120516020021115611b2857611b4a565b61012051850154610120516020028501525b8151600101808352811415611b15575b5050505050506105e06101a0516103e8818352015b6103e86105e0511115611b7157611b8d565b60006105e0516101c001535b8151600101808352811415611b5f575b505060206101805260406101a0510160206001820306601f8201039050610180f3005b63a23b48786000511415611bd6573415611bc957600080fd5b60045460005260206000f3005b633062afe46000511415611bfc573415611bef57600080fd5b60055460005260206000f3005b630350ebbe6000511415611c22573415611c1557600080fd5b60065460005260206000f3005b6349504c006000511415611c48573415611c3b57600080fd5b60075460005260206000f3005b60006000fd5b6100a7611cf5036100a76000396100a7611cf5036000f3"}, "runtime_bytecode": {"bytecode": "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"}}}, "deployments": {"blockchain://5c4f59274a0b01d050ac6f5c1e9c11b7975c0fdc82f8daa75925cd97b99cbacf/block/6fd5ae3341d910d1b9c3d069090eb578d8d7ad6eb6f5f90f8c706663a6863706": {"registry": {"address": "******************************************", "block": "0x6fd5ae3341d910d1b9c3d069090eb578d8d7ad6eb6f5f90f8c706663a6863706", "contract_type": "registry", "transaction": "0x2fe41fd7791b7bc884deef0c1b5ec99bc922fab955c543480ed628c59e4f3409"}}}, "manifest_version": "2", "meta": {"authors": ["<PERSON>"], "description": "[WARNING: This package is unaudited and unmaintained. It should not be used for production use cases unless the contents are independently verified.] Vyper reference implementations of ERC1319 (EthPM Package Registry). registry.vy: Does *not* allow for releases to be deleted after they're published. Package created Feb 12, 2019.", "keywords": ["ethpm", "ERC1319", "registry", "vyper", "unaudited package"], "license": "MIT", "links": {"website": "https://github.com/ethereum/EIPs/issues/1319"}}, "package_name": "vyper-registry", "sources": {"./registry.vy": "# Vyper Reference Implementation of ERC1319\n\n# Structs\nstruct Package:\n\texists: bool\n\tcreatedAt: timestamp\n\tupdatedAt: timestamp\n\tname: bytes32\n\treleaseCount: int128\n\nstruct Release:\n\texists: bool\n\tcreatedAt: timestamp\n\tpackageId: bytes32\n\tversion: bytes32\n\turi: bytes[1000]\n\n\t\n# Events\nVersionRelease: event({_package: indexed(bytes32), _version: bytes32, _uri: bytes[1000]})\n\nowner: public(address)\n\n# Package Data: (package_id => value)\npackages: public(map(bytes32, Package))\n\n#  Release Data: (release_id => value)\nreleases: public(map(bytes32, Release))\n\n\n# package_id#release_count => release_id\npackageReleaseIndex: map(bytes32, bytes32)\n# Total number of packages in registry\npackageCount: public(int128)\n# Total number of releases in registry\nreleaseCount: public(int128)\n# Total package number (int128) => package_id (bytes32)\npackageIds: map(int128, bytes32)\n# Total release number (int128) => release_id (bytes32)\nreleaseIds: map(int128, bytes32)\n\nEMPTY_BYTES: bytes32\n\n\n@public\ndef __init__():\n    self.owner = msg.sender\n\n\n@public\ndef transferOwner(newOwner: address):\n    assert self.owner == msg.sender\n    self.owner = newOwner\n\n\n@public\ndef getReleaseId(packageName: bytes32, version: bytes32) -> bytes32:\n    releaseConcat: bytes[64] = concat(packageName, version)\n    releaseId: bytes32 = sha3(releaseConcat)\n    assert self.releases[releaseId].exists\n    return releaseId\n\n\n@public\ndef generateReleaseId(packageName: bytes32, version: bytes32) -> bytes32:\n    releaseConcat: bytes[64] = concat(packageName, version)\n    releaseId: bytes32 = sha3(releaseConcat)\n    return releaseId\n\n\n@public\ndef getPackageName(packageId: bytes32) -> bytes32:\n    assert self.packages[packageId].exists\n    return self.packages[packageId].name\n\n\n@public\ndef getPackageData(packageName: bytes32) -> (bytes32, bytes32, int128):\n    packageId: bytes32 = sha3(packageName)\n    assert self.packages[packageId].exists\n    return (\n        self.packages[packageId].name,\n        packageId,\n        self.packages[packageId].releaseCount,\n    )\n\n\n@public\ndef numPackageIds() -> int128:\n    return self.packageCount\n\n\n@public\ndef numReleaseIds(packageName: bytes32) -> int128:\n    packageId: bytes32 = sha3(packageName)\n    assert self.packages[packageId].exists\n    return self.packages[packageId].releaseCount\n\n\n@public\ndef getAllPackageIds(\n    offset: uint256, length: uint256\n) -> (bytes32, bytes32, bytes32, bytes32, bytes32):\n    offset_int: int128 = convert(offset, int128)\n    length_int: int128 = convert(length, int128)\n    assert length_int == 5\n    assert offset_int <= self.packageCount\n    ids: bytes32[5]\n    for idx in range(offset_int, offset_int + 4):\n        if idx <= self.packageCount:\n            packageId: bytes32 = self.packageIds[idx]\n            ids[(idx - offset_int)] = packageId\n        else:\n            ids[(idx - offset_int)] = self.EMPTY_BYTES\n    return (ids[0], ids[1], ids[2], ids[3], ids[4])\n\n\n@private\ndef generatePackageReleaseId(packageId: bytes32, count: int128) -> bytes32:\n    countBytes: bytes32 = convert(count, bytes32)\n    packageReleaseTag: bytes[64] = concat(packageId, countBytes)\n    packageReleaseId: bytes32 = sha3(packageReleaseTag)\n    return packageReleaseId\n\n\n@public\ndef getAllReleaseIds(\n    packageName: bytes32, offset: uint256, length: uint256\n) -> (bytes32, bytes32, bytes32, bytes32, bytes32):\n    offset_int: int128 = convert(offset, int128)\n    length_int: int128 = convert(length, int128)\n    assert length_int == 5\n    packageId: bytes32 = sha3(packageName)\n    assert self.packages[packageId].exists\n    assert offset_int <= self.packages[packageId].releaseCount\n    ids: bytes32[5]\n    for idx in range(offset_int, offset_int + 4):\n        if idx <= self.packages[packageId].releaseCount:\n            packageReleaseId: bytes32 = self.generatePackageReleaseId(\n                packageId, (idx + 1)\n            )\n            releaseId: bytes32 = self.packageReleaseIndex[packageReleaseId]\n            ids[(idx - offset_int)] = releaseId\n        else:\n            ids[(idx - offset_int)] = self.EMPTY_BYTES\n    return (ids[0], ids[1], ids[2], ids[3], ids[4])\n\n\n@public\ndef getReleaseData(releaseId: bytes32) -> (bytes32, bytes32, bytes[1000]):\n    assert self.releases[releaseId].exists\n    packageId: bytes32 = self.releases[releaseId].packageId\n    return (\n        self.packages[packageId].name,\n        self.releases[releaseId].version,\n        self.releases[releaseId].uri,\n    )\n\n\n@private\ndef cutRelease(\n    releaseId: bytes32,\n    packageId: bytes32,\n    version: bytes32,\n    uri: bytes[1000],\n    name: bytes32,\n):\n    self.releases[releaseId] = Release({\n        exists: True,\n        createdAt: block.timestamp,\n        packageId: packageId,\n        version: version,\n        uri: uri,\n    })\n    self.packages[packageId].releaseCount += 1\n    self.releaseIds[self.releaseCount] = releaseId\n    self.releaseCount += 1\n    packageReleaseId: bytes32 = self.generatePackageReleaseId(\n        packageId, self.packages[packageId].releaseCount\n    )\n    self.packageReleaseIndex[packageReleaseId] = releaseId\n    log.VersionRelease(name, version, uri)\n\n\n@public\ndef release(packageName: bytes32, version: bytes32, manifestURI: bytes[1000]):\n    assert packageName != self.EMPTY_BYTES\n    assert version != self.EMPTY_BYTES\n    assert len(manifestURI) > 0\n    assert self.owner == msg.sender\n\n\n    packageId: bytes32 = sha3(packageName)\n    releaseId: bytes32 = self.generateReleaseId(packageName, version)\n\n    if self.packages[packageId].exists == True:\n        self.packages[packageId] = Package({\n            exists: True,\n            createdAt: self.packages[packageId].createdAt,\n            updatedAt: block.timestamp,\n            name: packageName,\n            releaseCount: self.packages[packageId].releaseCount,\n        })\n        assert self.releases[releaseId].exists == False\n        self.cutRelease(releaseId, packageId, version, manifestURI, packageName)\n    else:\n        self.packages[packageId] = Package({\n            exists: True,\n            createdAt: block.timestamp,\n            updatedAt: block.timestamp,\n            name: packageName,\n            releaseCount: 0,\n        })\n        self.packageIds[self.packageCount] = packageId\n        self.packageCount += 1\n        self.cutRelease(releaseId, packageId, version, manifestURI, packageName)", "./registry_with_delete.vy": "# Vyper Reference Implementation of ERC1319 - with delete\n# Once all the releaseIds of a package are deleted - package namespace is permanently unavailable\n\n# Structs\nstruct Package:\n\texists: bool\n\tcreatedAt: timestamp\n\tupdatedAt: timestamp\n\tname: bytes32\n\treleaseCount: int128\n\nstruct Release:\n\texists: bool\n\tcreatedAt: timestamp\n\tpackageId: bytes32\n\tversion: bytes32\n\turi: bytes[1000]\n\n# Events\nVersionRelease: event({_package: indexed(bytes32), _version: bytes32, _uri: bytes[1000]})\n\nowner: public(address)\n\n# Package Data: (package_id => value)\npackages: public(map(bytes32, Package))\n\n#  Release Data: (release_id => value)\nreleases: public(map(bytes32, Release))\n\n# package_id#release_count => release_id\npackageReleaseIndex: map(bytes32, bytes32)\n# Total number of packages in registry\ntotalPackageCount: public(int128)\nactivePackageCount: public(int128)\n# Total number of releases in registry\ntotalReleaseCount: public(int128)\nactiveReleaseCount: public(int128)\n# Total package number (int128) => package_id (bytes32)\npackageIds: map(int128, bytes32)\n# Total release number (int128) => release_id (bytes32)\nreleaseIds: map(int128, bytes32)\n\nEMPTY_BYTES: bytes32\n\n\n@public\ndef __init__():\n    self.owner = msg.sender\n\n\n@public\ndef transferOwner(newOwner: address):\n    assert self.owner == msg.sender\n    self.owner = newOwner\n\n\n@public\ndef getReleaseId(packageName: bytes32, version: bytes32) -> bytes32:\n    releaseConcat: bytes[64] = concat(packageName, version)\n    releaseId: bytes32 = sha3(releaseConcat)\n    assert self.releases[releaseId].exists\n    return releaseId\n\n\n@public\ndef generateReleaseId(packageName: bytes32, version: bytes32) -> bytes32:\n    releaseConcat: bytes[64] = concat(packageName, version)\n    releaseId: bytes32 = sha3(releaseConcat)\n    return releaseId\n\n\n@public\ndef getPackageName(packageId: bytes32) -> bytes32:\n    assert self.packages[packageId].exists\n    return self.packages[packageId].name\n\n\n@public\ndef getPackageData(packageName: bytes32) -> (bytes32, bytes32, int128):\n    packageId: bytes32 = sha3(packageName)\n    assert self.packages[packageId].exists\n    return (\n        self.packages[packageId].name,\n        packageId,\n        self.packages[packageId].releaseCount,\n    )\n\n\n@public\ndef numPackageIds() -> int128:\n    return self.activePackageCount\n\n\n@public\ndef numReleaseIds(packageName: bytes32) -> int128:\n    packageId: bytes32 = sha3(packageName)\n    assert self.packages[packageId].exists\n    return self.packages[packageId].releaseCount\n\n\n@public\ndef getAllPackageIds(\n    offset: uint256, length: uint256\n) -> (bytes32, bytes32, bytes32, bytes32, bytes32):\n    offset_int: int128 = convert(offset, int128)\n    length_int: int128 = convert(length, int128)\n    assert length_int == 5\n    assert offset_int <= self.activePackageCount\n    ids: bytes32[5]\n    for idx in range(offset_int, offset_int + 4):\n        if idx <= self.activePackageCount:\n            packageId: bytes32 = self.packageIds[idx]\n            if self.packages[packageId].exists:\n                ids[(idx - offset_int)] = packageId\n            else:\n                ids[(idx - offset_int)] = self.EMPTY_BYTES\n        else:\n            ids[(idx - offset_int)] = self.EMPTY_BYTES\n    return (ids[0], ids[1], ids[2], ids[3], ids[4])\n\n\n@private\ndef generatePackageReleaseId(packageId: bytes32, count: int128) -> bytes32:\n    countBytes: bytes32 = convert(count, bytes32)\n    packageReleaseTag: bytes[64] = concat(packageId, countBytes)\n    packageReleaseId: bytes32 = sha3(packageReleaseTag)\n    return packageReleaseId\n\n\n@public\ndef getAllReleaseIds(\n    packageName: bytes32, offset: uint256, length: uint256\n) -> (bytes32, bytes32, bytes32, bytes32, bytes32):\n    offset_int: int128 = convert(offset, int128)\n    length_int: int128 = convert(length, int128)\n    assert length_int == 5\n    packageId: bytes32 = sha3(packageName)\n    assert self.packages[packageId].exists\n    assert offset_int <= self.packages[packageId].releaseCount\n    ids: bytes32[5]\n    for idx in range(offset_int, offset_int + 4):\n        if idx <= self.packages[packageId].releaseCount:\n            packageReleaseId: bytes32 = self.generatePackageReleaseId(\n                packageId, (idx + 1)\n            )\n            releaseId: bytes32 = self.packageReleaseIndex[packageReleaseId]\n            if self.releases[releaseId].exists:\n                ids[(idx - offset_int)] = releaseId\n            else:\n                ids[(idx - offset_int)] = self.EMPTY_BYTES\n        else:\n            ids[(idx - offset_int)] = self.EMPTY_BYTES\n    return (ids[0], ids[1], ids[2], ids[3], ids[4])\n\n\n@public\ndef getReleaseData(releaseId: bytes32) -> (bytes32, bytes32, bytes[1000]):\n    assert self.releases[releaseId].exists\n    packageId: bytes32 = self.releases[releaseId].packageId\n    return (\n        self.packages[packageId].name,\n        self.releases[releaseId].version,\n        self.releases[releaseId].uri,\n    )\n\n\n@private\ndef cutRelease(\n    releaseId: bytes32,\n    packageId: bytes32,\n    version: bytes32,\n    uri: bytes[1000],\n    name: bytes32,\n):\n    assert self.releases[releaseId].createdAt == 0\n    self.releases[releaseId] = Release({\n        exists: True,\n        createdAt: block.timestamp,\n        packageId: packageId,\n        version: version,\n        uri: uri,\n    })\n    self.packages[packageId].releaseCount += 1\n    self.releaseIds[self.totalReleaseCount] = releaseId\n    self.totalReleaseCount += 1\n    self.activeReleaseCount += 1\n    packageReleaseId: bytes32 = self.generatePackageReleaseId(\n        packageId, self.packages[packageId].releaseCount\n    )\n    self.packageReleaseIndex[packageReleaseId] = releaseId\n    log.VersionRelease(name, version, uri)\n\n\n@public\ndef release(packageName: bytes32, version: bytes32, manifestURI: bytes[1000]):\n    assert packageName != self.EMPTY_BYTES\n    assert version != self.EMPTY_BYTES\n    assert len(manifestURI) > 0\n    assert self.owner == msg.sender\n\n\n    packageId: bytes32 = sha3(packageName)\n    releaseId: bytes32 = self.generateReleaseId(packageName, version)\n\n    if self.packages[packageId].exists == True:\n        self.packages[packageId] = Package({\n            exists: True,\n            createdAt: self.packages[packageId].createdAt,\n            updatedAt: block.timestamp,\n            name: packageName,\n            releaseCount: self.packages[packageId].releaseCount,\n        })\n        self.cutRelease(releaseId, packageId, version, manifestURI, packageName)\n    else:\n        assert self.packages[packageId].createdAt == 0\n        self.packages[packageId] = Package({\n            exists: True,\n            createdAt: block.timestamp,\n            updatedAt: block.timestamp,\n            name: packageName,\n            releaseCount: 0,\n        })\n        self.packageIds[self.totalPackageCount] = packageId\n        self.totalPackageCount += 1\n        self.activePackageCount += 1\n        self.cutRelease(releaseId, packageId, version, manifestURI, packageName)\n\t\n\n@public\ndef deleteReleaseId(releaseId: bytes32):\n    assert self.owner == msg.sender\n    assert self.releases[releaseId].exists\n    packageId: bytes32 = self.releases[releaseId].packageId\n    assert self.packages[packageId].exists\n    assert self.packages[packageId].releaseCount > 0\n    if self.packages[packageId].releaseCount == 1:\n        self.packages[packageId].releaseCount = 0\n        self.packages[packageId].exists = False\n        self.activePackageCount -= 1\n        self.activeReleaseCount -= 1\n    else:\n        self.packages[packageId].releaseCount -= 1\n        self.activeReleaseCount -= 1\n    self.releases[releaseId].exists = False"}, "version": "0.1.0"}