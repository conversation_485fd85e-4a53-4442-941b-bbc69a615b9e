#
# Arithmetic
#
STOP = "STOP"
ADD = "ADD"
MUL = "MUL"
SUB = "SUB"
DIV = "DIV"
SDIV = "SDIV"
MOD = "MOD"
SMOD = "SMOD"
ADDMOD = "ADDMOD"
MULMOD = "MULMOD"
EXP = "EXP"
SIGNEXTEND = "SIGNEXTEND"
SHL = "SHL"
SHR = "SHR"
SAR = "SAR"
#
# Comparisons
#
LT = "LT"
GT = "GT"
SLT = "SLT"
SGT = "SGT"
EQ = "EQ"
ISZERO = "ISZERO"
AND = "AND"
OR = "OR"
XOR = "XOR"
NOT = "NOT"
BYTE = "BYTE"
#
# Sha3
#
SHA3 = "SHA3"
#
# Environment Information
#
ADDRESS = "ADDRESS"
BALANCE = "BALANCE"
SELFBALANCE = "SELFBALANCE"
ORIGIN = "ORIGIN"
CALLER = "CALLER"
CALLVALUE = "CALLVALUE"
CALLDATALOAD = "CALLDATALOAD"
CALLDATASIZE = "CALLDATASIZE"
CALLDATACOPY = "CALLDATACOPY"
CODESIZE = "CODESIZE"
CODECOPY = "CODECOPY"
GASPRICE = "GASPRICE"
EXTCODESIZE = "EXTCODESIZE"
EXTCODECOPY = "EXTCODECOPY"
EXTCODEHASH = "EXTCODEHASH"
RETURNDATASIZE = "RETURNDATASIZE"
RETURNDATACOPY = "RETURNDATACOPY"
CHAINID = "CHAINID"
#
# Block Information
#
BLOCKHASH = "BLOCKHASH"
COINBASE = "COINBASE"
TIMESTAMP = "TIMESTAMP"
NUMBER = "NUMBER"
DIFFICULTY = "DIFFICULTY"
PREVRANDAO = "PREVRANDAO"
GASLIMIT = "GASLIMIT"
BASEFEE = "BASEFEE"
#
# Stack, Memory, Storage and Flow Operations
#
POP = "POP"
MLOAD = "MLOAD"
MSTORE = "MSTORE"
MSTORE8 = "MSTORE8"
SLOAD = "SLOAD"
SSTORE = "SSTORE"
JUMP = "JUMP"
JUMPI = "JUMPI"
PC = "PC"
MSIZE = "MSIZE"
GAS = "GAS"
JUMPDEST = "JUMPDEST"
REVERT = "REVERT"
#
# Push Operations
#
PUSH0 = "PUSH0"
PUSH1 = "PUSH1"
PUSH2 = "PUSH2"
PUSH3 = "PUSH3"
PUSH4 = "PUSH4"
PUSH5 = "PUSH5"
PUSH6 = "PUSH6"
PUSH7 = "PUSH7"
PUSH8 = "PUSH8"
PUSH9 = "PUSH9"
PUSH10 = "PUSH10"
PUSH11 = "PUSH11"
PUSH12 = "PUSH12"
PUSH13 = "PUSH13"
PUSH14 = "PUSH14"
PUSH15 = "PUSH15"
PUSH16 = "PUSH16"
PUSH17 = "PUSH17"
PUSH18 = "PUSH18"
PUSH19 = "PUSH19"
PUSH20 = "PUSH20"
PUSH21 = "PUSH21"
PUSH22 = "PUSH22"
PUSH23 = "PUSH23"
PUSH24 = "PUSH24"
PUSH25 = "PUSH25"
PUSH26 = "PUSH26"
PUSH27 = "PUSH27"
PUSH28 = "PUSH28"
PUSH29 = "PUSH29"
PUSH30 = "PUSH30"
PUSH31 = "PUSH31"
PUSH32 = "PUSH32"
#
# Duplicate Operations
#
DUP1 = "DUP1"
DUP2 = "DUP2"
DUP3 = "DUP3"
DUP4 = "DUP4"
DUP5 = "DUP5"
DUP6 = "DUP6"
DUP7 = "DUP7"
DUP8 = "DUP8"
DUP9 = "DUP9"
DUP10 = "DUP10"
DUP11 = "DUP11"
DUP12 = "DUP12"
DUP13 = "DUP13"
DUP14 = "DUP14"
DUP15 = "DUP15"
DUP16 = "DUP16"
#
# Exchange Operations
#
SWAP1 = "SWAP1"
SWAP2 = "SWAP2"
SWAP3 = "SWAP3"
SWAP4 = "SWAP4"
SWAP5 = "SWAP5"
SWAP6 = "SWAP6"
SWAP7 = "SWAP7"
SWAP8 = "SWAP8"
SWAP9 = "SWAP9"
SWAP10 = "SWAP10"
SWAP11 = "SWAP11"
SWAP12 = "SWAP12"
SWAP13 = "SWAP13"
SWAP14 = "SWAP14"
SWAP15 = "SWAP15"
SWAP16 = "SWAP16"
#
# Logging
#
LOG0 = "LOG0"
LOG1 = "LOG1"
LOG2 = "LOG2"
LOG3 = "LOG3"
LOG4 = "LOG4"
#
# System
#
CREATE = "CREATE"
CREATE2 = "CREATE2"
CALL = "CALL"
CALLCODE = "CALLCODE"
STATICCALL = "STATICCALL"
RETURN = "RETURN"
DELEGATECALL = "DELEGATECALL"
SELFDESTRUCT = "SELFDESTRUCT"
