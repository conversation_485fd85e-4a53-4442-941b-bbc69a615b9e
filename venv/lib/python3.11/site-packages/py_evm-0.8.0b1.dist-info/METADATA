Metadata-Version: 2.1
Name: py-evm
Version: 0.8.0b1
Summary: Python implementation of the Ethereum Virtual Machine
Home-page: https://github.com/ethereum/py-evm
Author: Ethereum Foundation
Author-email: <EMAIL>
License: MIT
Keywords: ethereum blockchain evm
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: cached-property <2,>=1.5.1
Requires-Dist: eth-bloom >=1.0.3
Requires-Dist: eth-keys <0.5.0,>=0.4.0
Requires-Dist: eth-typing <4.0.0,>=3.3.0
Requires-Dist: eth-utils <3.0.0,>=2.0.0
Requires-Dist: lru-dict >=1.1.6
Requires-Dist: mypy-extensions >=1.0.0
Requires-Dist: py-ecc <7.0.0,>=1.4.7
Requires-Dist: rlp <4,>=3
Requires-Dist: trie <3,>=2.0.0
Provides-Extra: benchmark
Requires-Dist: termcolor <2.0.0,>=1.1.0 ; extra == 'benchmark'
Requires-Dist: web3 <5.0.0,>=4.1.0 ; extra == 'benchmark'
Provides-Extra: dev
Requires-Dist: bumpversion <1,>=0.5.3 ; extra == 'dev'
Requires-Dist: wheel ; extra == 'dev'
Requires-Dist: setuptools >=36.2.0 ; extra == 'dev'
Requires-Dist: idna ==2.7 ; extra == 'dev'
Requires-Dist: requests <3,>=2.20 ; extra == 'dev'
Requires-Dist: tox >=4.0.0 ; extra == 'dev'
Requires-Dist: twine ; extra == 'dev'
Requires-Dist: cached-property <2,>=1.5.1 ; extra == 'dev'
Requires-Dist: eth-bloom >=1.0.3 ; extra == 'dev'
Requires-Dist: eth-keys <0.5.0,>=0.4.0 ; extra == 'dev'
Requires-Dist: eth-typing <4.0.0,>=3.3.0 ; extra == 'dev'
Requires-Dist: eth-utils <3.0.0,>=2.0.0 ; extra == 'dev'
Requires-Dist: lru-dict >=1.1.6 ; extra == 'dev'
Requires-Dist: mypy-extensions >=1.0.0 ; extra == 'dev'
Requires-Dist: py-ecc <7.0.0,>=1.4.7 ; extra == 'dev'
Requires-Dist: rlp <4,>=3 ; extra == 'dev'
Requires-Dist: trie <3,>=2.0.0 ; extra == 'dev'
Requires-Dist: factory-boy ==2.11.1 ; extra == 'dev'
Requires-Dist: hypothesis <6,>=5 ; extra == 'dev'
Requires-Dist: pexpect <5,>=4.6 ; extra == 'dev'
Requires-Dist: pytest <7,>=6.2.4 ; extra == 'dev'
Requires-Dist: pytest-asyncio <0.11,>=0.10.0 ; extra == 'dev'
Requires-Dist: pytest-cov ==2.5.1 ; extra == 'dev'
Requires-Dist: pytest-timeout <3,>=2.0.0 ; extra == 'dev'
Requires-Dist: pytest-watch <5,>=4.1.0 ; extra == 'dev'
Requires-Dist: pytest-xdist >=3.0 ; extra == 'dev'
Requires-Dist: flake8 ==6.0.0 ; extra == 'dev'
Requires-Dist: flake8-bugbear ==23.3.23 ; extra == 'dev'
Requires-Dist: isort >=5.10.1 ; extra == 'dev'
Requires-Dist: mypy ==1.4.0 ; extra == 'dev'
Requires-Dist: pydocstyle >=6.0.0 ; extra == 'dev'
Requires-Dist: black >=23 ; extra == 'dev'
Requires-Dist: types-setuptools ; extra == 'dev'
Requires-Dist: py-evm >=0.2.0-a.14 ; extra == 'dev'
Requires-Dist: Sphinx <2,>=1.5.5 ; extra == 'dev'
Requires-Dist: jinja2 <3.1.0,>=3.0.0 ; extra == 'dev'
Requires-Dist: sphinx-rtd-theme >=0.1.9 ; extra == 'dev'
Requires-Dist: sphinxcontrib-asyncio <0.4,>=0.2.0 ; extra == 'dev'
Requires-Dist: towncrier <22,>=21 ; extra == 'dev'
Requires-Dist: importlib-metadata <5.0 ; (python_version < "3.8") and extra == 'dev'
Provides-Extra: docs
Requires-Dist: py-evm >=0.2.0-a.14 ; extra == 'docs'
Requires-Dist: Sphinx <2,>=1.5.5 ; extra == 'docs'
Requires-Dist: jinja2 <3.1.0,>=3.0.0 ; extra == 'docs'
Requires-Dist: sphinx-rtd-theme >=0.1.9 ; extra == 'docs'
Requires-Dist: sphinxcontrib-asyncio <0.4,>=0.2.0 ; extra == 'docs'
Requires-Dist: towncrier <22,>=21 ; extra == 'docs'
Provides-Extra: eth
Requires-Dist: cached-property <2,>=1.5.1 ; extra == 'eth'
Requires-Dist: eth-bloom >=1.0.3 ; extra == 'eth'
Requires-Dist: eth-keys <0.5.0,>=0.4.0 ; extra == 'eth'
Requires-Dist: eth-typing <4.0.0,>=3.3.0 ; extra == 'eth'
Requires-Dist: eth-utils <3.0.0,>=2.0.0 ; extra == 'eth'
Requires-Dist: lru-dict >=1.1.6 ; extra == 'eth'
Requires-Dist: mypy-extensions >=1.0.0 ; extra == 'eth'
Requires-Dist: py-ecc <7.0.0,>=1.4.7 ; extra == 'eth'
Requires-Dist: rlp <4,>=3 ; extra == 'eth'
Requires-Dist: trie <3,>=2.0.0 ; extra == 'eth'
Provides-Extra: eth-extra
Requires-Dist: blake2b-py <0.3.0,>=0.2.0 ; extra == 'eth-extra'
Requires-Dist: coincurve >=18.0.0 ; extra == 'eth-extra'
Provides-Extra: lint
Requires-Dist: flake8 ==6.0.0 ; extra == 'lint'
Requires-Dist: flake8-bugbear ==23.3.23 ; extra == 'lint'
Requires-Dist: isort >=5.10.1 ; extra == 'lint'
Requires-Dist: mypy ==1.4.0 ; extra == 'lint'
Requires-Dist: pydocstyle >=6.0.0 ; extra == 'lint'
Requires-Dist: black >=23 ; extra == 'lint'
Requires-Dist: types-setuptools ; extra == 'lint'
Requires-Dist: importlib-metadata <5.0 ; (python_version < "3.8") and extra == 'lint'
Provides-Extra: test
Requires-Dist: factory-boy ==2.11.1 ; extra == 'test'
Requires-Dist: hypothesis <6,>=5 ; extra == 'test'
Requires-Dist: pexpect <5,>=4.6 ; extra == 'test'
Requires-Dist: pytest <7,>=6.2.4 ; extra == 'test'
Requires-Dist: pytest-asyncio <0.11,>=0.10.0 ; extra == 'test'
Requires-Dist: pytest-cov ==2.5.1 ; extra == 'test'
Requires-Dist: pytest-timeout <3,>=2.0.0 ; extra == 'test'
Requires-Dist: pytest-watch <5,>=4.1.0 ; extra == 'test'
Requires-Dist: pytest-xdist >=3.0 ; extra == 'test'
Requires-Dist: importlib-metadata <5.0 ; (python_version < "3.8") and extra == 'test'

# Python Implementation of the Ethereum protocol

[![Join the conversation on Discord](https://img.shields.io/discord/809793915578089484?color=blue&label=chat&logo=discord&logoColor=white)](https://discord.gg/GHryRvPB84)
[![Build Status](https://circleci.com/gh/ethereum/py-evm.svg?style=shield)](https://circleci.com/gh/ethereum/py-evm)
[![PyPI version](https://badge.fury.io/py/py-evm.svg)](https://badge.fury.io/py/py-evm)
[![Python versions](https://img.shields.io/pypi/pyversions/py-evm.svg)](https://pypi.python.org/pypi/py-evm)
[![Docs build](https://readthedocs.org/projects/py-evm/badge/?version=latest)](http://py-evm.readthedocs.io/en/latest/?badge=latest)


## Py-EVM

Py-EVM is an implementation of the Ethereum protocol in Python. It contains the low level
primitives for the existing Ethereum 1.0 chain as well as emerging support for the upcoming
Ethereum 2.0 / Serenity spec.

### Goals

Py-EVM aims to eventually become the defacto Python implementation of the Ethereum protocol,
enabling a wide array of use cases for both public and private chains. 

In particular Py-EVM aims to:

- be a reference implementation of the Ethereum 1.0 and 2.0 implementation in one of the most widely used and understood languages, Python.

- be easy to understand and modifiable

- have clear and simple APIs

- come with solid, friendly documentation

- deliver the low level primitives to build various clients on top (including *full* and *light* clients)

- be highly flexible to support both research as well as alternate use cases like private chains.


## Quickstart

[Get started in 5 minutes](https://py-evm.readthedocs.io/en/latest/guides/quickstart.html)

## Documentation

Check out the [documentation on our official website](https://py-evm.readthedocs.io/en/latest/)

## Want to help?

Want to file a bug, contribute some code, or improve documentation? Excellent! Read up on our
guidelines for [contributing](https://py-evm.readthedocs.io/en/latest/contributing.html) and then check out one of our issues that are labeled [Good First Issue](https://github.com/ethereum/py-evm/issues?q=is%3Aissue+is%3Aopen+label%3A%22Good+First+Issue%22).
