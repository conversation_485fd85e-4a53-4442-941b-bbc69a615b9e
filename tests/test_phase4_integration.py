"""
Comprehensive tests for Phase 4: News Source Integration Enhancement
Tests blockchain-aware classification, Solana integration, and cross-chain aggregation
"""
import pytest
import asyncio
from datetime import datetime
from typing import List

from scrapers.news.base import NewsItem
from scrapers.news.gaming_sources import (
    blockchain_classifier, 
    BlockchainNetwork, 
    GamingCategory,
    SolanaNewsScraper,
    MagicEdenBlogScraper,
    StarAtlasScraper
)
from scrapers.news.entity_recognition import entity_engine, GamingProject
from scrapers.news.cross_chain_aggregator import (
    cross_chain_aggregator,
    AggregationStrategy
)
from scrapers.news.duplicate_detection import duplicate_detector
from scrapers.news.real_time_monitor import RealTimeNewsMonitor, MonitoringConfig


class TestBlockchainAwareClassification:
    """Test blockchain-aware content classification"""
    
    def test_solana_detection(self):
        """Test Solana blockchain detection"""
        solana_content = "Star Atlas announces new Solana gaming features with SOL rewards"
        classification = blockchain_classifier.classify_content(solana_content)
        
        assert BlockchainNetwork.SOLANA.value in classification['blockchain_networks']
        assert classification['is_solana_focused'] is True
        assert GamingCategory.P2E.value in classification['gaming_categories']
    
    def test_ethereum_detection(self):
        """Test Ethereum blockchain detection"""
        ethereum_content = "The Sandbox launches new NFT game on Ethereum with SAND tokens"
        classification = blockchain_classifier.classify_content(ethereum_content)
        
        assert BlockchainNetwork.ETHEREUM.value in classification['blockchain_networks']
        assert classification['is_ethereum_focused'] is True
        assert GamingCategory.NFT_GAMING.value in classification['gaming_categories']
    
    def test_multi_chain_detection(self):
        """Test multi-chain content detection"""
        multi_chain_content = "Gaming protocol launches on Ethereum, Polygon, and Solana networks"
        classification = blockchain_classifier.classify_content(multi_chain_content)
        
        assert len(classification['blockchain_networks']) >= 3
        assert classification['is_multi_chain'] is True
        assert BlockchainNetwork.ETHEREUM.value in classification['blockchain_networks']
        assert BlockchainNetwork.POLYGON.value in classification['blockchain_networks']
        assert BlockchainNetwork.SOLANA.value in classification['blockchain_networks']
    
    def test_gaming_category_detection(self):
        """Test gaming category detection"""
        test_cases = [
            ("Play-to-earn game launches", GamingCategory.P2E.value),
            ("NFT gaming collection drops", GamingCategory.NFT_GAMING.value),
            ("Metaverse virtual world opens", GamingCategory.METAVERSE.value),
            ("DeFi gaming protocol update", GamingCategory.DEFI_GAMING.value)
        ]
        
        for content, expected_category in test_cases:
            classification = blockchain_classifier.classify_content(content)
            assert expected_category in classification['gaming_categories']


class TestEntityRecognition:
    """Test gaming project entity recognition"""
    
    def test_axie_infinity_detection(self):
        """Test Axie Infinity project detection"""
        content = "Axie Infinity announces new AXS staking rewards for players"
        analysis = entity_engine.analyze_content(content)
        
        assert 'axie_infinity' in analysis['detected_projects']
        assert analysis['has_ethereum_projects'] is True
        
        project_details = analysis['project_details']['axie_infinity']
        assert project_details['name'] == "Axie Infinity"
        assert 'AXS' in project_details['tokens']
    
    def test_star_atlas_detection(self):
        """Test Star Atlas (Solana) project detection"""
        content = "Star Atlas releases new ATLAS token mechanics for space exploration"
        analysis = entity_engine.analyze_content(content)
        
        assert 'star_atlas' in analysis['detected_projects']
        assert analysis['has_solana_projects'] is True
        assert analysis['primary_network'] == 'solana'
        
        project_details = analysis['project_details']['star_atlas']
        assert project_details['name'] == "Star Atlas"
        assert 'ATLAS' in project_details['tokens']
    
    def test_multiple_project_detection(self):
        """Test detection of multiple gaming projects"""
        content = "Axie Infinity and The Sandbox announce partnership for metaverse integration"
        analysis = entity_engine.analyze_content(content)
        
        assert 'axie_infinity' in analysis['detected_projects']
        assert 'the_sandbox' in analysis['detected_projects']
        assert analysis['total_projects_mentioned'] >= 2
    
    def test_cross_reference_data(self):
        """Test cross-reference data generation"""
        projects = ['star_atlas', 'axie_infinity']
        cross_ref = entity_engine.get_cross_reference_data(projects)
        
        assert 'contracts_to_monitor' in cross_ref
        assert 'tokens_to_track' in cross_ref
        assert 'networks_involved' in cross_ref
        
        assert 'solana' in cross_ref['networks_involved']
        assert 'ethereum' in cross_ref['networks_involved'] or 'ronin' in cross_ref['networks_involved']


class TestCrossChainAggregation:
    """Test cross-chain content aggregation"""
    
    def create_test_articles(self) -> List[NewsItem]:
        """Create test articles for aggregation"""
        articles = [
            NewsItem(
                title="Solana Gaming Ecosystem Grows with New Star Atlas Update",
                content="Star Atlas announces major update to Solana gaming platform",
                url="https://example.com/solana-gaming-1",
                published_at=datetime.now(),
                source_id=1
            ),
            NewsItem(
                title="Ethereum NFT Game The Sandbox Launches New Features",
                content="The Sandbox introduces new SAND token mechanics on Ethereum",
                url="https://example.com/ethereum-gaming-1",
                published_at=datetime.now(),
                source_id=2
            ),
            NewsItem(
                title="Multi-Chain Gaming Protocol Supports Ethereum and Solana",
                content="New gaming protocol launches on both Ethereum and Solana networks",
                url="https://example.com/multi-chain-1",
                published_at=datetime.now(),
                source_id=3
            )
        ]
        return articles
    
    @pytest.mark.asyncio
    async def test_content_aggregation(self):
        """Test content aggregation with classification"""
        articles = self.create_test_articles()
        
        aggregated = await cross_chain_aggregator.aggregate_content(
            articles, AggregationStrategy.RELEVANCE_BASED
        )
        
        assert len(aggregated) == 3
        
        # Check that all items have proper classification
        for item in aggregated:
            assert item.blockchain_classification is not None
            assert item.entity_analysis is not None
            assert item.priority_score is not None
            assert item.relevance_score is not None
            assert item.network_focus is not None
    
    @pytest.mark.asyncio
    async def test_solana_filtering(self):
        """Test Solana-focused content filtering"""
        articles = self.create_test_articles()
        
        aggregated = await cross_chain_aggregator.aggregate_content(articles)
        solana_content = cross_chain_aggregator.get_solana_focused_content(aggregated)
        
        assert len(solana_content) >= 1
        
        for item in solana_content:
            assert (item.network_focus == 'solana' or 
                   item.aggregation_metadata.get('solana_focused', False))
    
    @pytest.mark.asyncio
    async def test_network_balanced_strategy(self):
        """Test network-balanced aggregation strategy"""
        articles = self.create_test_articles()
        
        aggregated = await cross_chain_aggregator.aggregate_content(
            articles, AggregationStrategy.NETWORK_BALANCED
        )
        
        # Check that different networks are represented
        networks = [item.network_focus for item in aggregated]
        assert len(set(networks)) > 1  # Multiple networks represented
    
    def test_network_distribution(self):
        """Test network distribution calculation"""
        # Create mock aggregated content
        from scrapers.news.cross_chain_aggregator import AggregatedContent
        
        mock_items = []
        for i, network in enumerate(['solana', 'ethereum', 'solana', 'polygon']):
            mock_item = type('MockItem', (), {
                'network_focus': network
            })()
            mock_items.append(mock_item)
        
        distribution = cross_chain_aggregator.get_network_distribution(mock_items)
        
        assert distribution['solana'] == 2
        assert distribution['ethereum'] == 1
        assert distribution['polygon'] == 1


class TestDuplicateDetection:
    """Test enhanced duplicate detection"""
    
    def create_duplicate_articles(self) -> List[NewsItem]:
        """Create test articles with duplicates"""
        articles = [
            NewsItem(
                title="Axie Infinity Announces New Update",
                content="Axie Infinity has announced a major new update to their gaming platform",
                url="https://coindesk.com/axie-update",
                published_at=datetime.now(),
                source_id=1
            ),
            NewsItem(
                title="Axie Infinity Reveals Major Update",
                content="Axie Infinity announced a significant update to their gaming ecosystem",
                url="https://decrypt.co/axie-news",
                published_at=datetime.now(),
                source_id=2
            ),
            NewsItem(
                title="Solana Gaming News: Star Atlas Expansion",
                content="Star Atlas expands their Solana-based gaming universe",
                url="https://solana.com/star-atlas",
                published_at=datetime.now(),
                source_id=3
            )
        ]
        return articles
    
    def test_duplicate_detection(self):
        """Test duplicate detection between similar articles"""
        articles = self.create_duplicate_articles()
        duplicates = duplicate_detector.detect_duplicates(articles)
        
        assert len(duplicates) >= 1
        
        # Check that the Axie Infinity articles are detected as duplicates
        axie_duplicate = next(
            (d for d in duplicates 
             if 'axie' in d.article1_url.lower() and 'axie' in d.article2_url.lower()),
            None
        )
        assert axie_duplicate is not None
        assert axie_duplicate.similarity_score >= 0.65
    
    def test_cross_post_detection(self):
        """Test cross-post detection across different sources"""
        articles = self.create_duplicate_articles()
        duplicates = duplicate_detector.detect_duplicates(articles)
        
        cross_posts = [d for d in duplicates if d.match_type == 'cross_post']
        assert len(cross_posts) >= 0  # May or may not have cross-posts in test data
    
    def test_duplicate_filtering(self):
        """Test duplicate filtering functionality"""
        articles = self.create_duplicate_articles()
        filtered = duplicate_detector.filter_duplicates(articles, 'earliest')
        
        # Should have fewer articles after filtering
        assert len(filtered) <= len(articles)
        
        # All remaining articles should have unique URLs
        urls = [article.url for article in filtered]
        assert len(urls) == len(set(urls))


class TestSolanaScrapers:
    """Test Solana-specific news scrapers"""
    
    def test_solana_news_scraper_initialization(self):
        """Test Solana news scraper initialization"""
        scraper = SolanaNewsScraper(source_id=1)
        assert scraper.source_id == 1
        assert 'solana.com' in scraper.config['rss_url']
    
    def test_magic_eden_scraper_initialization(self):
        """Test Magic Eden blog scraper initialization"""
        scraper = MagicEdenBlogScraper(source_id=2)
        assert scraper.source_id == 2
        assert 'magiceden' in scraper.config['base_url']
    
    def test_star_atlas_scraper_initialization(self):
        """Test Star Atlas scraper initialization"""
        scraper = StarAtlasScraper(source_id=3)
        assert scraper.source_id == 3
        assert 'staratlas' in scraper.config['base_url']
    
    def test_solana_gaming_detection(self):
        """Test Solana gaming content detection"""
        scraper = SolanaNewsScraper(source_id=1)
        
        solana_gaming_text = "New Solana gaming dapp launches with SOL rewards"
        assert scraper.is_gaming_related(solana_gaming_text) is True
        
        non_gaming_text = "Solana network upgrade improves transaction speed"
        # This might still be detected as gaming-related due to broad keywords
        # The test verifies the method works, not necessarily the specific result


class TestRealTimeMonitoring:
    """Test real-time news monitoring system"""
    
    def test_monitoring_config(self):
        """Test monitoring configuration"""
        config = MonitoringConfig(
            check_interval_minutes=10,
            focus_networks=['solana', 'ethereum'],
            notification_threshold_score=0.8
        )
        
        assert config.check_interval_minutes == 10
        assert 'solana' in config.focus_networks
        assert config.notification_threshold_score == 0.8
    
    def test_monitor_initialization(self):
        """Test real-time monitor initialization"""
        config = MonitoringConfig(focus_networks=['solana'])
        monitor = RealTimeNewsMonitor(config)
        
        assert monitor.config.focus_networks == ['solana']
        assert monitor.is_running is False
        assert len(monitor.alert_handlers) == 0
    
    def test_priority_sources(self):
        """Test priority source identification"""
        monitor = RealTimeNewsMonitor()
        priority_sources = monitor._get_priority_sources()
        
        # Should include Solana-focused sources
        solana_sources = [s for s in priority_sources if 'solana' in s.lower()]
        assert len(solana_sources) > 0
    
    def test_monitoring_stats(self):
        """Test monitoring statistics"""
        monitor = RealTimeNewsMonitor()
        stats = monitor.get_monitoring_stats()
        
        assert 'total_checks' in stats
        assert 'articles_processed' in stats
        assert 'alerts_generated' in stats
        assert 'is_running' in stats


@pytest.mark.integration
class TestPhase4Integration:
    """Integration tests for complete Phase 4 functionality"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_processing(self):
        """Test end-to-end news processing with Phase 4 enhancements"""
        # Create test article
        article = NewsItem(
            title="Star Atlas Launches New Solana Gaming Features with ATLAS Rewards",
            content="Star Atlas, the space exploration game on Solana, has announced new gaming features that reward players with ATLAS tokens for completing missions.",
            url="https://example.com/star-atlas-update",
            published_at=datetime.now(),
            source_id=1
        )
        
        # Test blockchain classification
        classification = blockchain_classifier.classify_content(
            article.title, article.content, ""
        )
        assert BlockchainNetwork.SOLANA.value in classification['blockchain_networks']
        assert classification['is_solana_focused'] is True
        
        # Test entity recognition
        entity_analysis = entity_engine.analyze_content(
            article.title, article.content, ""
        )
        assert 'star_atlas' in entity_analysis['detected_projects']
        assert entity_analysis['has_solana_projects'] is True
        
        # Test aggregation
        aggregated = await cross_chain_aggregator.aggregate_content([article])
        assert len(aggregated) == 1
        
        aggregated_item = aggregated[0]
        assert aggregated_item.network_focus == 'solana'
        assert aggregated_item.priority_score > 0
        assert aggregated_item.relevance_score > 0
        
        # Test cross-reference data
        cross_ref = entity_analysis.get('cross_references') or entity_engine.get_cross_reference_data(
            entity_analysis['detected_projects']
        )
        assert 'solana' in cross_ref['networks_involved']
    
    def test_phase4_metadata_structure(self):
        """Test that Phase 4 metadata has correct structure"""
        article = NewsItem(
            title="Test Gaming Article",
            content="Test content about gaming",
            url="https://example.com/test",
            published_at=datetime.now(),
            source_id=1
        )
        
        # Simulate Phase 4 processing
        classification = blockchain_classifier.classify_content(article.title, article.content)
        entity_analysis = entity_engine.analyze_content(article.title, article.content)
        
        # Check required fields
        assert 'blockchain_networks' in classification
        assert 'gaming_categories' in classification
        assert 'is_solana_focused' in classification
        assert 'is_ethereum_focused' in classification
        assert 'is_multi_chain' in classification
        
        assert 'detected_projects' in entity_analysis
        assert 'project_details' in entity_analysis
        assert 'network_distribution' in entity_analysis
        assert 'has_solana_projects' in entity_analysis
        assert 'has_ethereum_projects' in entity_analysis


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
