#!/usr/bin/env python3
"""
Discord Web3/Crypto Gaming Server Scraper
Monitors and discovers Discord servers related to web3, blockchain, and crypto gaming.

Note: Due to Discord API limitations, this tool works by:
1. Monitoring servers the bot is already in
2. Analyzing invite links and server metadata
3. Using third-party Discord listing sites
4. Tracking gaming-related keywords and blockchain mentions
"""
import os
import sys
import json
import asyncio
import aiohttp
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import discord
from discord.ext import commands, tasks
from config.settings import get_settings

settings = get_settings()


@dataclass
class ServerInfo:
    """Data class for Discord server information"""
    id: int
    name: str
    description: Optional[str]
    member_count: int
    created_at: str
    invite_url: Optional[str]
    icon_url: Optional[str]
    banner_url: Optional[str]
    categories: List[str]
    blockchain_mentions: List[str]
    gaming_keywords: List[str]
    verification_level: str
    boost_level: int
    features: List[str]
    discovered_at: str
    source: str  # How we discovered this server


class Web3GamingDiscordBot(commands.Bot):
    """Discord bot for discovering and monitoring web3/crypto gaming servers"""
    
    def __init__(self):
        # Gaming and blockchain keywords to monitor
        self.gaming_keywords = {
            'p2e', 'play to earn', 'play-to-earn', 'gamefi', 'gaming', 'nft game', 
            'blockchain game', 'crypto game', 'metaverse', 'virtual world',
            'guild', 'esports', 'tournament', 'rpg', 'mmorpg', 'strategy',
            'card game', 'trading card', 'collectible', 'avatar', 'character'
        }
        
        self.blockchain_keywords = {
            'solana', 'ethereum', 'avalanche', 'base', 'bsc', 'binance smart chain',
            'ton', 'polygon', 'arbitrum', 'optimism', 'fantom', 'near',
            'web3', 'blockchain', 'crypto', 'defi', 'nft', 'token', 'coin',
            'smart contract', 'dapp', 'dao', 'yield farming', 'staking'
        }
        
        self.target_blockchains = {
            'solana', 'ethereum', 'avalanche', 'base', 'bsc', 'ton', 'polygon'
        }
        
        # Storage for discovered servers
        self.discovered_servers: Dict[int, ServerInfo] = {}
        self.monitored_servers: Set[int] = set()
        
        # Initialize bot with required intents
        intents = discord.Intents.default()
        intents.guilds = True
        intents.guild_messages = True
        intents.message_content = True
        
        super().__init__(
            command_prefix='!',
            intents=intents,
            help_command=None
        )
    
    async def setup_hook(self):
        """Setup hook called when bot starts"""
        print("🤖 Discord Web3 Gaming Bot starting up...")
        
        # Load previously discovered servers
        await self.load_discovered_servers()
        
        # Start monitoring tasks
        self.monitor_servers.start()
        self.analyze_messages.start()
        
        print(f"✅ Bot ready! Monitoring {len(self.guilds)} servers")
    
    async def on_ready(self):
        """Called when bot is ready"""
        print(f"🚀 Logged in as {self.user} (ID: {self.user.id})")
        print(f"📊 Connected to {len(self.guilds)} servers")
        
        # Analyze current servers
        for guild in self.guilds:
            await self.analyze_server(guild, source="initial_scan")
    
    async def on_guild_join(self, guild):
        """Called when bot joins a new server"""
        print(f"🆕 Joined new server: {guild.name} (ID: {guild.id})")
        await self.analyze_server(guild, source="bot_joined")
    
    async def analyze_server(self, guild: discord.Guild, source: str = "unknown"):
        """Analyze a Discord server for web3/gaming relevance"""
        try:
            # Skip if already analyzed recently
            if guild.id in self.discovered_servers:
                return
            
            print(f"🔍 Analyzing server: {guild.name}")
            
            # Gather server information
            server_info = await self.gather_server_info(guild, source)
            
            # Check if server is relevant to web3/crypto gaming
            relevance_score = self.calculate_relevance_score(server_info)
            
            if relevance_score > 0.3:  # Threshold for relevance
                print(f"✅ Relevant server found: {guild.name} (Score: {relevance_score:.2f})")
                self.discovered_servers[guild.id] = server_info
                await self.save_discovered_servers()
                
                # Add to monitoring list
                self.monitored_servers.add(guild.id)
            else:
                print(f"❌ Server not relevant: {guild.name} (Score: {relevance_score:.2f})")
        
        except Exception as e:
            print(f"❌ Error analyzing server {guild.name}: {e}")
    
    async def gather_server_info(self, guild: discord.Guild, source: str) -> ServerInfo:
        """Gather comprehensive information about a Discord server"""
        # Get server description from various sources
        description = None
        if hasattr(guild, 'description') and guild.description:
            description = guild.description
        elif guild.system_channel:
            # Try to get description from system channel topic
            try:
                if guild.system_channel.topic:
                    description = guild.system_channel.topic
            except:
                pass
        
        # Get invite URL
        invite_url = None
        try:
            invites = await guild.invites()
            if invites:
                invite_url = str(invites[0])
            else:
                # Create a temporary invite if possible
                if guild.text_channels:
                    invite = await guild.text_channels[0].create_invite(
                        max_age=0, max_uses=0, reason="Server analysis"
                    )
                    invite_url = str(invite)
        except:
            pass
        
        # Analyze channels for keywords
        categories = []
        gaming_keywords = []
        blockchain_mentions = []
        
        for channel in guild.channels:
            channel_name = channel.name.lower()
            
            # Check for gaming keywords
            for keyword in self.gaming_keywords:
                if keyword in channel_name:
                    gaming_keywords.append(keyword)
            
            # Check for blockchain mentions
            for keyword in self.blockchain_keywords:
                if keyword in channel_name:
                    blockchain_mentions.append(keyword)
            
            # Categorize channels
            if any(word in channel_name for word in ['general', 'chat', 'discussion']):
                categories.append('general')
            elif any(word in channel_name for word in ['announcement', 'news', 'update']):
                categories.append('announcements')
            elif any(word in channel_name for word in ['trading', 'marketplace', 'buy', 'sell']):
                categories.append('trading')
            elif any(word in channel_name for word in ['game', 'play', 'guild']):
                categories.append('gaming')
        
        return ServerInfo(
            id=guild.id,
            name=guild.name,
            description=description,
            member_count=guild.member_count,
            created_at=guild.created_at.isoformat(),
            invite_url=invite_url,
            icon_url=str(guild.icon.url) if guild.icon else None,
            banner_url=str(guild.banner.url) if guild.banner else None,
            categories=list(set(categories)),
            blockchain_mentions=list(set(blockchain_mentions)),
            gaming_keywords=list(set(gaming_keywords)),
            verification_level=str(guild.verification_level),
            boost_level=guild.premium_tier,
            features=guild.features,
            discovered_at=datetime.utcnow().isoformat(),
            source=source
        )
    
    def calculate_relevance_score(self, server_info: ServerInfo) -> float:
        """Calculate relevance score for web3/crypto gaming"""
        score = 0.0
        
        # Server name analysis
        name_lower = server_info.name.lower()
        for keyword in self.gaming_keywords:
            if keyword in name_lower:
                score += 0.3
        
        for keyword in self.blockchain_keywords:
            if keyword in name_lower:
                score += 0.2
        
        # Description analysis
        if server_info.description:
            desc_lower = server_info.description.lower()
            for keyword in self.gaming_keywords:
                if keyword in desc_lower:
                    score += 0.2
            
            for keyword in self.blockchain_keywords:
                if keyword in desc_lower:
                    score += 0.15
        
        # Channel analysis
        score += len(server_info.gaming_keywords) * 0.1
        score += len(server_info.blockchain_mentions) * 0.1
        
        # Target blockchain bonus
        for blockchain in self.target_blockchains:
            if blockchain in server_info.blockchain_mentions:
                score += 0.2
        
        # Member count factor (larger servers might be more relevant)
        if server_info.member_count > 1000:
            score += 0.1
        if server_info.member_count > 10000:
            score += 0.1
        
        # Features bonus
        if 'COMMUNITY' in server_info.features:
            score += 0.1
        if 'VERIFIED' in server_info.features:
            score += 0.1
        
        return min(score, 1.0)  # Cap at 1.0
    
    @tasks.loop(hours=6)
    async def monitor_servers(self):
        """Periodically monitor servers for changes"""
        print("🔄 Running periodic server monitoring...")
        
        for guild in self.guilds:
            if guild.id in self.monitored_servers:
                # Re-analyze monitored servers for updates
                await self.analyze_server(guild, source="periodic_scan")
        
        print(f"✅ Monitoring complete. Tracking {len(self.discovered_servers)} relevant servers")
    
    @tasks.loop(minutes=30)
    async def analyze_messages(self):
        """Analyze recent messages for invite links and server mentions"""
        print("🔍 Analyzing messages for new server discoveries...")
        
        for guild in self.guilds:
            if guild.id not in self.monitored_servers:
                continue
            
            try:
                # Check recent messages in general channels
                for channel in guild.text_channels[:3]:  # Limit to first 3 channels
                    if not channel.permissions_for(guild.me).read_message_history:
                        continue
                    
                    async for message in channel.history(limit=50):
                        await self.analyze_message_for_invites(message)
            
            except Exception as e:
                print(f"❌ Error analyzing messages in {guild.name}: {e}")
    
    async def analyze_message_for_invites(self, message):
        """Analyze a message for Discord invite links"""
        # Look for Discord invite patterns
        invite_pattern = r'discord\.gg/[a-zA-Z0-9]+'
        invites = re.findall(invite_pattern, message.content)
        
        for invite_code in invites:
            try:
                invite = await self.fetch_invite(invite_code.split('/')[-1])
                if invite and invite.guild:
                    await self.analyze_server(invite.guild, source="invite_link")
            except:
                pass
    
    async def save_discovered_servers(self):
        """Save discovered servers to JSON file"""
        data = {
            'last_updated': datetime.utcnow().isoformat(),
            'total_servers': len(self.discovered_servers),
            'servers': {str(k): asdict(v) for k, v in self.discovered_servers.items()}
        }
        
        os.makedirs('data/discord', exist_ok=True)
        with open('data/discord/discovered_servers.json', 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"💾 Saved {len(self.discovered_servers)} discovered servers")
    
    async def load_discovered_servers(self):
        """Load previously discovered servers from JSON file"""
        try:
            with open('data/discord/discovered_servers.json', 'r') as f:
                data = json.load(f)
            
            for server_id, server_data in data.get('servers', {}).items():
                self.discovered_servers[int(server_id)] = ServerInfo(**server_data)
                self.monitored_servers.add(int(server_id))
            
            print(f"📂 Loaded {len(self.discovered_servers)} previously discovered servers")
        
        except FileNotFoundError:
            print("📂 No previous server data found, starting fresh")
        except Exception as e:
            print(f"❌ Error loading server data: {e}")


async def scrape_discord_listing_sites():
    """Scrape third-party Discord server listing sites for web3/gaming servers"""
    print("🌐 Scraping Discord listing sites...")
    
    # Popular Discord server listing sites
    listing_sites = [
        'https://disboard.org/servers/tag/gaming',
        'https://disboard.org/servers/tag/crypto',
        'https://discord.me/servers/tag/gaming',
        'https://discord.me/servers/tag/blockchain'
    ]
    
    discovered_invites = []
    
    async with aiohttp.ClientSession() as session:
        for site_url in listing_sites:
            try:
                print(f"🔍 Scraping: {site_url}")
                
                async with session.get(site_url) as response:
                    if response.status == 200:
                        html = await response.text()
                        
                        # Extract Discord invite links
                        invite_pattern = r'discord\.gg/[a-zA-Z0-9]+'
                        invites = re.findall(invite_pattern, html)
                        
                        for invite in invites:
                            if invite not in discovered_invites:
                                discovered_invites.append(invite)
                
                # Rate limiting
                await asyncio.sleep(2)
            
            except Exception as e:
                print(f"❌ Error scraping {site_url}: {e}")
    
    print(f"✅ Found {len(discovered_invites)} invite links from listing sites")
    
    # Save discovered invites
    os.makedirs('data/discord', exist_ok=True)
    with open('data/discord/discovered_invites.json', 'w') as f:
        json.dump({
            'discovered_at': datetime.utcnow().isoformat(),
            'invites': discovered_invites
        }, f, indent=2)
    
    return discovered_invites


def main():
    """Main function to run the Discord scraper"""
    print("🚀 Starting Discord Web3 Gaming Server Scraper")
    
    # Check for Discord bot token
    discord_token = os.getenv('DISCORD_BOT_TOKEN')
    if not discord_token:
        print("❌ DISCORD_BOT_TOKEN environment variable not set")
        print("Please create a Discord bot at https://discord.com/developers/applications")
        print("and set the DISCORD_BOT_TOKEN environment variable")
        return
    
    # Create and run bot
    bot = Web3GamingDiscordBot()
    
    try:
        bot.run(discord_token)
    except discord.LoginFailure:
        print("❌ Invalid Discord bot token")
    except Exception as e:
        print(f"❌ Error running bot: {e}")


if __name__ == '__main__':
    main()
