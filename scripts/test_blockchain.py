#!/usr/bin/env python3
"""
Test script for blockchain integration functionality
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
from datetime import datetime
from models.base import SessionLocal
from models.gaming import BlockchainData, GamingProject
from blockchain.rpc import rpc_manager

async def test_blockchain_data_storage():
    """Test storing blockchain data"""
    print("🔗 Testing blockchain data storage...")
    
    db = SessionLocal()
    
    try:
        # Get a gaming project to associate with
        project = db.query(GamingProject).filter(GamingProject.slug == "axie-infinity").first()
        if not project:
            print("❌ No Axie Infinity project found")
            return
        
        # Create sample blockchain data
        blockchain_data = BlockchainData(
            gaming_project_id=project.id,
            blockchain="ethereum",
            block_number=18500000,
            transaction_hash="0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
            contract_address="******************************************",  # Axie contract
            event_type="Transfer",
            event_data={
                "from": "******************************************",
                "to": "******************************************",
                "tokenId": "12345"
            },
            token_symbol="AXS",
            token_address="******************************************",
            token_amount=100.0,
            token_price_usd=8.50,
            from_address="******************************************",
            to_address="******************************************",
            gas_used=21000,
            gas_price=20.0,
            block_timestamp=datetime.now()
        )
        
        db.add(blockchain_data)
        db.commit()
        
        print("✅ Successfully stored blockchain data")
        print(f"   - Project: {project.name}")
        print(f"   - Blockchain: {blockchain_data.blockchain}")
        print(f"   - Event: {blockchain_data.event_type}")
        print(f"   - Token: {blockchain_data.token_symbol}")
        
    except Exception as e:
        print(f"❌ Error storing blockchain data: {e}")
        db.rollback()
    finally:
        db.close()

async def test_rpc_connections():
    """Test RPC connections to different blockchains"""
    print("🌐 Testing RPC connections...")
    
    blockchains = ["ethereum", "polygon", "bsc"]
    
    for blockchain in blockchains:
        try:
            client = rpc_manager.get_connection(blockchain)
            if client:
                # Try to get latest block number
                try:
                    latest_block = client.eth.block_number
                    print(f"✅ {blockchain.capitalize()}: Connected (Block: {latest_block})")
                except Exception as e:
                    print(f"❌ {blockchain.capitalize()}: Connection failed - {str(e)[:50]}...")
            else:
                print(f"❌ {blockchain.capitalize()}: No client available")
        except Exception as e:
            print(f"❌ {blockchain.capitalize()}: Error - {str(e)[:50]}...")

async def test_contract_interaction():
    """Test basic contract interaction"""
    print("📄 Testing contract interaction...")
    
    try:
        # Test with Ethereum
        client = rpc_manager.get_connection("ethereum")
        if not client:
            print("❌ No Ethereum client available")
            return
        
        # Test getting balance of a known address (Axie Infinity treasury)
        axie_treasury = "******************************************"
        
        try:
            balance = client.eth.get_balance(axie_treasury)
            balance_eth = client.from_wei(balance, 'ether')
            print(f"✅ Successfully queried balance: {balance_eth:.4f} ETH")
        except Exception as e:
            print(f"❌ Balance query failed: {str(e)[:50]}...")
        
        # Test getting transaction count
        try:
            tx_count = client.eth.get_transaction_count(axie_treasury)
            print(f"✅ Transaction count: {tx_count}")
        except Exception as e:
            print(f"❌ Transaction count query failed: {str(e)[:50]}...")
            
    except Exception as e:
        print(f"❌ Contract interaction test failed: {e}")

async def test_gaming_metrics():
    """Test gaming metrics calculation"""
    print("📊 Testing gaming metrics...")
    
    db = SessionLocal()
    
    try:
        # Get all gaming projects
        projects = db.query(GamingProject).all()
        
        print(f"📈 Gaming Projects Analysis:")
        print(f"   - Total projects: {len(projects)}")
        
        # Calculate metrics by category
        categories = {}
        total_market_cap = 0
        total_dau = 0
        
        for project in projects:
            category = project.category
            if category not in categories:
                categories[category] = {
                    "count": 0,
                    "market_cap": 0,
                    "dau": 0
                }
            
            categories[category]["count"] += 1
            if project.market_cap:
                categories[category]["market_cap"] += project.market_cap
                total_market_cap += project.market_cap
            if project.daily_active_users:
                categories[category]["dau"] += project.daily_active_users
                total_dau += project.daily_active_users
        
        print(f"   - Total market cap: ${total_market_cap:,.0f}")
        print(f"   - Total DAU: {total_dau:,}")
        
        print(f"\n📊 By Category:")
        for category, metrics in categories.items():
            print(f"   - {category}:")
            print(f"     • Projects: {metrics['count']}")
            print(f"     • Market Cap: ${metrics['market_cap']:,.0f}")
            print(f"     • DAU: {metrics['dau']:,}")
        
        # Get blockchain data count
        blockchain_data_count = db.query(BlockchainData).count()
        print(f"\n🔗 Blockchain Data:")
        print(f"   - Total records: {blockchain_data_count}")
        
    except Exception as e:
        print(f"❌ Gaming metrics test failed: {e}")
    finally:
        db.close()

async def main():
    """Main test function"""
    print("🚀 Starting Web3 Gaming Tracker Tests")
    print("=" * 50)
    
    await test_blockchain_data_storage()
    print()
    
    await test_rpc_connections()
    print()
    
    await test_contract_interaction()
    print()
    
    await test_gaming_metrics()
    print()
    
    print("=" * 50)
    print("✅ All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
