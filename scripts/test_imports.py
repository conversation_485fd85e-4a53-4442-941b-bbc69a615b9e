#!/usr/bin/env python3
"""
Test script to check imports
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("Testing imports...")

try:
    from scrapers.news.base import BaseScraper, NewsItem, RSSFeedScraper, WebScraper
    print("✅ Base imports successful")
except Exception as e:
    print(f"❌ Base imports failed: {e}")

try:
    from scrapers.news.gaming_sources import CoinDeskGamingScraper
    print("✅ Gaming sources imports successful")
except Exception as e:
    print(f"❌ Gaming sources imports failed: {e}")

try:
    # Try to instantiate a concrete scraper
    scraper = CoinDeskGamingScraper(1)
    print("✅ CoinDeskGamingScraper instantiation successful")
except Exception as e:
    print(f"❌ CoinDeskGamingScraper instantiation failed: {e}")

print("Import tests completed!")
