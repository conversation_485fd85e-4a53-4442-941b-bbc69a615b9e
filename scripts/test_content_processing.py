#!/usr/bin/env python3
"""
Simple test for content processing functionality
"""
import asyncio
from textblob import TextBlob


async def test_content_processing():
    """Test content processing functionality"""
    print("🧠 Testing content processing...")
    
    try:
        # Test content
        test_content = """
        This is a test article about blockchain gaming and NFTs.
        The game features play-to-earn mechanics and DeFi integration.
        Players can earn tokens by completing quests and battles.
        """
        
        # Test sentiment analysis directly
        blob = TextBlob(test_content)
        sentiment = blob.sentiment.polarity
        print(f"   📊 Sentiment score: {sentiment}")
        
        # Test keyword extraction directly
        keywords = [word for word, pos in blob.tags if pos.startswith('NN')][:5]
        print(f"   🔑 Keywords: {keywords}")
        
        # Test gaming content detection
        gaming_keywords = [
            'gaming', 'game', 'nft', 'metaverse', 'play-to-earn', 'p2e',
            'blockchain game', 'crypto game', 'defi', 'token', 'quest'
        ]
        text_lower = test_content.lower()
        is_gaming = any(keyword in text_lower for keyword in gaming_keywords)
        print(f"   🎮 Is gaming content: {is_gaming}")
        
        print("   ✅ Content processing tests passed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing content processing: {e}")
        return False


async def main():
    """Run content processing test"""
    print("🚀 Starting Content Processing Test")
    print("=" * 50)
    
    success = await test_content_processing()
    
    if success:
        print("\n🎉 Content processing test completed successfully!")
    else:
        print("\n❌ Content processing test failed!")


if __name__ == "__main__":
    asyncio.run(main())
