#!/usr/bin/env python3
"""
Test Phase 3: Blockchain Integration Framework
Comprehensive testing of all blockchain integration components
"""
import asyncio
import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from blockchain import (
    multi_chain_manager,
    enhanced_rpc_manager,
    gaming_contract_manager,
    gaming_event_monitor,
    nft_tracker,
    gaming_market_data,
    blockchain_sync_manager,
    error_handler,
    get_blockchain_health_status
)
from config.settings import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

settings = get_settings()


async def test_multi_chain_client():
    """Test multi-chain client functionality"""
    logger.info("=== Testing Multi-Chain Client ===")
    
    try:
        # Test getting supported chains
        chains = multi_chain_manager.get_gaming_chains()
        logger.info(f"Supported chains: {chains}")
        
        # Test each chain client
        for chain in chains:
            logger.info(f"\nTesting {chain} client...")
            client = multi_chain_manager.get_client(chain)
            
            if client:
                logger.info(f"✓ {chain} client created: {client.__class__.__name__}")
                
                # Test getting latest block (if RPC is configured)
                try:
                    latest_block = await client.get_latest_block_number()
                    if latest_block:
                        logger.info(f"✓ {chain} latest block: {latest_block}")
                    else:
                        logger.warning(f"⚠ {chain} RPC not configured or unavailable")
                except Exception as e:
                    logger.warning(f"⚠ {chain} RPC error: {e}")
            else:
                logger.error(f"✗ Failed to create {chain} client")
        
        return True
        
    except Exception as e:
        logger.error(f"Multi-chain client test failed: {e}")
        return False


async def test_enhanced_rpc():
    """Test enhanced RPC management"""
    logger.info("\n=== Testing Enhanced RPC Management ===")
    
    try:
        # Test RPC endpoint management
        chains_with_rpc = []
        
        for chain in multi_chain_manager.get_gaming_chains():
            endpoints = enhanced_rpc_manager.get_endpoints_for_chain(chain)
            if endpoints:
                chains_with_rpc.append(chain)
                logger.info(f"✓ {chain} has {len(endpoints)} RPC endpoints")
                
                for endpoint in endpoints:
                    logger.info(f"  - {endpoint.url} (priority: {endpoint.priority}, "
                               f"health: {endpoint.health_score:.2f})")
        
        if chains_with_rpc:
            logger.info(f"✓ Enhanced RPC configured for {len(chains_with_rpc)} chains")
        else:
            logger.warning("⚠ No RPC endpoints configured")
        
        return True
        
    except Exception as e:
        logger.error(f"Enhanced RPC test failed: {e}")
        return False


async def test_gaming_contracts():
    """Test gaming contract management"""
    logger.info("\n=== Testing Gaming Contract Management ===")
    
    try:
        # Test getting all contracts
        all_contracts = gaming_contract_manager.get_all_contracts()
        logger.info(f"✓ Total gaming contracts: {len(all_contracts)}")
        
        # Test contracts by chain
        for chain in multi_chain_manager.get_gaming_chains():
            chain_contracts = gaming_contract_manager.get_contracts_by_chain(chain)
            if chain_contracts:
                logger.info(f"✓ {chain}: {len(chain_contracts)} contracts")
                
                # Show first few contracts
                for contract in chain_contracts[:3]:
                    logger.info(f"  - {contract.name} ({contract.contract_type.value})")
        
        # Test gaming tokens and NFTs
        gaming_tokens = gaming_contract_manager.get_gaming_tokens()
        gaming_nfts = gaming_contract_manager.get_gaming_nfts()
        
        logger.info(f"✓ Gaming tokens: {len(gaming_tokens)}")
        logger.info(f"✓ Gaming NFTs: {len(gaming_nfts)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Gaming contracts test failed: {e}")
        return False


async def test_event_monitoring():
    """Test event monitoring system"""
    logger.info("\n=== Testing Event Monitoring ===")
    
    try:
        # Test event monitor initialization
        logger.info("✓ Gaming event monitor initialized")
        
        # Test getting monitored contracts
        monitored_contracts = gaming_event_monitor.get_monitored_contracts()
        logger.info(f"✓ Monitoring {len(monitored_contracts)} contracts")
        
        # Show some monitored contracts
        for chain, contracts in monitored_contracts.items():
            if contracts:
                logger.info(f"  - {chain}: {len(contracts)} contracts")
        
        return True
        
    except Exception as e:
        logger.error(f"Event monitoring test failed: {e}")
        return False


async def test_nft_tracker():
    """Test NFT tracking system"""
    logger.info("\n=== Testing NFT Tracker ===")
    
    try:
        # Test tracked collections
        tracked_collections = nft_tracker.get_tracked_collections()
        logger.info(f"✓ Tracking {len(tracked_collections)} NFT collections")
        
        # Show some tracked collections
        for collection in tracked_collections[:5]:
            logger.info(f"  - {collection}")
        
        # Test metadata cache
        metadata_cache_size = len(nft_tracker.metadata_cache)
        asset_cache_size = len(nft_tracker.asset_cache)
        
        logger.info(f"✓ Metadata cache: {metadata_cache_size} entries")
        logger.info(f"✓ Asset cache: {asset_cache_size} entries")
        
        return True
        
    except Exception as e:
        logger.error(f"NFT tracker test failed: {e}")
        return False


async def test_market_data():
    """Test market data integration"""
    logger.info("\n=== Testing Market Data Integration ===")
    
    try:
        # Test price cache stats
        cache_stats = gaming_market_data.get_price_cache_stats()
        logger.info(f"✓ Price cache: {cache_stats['total_cached']} entries, "
                   f"{cache_stats['fresh_entries']} fresh")
        
        # Test gaming tokens
        gaming_tokens = gaming_market_data.gaming_tokens
        logger.info(f"✓ Tracking {len(gaming_tokens)} gaming tokens")
        
        # Show some gaming tokens
        for token in gaming_tokens[:5]:
            logger.info(f"  - {token['symbol']} on {token['chain']}")
        
        # Test provider availability
        providers = gaming_market_data.providers
        logger.info(f"✓ Market data providers: {list(providers.keys())}")
        
        return True
        
    except Exception as e:
        logger.error(f"Market data test failed: {e}")
        return False


async def test_sync_manager():
    """Test blockchain synchronization manager"""
    logger.info("\n=== Testing Sync Manager ===")
    
    try:
        # Test sync states
        sync_status = blockchain_sync_manager.get_sync_status()
        
        logger.info(f"✓ Sync active: {sync_status['sync_active']}")
        logger.info(f"✓ Total chains: {sync_status['total_chains']}")
        logger.info(f"✓ Reorg events: {sync_status['reorg_events']}")
        
        # Show chain sync states
        for chain, state in sync_status['chains'].items():
            logger.info(f"  - {chain}: {state['sync_status']}, "
                       f"block {state['last_synced_block']}/{state['latest_block']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Sync manager test failed: {e}")
        return False


async def test_error_handling():
    """Test error handling and retry mechanisms"""
    logger.info("\n=== Testing Error Handling ===")
    
    try:
        # Test overall health
        overall_health = error_handler.get_overall_health()
        
        logger.info(f"✓ Total services: {overall_health['total_services']}")
        logger.info(f"✓ Healthy services: {overall_health['healthy_services']}")
        logger.info(f"✓ Degraded services: {overall_health['degraded_services']}")
        logger.info(f"✓ Unhealthy services: {overall_health['unhealthy_services']}")
        
        # Show service details
        for service_name, health in overall_health['services'].items():
            logger.info(f"  - {service_name}: {health['error_rate']:.2%} error rate, "
                       f"circuit {health['circuit_breaker']['state']}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error handling test failed: {e}")
        return False


async def test_blockchain_health():
    """Test blockchain health status"""
    logger.info("\n=== Testing Blockchain Health Status ===")
    
    try:
        health_status = await get_blockchain_health_status()
        
        logger.info(f"✓ Health check completed")
        logger.info(f"✓ Total services monitored: {health_status['total_services']}")
        
        # Show service health summary
        for service_name, health in health_status['services'].items():
            status = "🟢" if health['error_rate'] < 0.1 else "🟡" if health['error_rate'] < 0.5 else "🔴"
            logger.info(f"  {status} {service_name}: {health['total_operations']} ops, "
                       f"{health['error_rate']:.2%} error rate")
        
        return True
        
    except Exception as e:
        logger.error(f"Blockchain health test failed: {e}")
        return False


async def run_integration_test():
    """Run a simple integration test"""
    logger.info("\n=== Running Integration Test ===")
    
    try:
        # Test getting a token price (if API keys are configured)
        test_tokens = [
            ("ethereum", "******************************************", "MATIC"),  # Polygon token
            ("bsc", "******************************************", "USDC"),     # USDC on BSC
        ]
        
        for chain, contract_address, symbol in test_tokens:
            try:
                price = await gaming_market_data.get_token_price(chain, contract_address, symbol)
                if price:
                    logger.info(f"✓ {symbol} price: ${price.price_usd:.4f} "
                               f"({price.price_change_24h:+.2f}%)")
                else:
                    logger.info(f"⚠ Could not get price for {symbol}")
            except Exception as e:
                logger.info(f"⚠ Price fetch error for {symbol}: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Integration test failed: {e}")
        return False


async def main():
    """Run all Phase 3 tests"""
    logger.info("🚀 Starting Phase 3: Blockchain Integration Framework Tests")
    logger.info(f"Timestamp: {datetime.utcnow().isoformat()}")
    
    tests = [
        ("Multi-Chain Client", test_multi_chain_client),
        ("Enhanced RPC", test_enhanced_rpc),
        ("Gaming Contracts", test_gaming_contracts),
        ("Event Monitoring", test_event_monitoring),
        ("NFT Tracker", test_nft_tracker),
        ("Market Data", test_market_data),
        ("Sync Manager", test_sync_manager),
        ("Error Handling", test_error_handling),
        ("Blockchain Health", test_blockchain_health),
        ("Integration Test", run_integration_test),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            logger.info(f"\n{'='*60}")
            result = await test_func()
            results[test_name] = result
            
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("📊 TEST SUMMARY")
    logger.info(f"{'='*60}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 All Phase 3 tests passed! Blockchain integration is ready.")
    else:
        logger.warning(f"⚠️ {total-passed} tests failed. Review the logs above.")
    
    # Close any open sessions
    try:
        await gaming_market_data.close_all()
    except:
        pass


if __name__ == "__main__":
    asyncio.run(main())
