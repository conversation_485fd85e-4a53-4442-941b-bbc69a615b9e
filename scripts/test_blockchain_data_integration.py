#!/usr/bin/env python3
"""
Test script for blockchain data integration
"""
import asyncio
import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from blockchain.data_clients.manager import blockchain_data_manager
from blockchain.data_clients.flipside import FlipsideClient
from blockchain.data_clients.bitquery import BitQueryClient
from blockchain.data_clients.cryptorank import CryptoRankClient
from blockchain.data_clients.dextools import DexToolsClient
from config.settings import get_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

settings = get_settings()


async def test_individual_clients():
    """Test each client individually"""
    logger.info("🧪 Testing individual blockchain data clients...")
    
    # Test Flipside
    if settings.blockchain_data.flipside_api_key:
        logger.info("Testing Flipside client...")
        try:
            async with FlipsideClient() as flipside:
                connection_test = await flipside.test_connection()
                logger.info(f"Flipside connection: {'✅' if connection_test else '❌'}")
                
                if connection_test:
                    # Test gaming tokens data
                    tokens_data = await flipside.get_gaming_tokens_data(['AXS', 'SAND'])
                    logger.info(f"Flipside gaming tokens data: {len(tokens_data)} results")
                    
        except Exception as e:
            logger.error(f"Flipside test failed: {e}")
    else:
        logger.warning("Flipside API key not configured")
    
    # Test BitQuery
    if settings.blockchain_data.bitquery_api_key:
        logger.info("Testing BitQuery client...")
        try:
            async with BitQueryClient() as bitquery:
                connection_test = await bitquery.test_connection()
                logger.info(f"BitQuery connection: {'✅' if connection_test else '❌'}")
                
                if connection_test:
                    # Test gaming tokens data
                    tokens_data = await bitquery.get_gaming_tokens_data(['AXS', 'SAND'])
                    logger.info(f"BitQuery gaming tokens data: {len(tokens_data)} results")
                    
        except Exception as e:
            logger.error(f"BitQuery test failed: {e}")
    else:
        logger.warning("BitQuery API key not configured")
    
    # Test CryptoRank
    if settings.blockchain_data.cryptorank_api_key:
        logger.info("Testing CryptoRank client...")
        try:
            async with CryptoRankClient() as cryptorank:
                connection_test = await cryptorank.test_connection()
                logger.info(f"CryptoRank connection: {'✅' if connection_test else '❌'}")
                
                if connection_test:
                    # Test gaming tokens data
                    tokens_data = await cryptorank.get_gaming_tokens_data(['AXS', 'SAND'])
                    logger.info(f"CryptoRank gaming tokens data: {len(tokens_data)} results")
                    
        except Exception as e:
            logger.error(f"CryptoRank test failed: {e}")
    else:
        logger.warning("CryptoRank API key not configured")
    
    # Test DexTools
    if settings.blockchain_data.dextools_api_key:
        logger.info("Testing DexTools client...")
        try:
            async with DexToolsClient() as dextools:
                connection_test = await dextools.test_connection()
                logger.info(f"DexTools connection: {'✅' if connection_test else '❌'}")
                
                if connection_test:
                    # Test gaming tokens data
                    tokens_data = await dextools.get_gaming_tokens_data(['AXS', 'SAND'])
                    logger.info(f"DexTools gaming tokens data: {len(tokens_data)} results")
                    
        except Exception as e:
            logger.error(f"DexTools test failed: {e}")
    else:
        logger.warning("DexTools API key not configured")


async def test_manager():
    """Test the blockchain data manager"""
    logger.info("🔧 Testing blockchain data manager...")
    
    try:
        # Initialize clients
        await blockchain_data_manager.initialize_clients()
        logger.info(f"Initialized {len(blockchain_data_manager.clients)} clients")
        
        # Test connections
        connection_status = await blockchain_data_manager.test_all_connections()
        logger.info(f"Connection status: {connection_status}")
        
        # Test gaming tokens data
        logger.info("Testing gaming tokens data aggregation...")
        gaming_tokens = ['AXS', 'SAND', 'MANA', 'ENJ']
        tokens_data = await blockchain_data_manager.get_gaming_tokens_data(gaming_tokens)
        
        for source, data in tokens_data.items():
            logger.info(f"{source}: {len(data) if isinstance(data, list) else 'N/A'} token records")
        
        # Test NFT collection data
        logger.info("Testing NFT collection data...")
        axie_contract = settings.gaming.axie_infinity_contract
        if axie_contract:
            nft_data = await blockchain_data_manager.get_nft_collection_data(axie_contract)
            for source, data in nft_data.items():
                logger.info(f"{source} NFT data: {len(str(data))} chars")
        
        # Test protocol metrics
        logger.info("Testing protocol metrics...")
        protocol_data = await blockchain_data_manager.get_gaming_protocol_metrics('axie-infinity')
        for source, data in protocol_data.items():
            logger.info(f"{source} protocol data: {len(str(data))} chars")
        
        # Test comprehensive data
        logger.info("Testing comprehensive gaming data...")
        comprehensive_data = await blockchain_data_manager.get_comprehensive_gaming_data()
        logger.info(f"Comprehensive data keys: {list(comprehensive_data.keys())}")
        
        # Test client status
        status = await blockchain_data_manager.get_client_status()
        logger.info(f"Manager status: {status}")
        
        return True
        
    except Exception as e:
        logger.error(f"Manager test failed: {e}")
        return False


async def test_specific_features():
    """Test specific features of each client"""
    logger.info("🎯 Testing specific client features...")
    
    # Test Flipside specific features
    if 'flipside' in blockchain_data_manager.clients:
        logger.info("Testing Flipside specific features...")
        try:
            async with FlipsideClient() as flipside:
                # Test gaming DeFi metrics
                defi_metrics = await flipside.get_gaming_defi_metrics()
                logger.info(f"Flipside DeFi metrics: {len(str(defi_metrics))} chars")
                
                # Test P2E game activity
                game_contracts = [settings.gaming.axie_infinity_contract]
                if game_contracts[0]:
                    p2e_activity = await flipside.get_p2e_game_activity(game_contracts)
                    logger.info(f"Flipside P2E activity: {len(str(p2e_activity))} chars")
                    
        except Exception as e:
            logger.error(f"Flipside specific features test failed: {e}")
    
    # Test CryptoRank specific features
    if 'cryptorank' in blockchain_data_manager.clients:
        logger.info("Testing CryptoRank specific features...")
        try:
            async with CryptoRankClient() as cryptorank:
                # Test gaming categories
                gaming_categories = await cryptorank.get_gaming_categories_data()
                logger.info(f"CryptoRank gaming categories: {len(str(gaming_categories))} chars")
                
                # Test trending gaming tokens
                trending = await cryptorank.get_trending_gaming_tokens()
                logger.info(f"CryptoRank trending tokens: {len(str(trending))} chars")
                
        except Exception as e:
            logger.error(f"CryptoRank specific features test failed: {e}")
    
    # Test DexTools specific features
    if 'dextools' in blockchain_data_manager.clients:
        logger.info("Testing DexTools specific features...")
        try:
            async with DexToolsClient() as dextools:
                # Test hot pools
                hot_pools = await dextools.get_hot_pools(limit=10)
                logger.info(f"DexTools hot pools: {len(str(hot_pools))} chars")
                
                # Test trending tokens
                trending = await dextools.get_trending_tokens(limit=10)
                logger.info(f"DexTools trending tokens: {len(str(trending))} chars")
                
        except Exception as e:
            logger.error(f"DexTools specific features test failed: {e}")


async def main():
    """Main test function"""
    logger.info("🚀 Starting blockchain data integration tests...")
    logger.info(f"Timestamp: {datetime.utcnow().isoformat()}")
    
    # Check configuration
    logger.info("📋 Configuration check:")
    logger.info(f"Flipside API key configured: {'✅' if settings.blockchain_data.flipside_api_key else '❌'}")
    logger.info(f"BitQuery API key configured: {'✅' if settings.blockchain_data.bitquery_api_key else '❌'}")
    logger.info(f"CryptoRank API key configured: {'✅' if settings.blockchain_data.cryptorank_api_key else '❌'}")
    logger.info(f"DexTools API key configured: {'✅' if settings.blockchain_data.dextools_api_key else '❌'}")
    
    # Run tests
    try:
        # Test individual clients
        await test_individual_clients()
        
        # Test manager
        manager_success = await test_manager()
        
        # Test specific features
        await test_specific_features()
        
        logger.info("✅ All blockchain data integration tests completed!")
        
        if manager_success:
            logger.info("🎉 Blockchain data integration is working correctly!")
        else:
            logger.warning("⚠️ Some issues detected in blockchain data integration")
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
