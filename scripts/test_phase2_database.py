#!/usr/bin/env python3
"""
Comprehensive test script for Phase 2: Database & Data Models
Tests migrations, CRUD operations, validation, and performance
"""
import sys
import os
import time
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from models.base import SessionLocal, engine
from models.crud import source, article, gaming_project, nft_collection, blockchain_data
from models.schemas import (
    SourceCreate, ArticleCreate, GamingProjectCreate, 
    NFTCollectionCreate, BlockchainDataCreate,
    GamingCategory, BlockchainNetwork
)
from scripts.database_backup import DatabaseBackup
from sqlalchemy import text
import json


def test_database_connection():
    """Test database connection and basic functionality"""
    print("🔄 Testing database connection...")
    
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            assert result.fetchone()[0] == 1
        print("✅ Database connection successful")
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False


def test_migrations():
    """Test migration system"""
    print("🔄 Testing migration system...")
    
    try:
        # Check if alembic_version table exists
        with engine.connect() as conn:
            result = conn.execute(text(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='alembic_version'"
            ))
            if result.fetchone():
                print("✅ Migration system initialized")
                
                # Get current revision
                result = conn.execute(text("SELECT version_num FROM alembic_version"))
                version = result.fetchone()
                if version:
                    print(f"✅ Current migration version: {version[0]}")
                return True
            else:
                print("❌ Migration system not initialized")
                return False
    except Exception as e:
        print(f"❌ Migration test failed: {e}")
        return False


def test_pydantic_validation():
    """Test Pydantic schema validation"""
    print("🔄 Testing Pydantic validation...")
    
    try:
        # Test valid source creation
        valid_source = SourceCreate(
            name="Test Source",
            slug="test-source",
            url="https://example.com",
            source_type="Scraper",
            category="Gaming"
        )
        print("✅ Valid source schema validation passed")
        
        # Test invalid source creation
        try:
            invalid_source = SourceCreate(
                name="",  # Invalid: empty name
                slug="test",
                url="invalid-url",  # Invalid URL format
                source_type="InvalidType",  # Invalid enum
                category="Gaming"
            )
            print("❌ Invalid source validation should have failed")
            return False
        except Exception:
            print("✅ Invalid source schema validation correctly failed")
        
        # Test gaming project validation
        valid_project = GamingProjectCreate(
            name="Test Game",
            slug="test-game",
            category=GamingCategory.P2E,
            blockchain=BlockchainNetwork.ETHEREUM
        )
        print("✅ Gaming project schema validation passed")
        
        return True
    except Exception as e:
        print(f"❌ Pydantic validation test failed: {e}")
        return False


def test_crud_operations():
    """Test CRUD operations for all models"""
    print("🔄 Testing CRUD operations...")
    
    db = SessionLocal()
    try:
        # Test Source CRUD
        print("  Testing Source CRUD...")
        source_data = SourceCreate(
            name="Test Gaming News",
            slug="test-gaming-news",
            url="https://testgaming.com",
            source_type="Scraper",
            category="Gaming",
            scrape_frequency=3600,
            reliability_score=0.8
        )
        
        # Create
        created_source = source.create(db, source_data)
        assert created_source.id is not None
        assert created_source.name == "Test Gaming News"
        print("    ✅ Source creation successful")
        
        # Read
        retrieved_source = source.get(db, created_source.id)
        assert retrieved_source.slug == "test-gaming-news"
        print("    ✅ Source retrieval successful")
        
        # Update
        updated_source = source.update(db, retrieved_source, {"reliability_score": 0.9})
        assert updated_source.reliability_score == 0.9
        print("    ✅ Source update successful")
        
        # Test Gaming Project CRUD
        print("  Testing Gaming Project CRUD...")
        project_data = GamingProjectCreate(
            name="Test P2E Game",
            slug="test-p2e-game",
            category=GamingCategory.P2E,
            blockchain=BlockchainNetwork.POLYGON,
            token_symbol="TESTGAME",
            description="A test gaming project"
        )
        
        created_project = gaming_project.create(db, project_data)
        assert created_project.category == "P2E"
        assert created_project.blockchain == "polygon"
        print("    ✅ Gaming project creation successful")
        
        # Test Article CRUD
        print("  Testing Article CRUD...")
        article_data = ArticleCreate(
            title="Test Gaming Article",
            content="This is a test article about gaming",
            url="https://testgaming.com/article1",
            source_id=created_source.id,
            gaming_category=GamingCategory.P2E,
            relevance_score=0.8,
            sentiment_score=0.5
        )
        
        created_article = article.create(db, article_data)
        assert created_article.gaming_category == "P2E"
        assert created_article.source_id == created_source.id
        print("    ✅ Article creation successful")
        
        # Test NFT Collection CRUD
        print("  Testing NFT Collection CRUD...")
        nft_data = NFTCollectionCreate(
            name="Test NFT Collection",
            slug="test-nft-collection",
            contract_address="0x1234567890abcdef",
            blockchain=BlockchainNetwork.ETHEREUM,
            gaming_category="Characters",
            gaming_project_id=created_project.id
        )
        
        created_nft = nft_collection.create(db, nft_data)
        assert created_nft.blockchain == "ethereum"
        assert created_nft.gaming_project_id == created_project.id
        print("    ✅ NFT collection creation successful")
        
        # Test Blockchain Data CRUD
        print("  Testing Blockchain Data CRUD...")
        blockchain_data_obj = BlockchainDataCreate(
            blockchain=BlockchainNetwork.ETHEREUM,
            transaction_hash="0xabcdef1234567890",
            contract_address="0x1234567890abcdef",
            event_type="Transfer",
            token_symbol="ETH",
            token_amount=1.5,
            article_id=created_article.id
        )
        
        created_blockchain_data = blockchain_data.create(db, blockchain_data_obj)
        assert created_blockchain_data.blockchain == "ethereum"
        assert created_blockchain_data.article_id == created_article.id
        print("    ✅ Blockchain data creation successful")
        
        print("✅ All CRUD operations successful")
        return True
        
    except Exception as e:
        print(f"❌ CRUD operations test failed: {e}")
        return False
    finally:
        db.close()


def test_database_performance():
    """Test database performance with indexes"""
    print("🔄 Testing database performance...")
    
    db = SessionLocal()
    try:
        # Test query performance with indexes
        start_time = time.time()
        
        # Test article queries that should use indexes
        recent_articles = article.get_recent(db, hours=24, limit=10)
        category_articles = article.get_by_category(db, "P2E", limit=10)
        relevance_articles = article.get_by_relevance(db, min_score=0.5, limit=10)
        
        # Test gaming project queries
        active_projects = gaming_project.get_active(db)
        ethereum_projects = gaming_project.get_by_blockchain(db, "ethereum")
        
        # Test source queries
        active_sources = source.get_active(db)
        
        end_time = time.time()
        query_time = end_time - start_time
        
        print(f"✅ Performance test completed in {query_time:.3f} seconds")
        print(f"    Recent articles: {len(recent_articles)}")
        print(f"    Category articles: {len(category_articles)}")
        print(f"    High relevance articles: {len(relevance_articles)}")
        print(f"    Active projects: {len(active_projects)}")
        print(f"    Ethereum projects: {len(ethereum_projects)}")
        print(f"    Active sources: {len(active_sources)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False
    finally:
        db.close()


def test_backup_system():
    """Test database backup and recovery system"""
    print("🔄 Testing backup system...")
    
    try:
        backup_manager = DatabaseBackup()
        
        # Test backup creation
        backup_path = backup_manager.create_backup("test_backup")
        assert os.path.exists(backup_path)
        print("✅ Backup creation successful")
        
        # Test backup listing
        backups = backup_manager.list_backups()
        assert len(backups) > 0
        print(f"✅ Backup listing successful: {len(backups)} backups found")
        
        # Test database stats
        stats = backup_manager.get_database_stats()
        assert 'sources' in stats
        assert 'articles' in stats
        print("✅ Database statistics successful")
        print(f"    Sources: {stats['sources']}")
        print(f"    Articles: {stats['articles']}")
        print(f"    Gaming Projects: {stats['gaming_projects']}")
        print(f"    NFT Collections: {stats['nft_collections']}")
        print(f"    Blockchain Data: {stats['blockchain_data']}")
        
        # Test JSON export
        export_file = "test_export.json"
        backup_manager.export_data_json(export_file)
        assert os.path.exists(export_file)
        
        # Verify JSON content
        with open(export_file, 'r') as f:
            data = json.load(f)
        assert 'sources' in data
        assert 'articles' in data
        print("✅ JSON export successful")
        
        # Cleanup
        os.remove(export_file)
        os.remove(backup_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Backup system test failed: {e}")
        return False


def test_data_relationships():
    """Test database relationships and foreign keys"""
    print("🔄 Testing data relationships...")
    
    db = SessionLocal()
    try:
        # Get existing data
        test_source = source.get_by_slug(db, "test-gaming-news")
        test_project = gaming_project.get_by_slug(db, "test-p2e-game")
        
        if test_source and test_project:
            # Test article-source relationship
            source_articles = article.get_by_source(db, test_source.id, limit=5)
            print(f"✅ Article-Source relationship: {len(source_articles)} articles found")
            
            # Test NFT-project relationship
            project_nfts = nft_collection.get_multi(db, limit=10)
            project_nft = next((nft for nft in project_nfts if nft.gaming_project_id == test_project.id), None)
            if project_nft:
                print("✅ NFT-Project relationship working")
            
            # Test blockchain data relationships
            blockchain_records = blockchain_data.get_by_blockchain(db, "ethereum", limit=5)
            print(f"✅ Blockchain data queries: {len(blockchain_records)} records found")
            
            return True
        else:
            print("❌ Test data not found for relationship testing")
            return False
            
    except Exception as e:
        print(f"❌ Relationship test failed: {e}")
        return False
    finally:
        db.close()


def main():
    """Run all Phase 2 tests"""
    print("🚀 Starting Phase 2: Database & Data Models Tests")
    print("=" * 60)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Migration System", test_migrations),
        ("Pydantic Validation", test_pydantic_validation),
        ("CRUD Operations", test_crud_operations),
        ("Database Performance", test_database_performance),
        ("Backup System", test_backup_system),
        ("Data Relationships", test_data_relationships)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All Phase 2 tests passed! Database & Data Models implementation is complete.")
        return True
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
